<!-- Link: https://club.pokemon.com/us/pokemon-trainer-club/email-change-approval/f1f8d062f900a1b316810989ff178ec3 -->
<!-- Password: NE66$Sj%F -->
<!-- Status: 200 -->
<!-- CSRF: HTrRafij9ncXS0JbGl6JUQWahllPG2AiPNS9lorTjASBo35zC9pfm9TGjQuWrJMU -->

<!DOCTYPE html>
<html>
    <head>
        <noscript>
            <title>Pardon Our Interruption</title>
        </noscript>

        <meta name="viewport" content="width=1000">
        <meta name="robots" content="noindex, nofollow">
        <meta http-equiv="cache-control" content="no-cache, no-store, must-revalidate">
        <meta http-equiv="pragma" content="no-cache">
        <meta http-equiv="expires" content="0">

        <style>
            .container { max-width: 800px; margin: auto; font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif; color: #7a838c; }
            h1 { color: #2a2d30; font-weight: 500; }
            li { margin: 0 0 10px; }
            a { color: #428bca; }
            a:hover, a:focus { color: #2a6496; }
        </style>

        <script>
          var isSpa = new URLSearchParams(window.location.search).get('X-SPA') === '1' || window.isImpervaSpaSupport;
        </script>

        <!-- This head template should be placed before the following script tag that loads the challenge script -->
        <script>
          window.onProtectionInitialized = function(protection) {
            if (protection && protection.cookieIsSet && !protection.cookieIsSet()) {
              showBlockPage();
              return;
            }
            if (!isSpa) {
              window.location.reload(true);
            }
          };
          window.reeseSkipExpirationCheck = true;
        </script>

        <script>
          if (!isSpa) {
            var scriptElement = document.createElement('script');
            scriptElement.type = "text/javascript";
            scriptElement.src = "/trum-werest-rementy-harme-That-leauine-what-with/194546455211006560?s=EbfzArgG";
            scriptElement.async = true;
            scriptElement.defer = true;
            document.head.appendChild(scriptElement);
          }
        </script>
        
    </head>
    <body>

        

        <div class="container">
            <script>document.getElementsByClassName("container")[0].style.display = "none";</script>
            
            <h1>Pardon Our Interruption</h1>
<p>As you were browsing something about your browser made us think you were a bot. There are a few reasons this might happen:</p>
<ul>
<noscript><li>You've disabled JavaScript in your web browser.</li></noscript>
<li>You're a power user moving through this website with super-human speed.</li>
<li>You've disabled cookies in your web browser.</li>
<li>A third-party browser plugin, such as Ghostery or NoScript, is preventing JavaScript from running. Additional information is available in this <a title='Third party browser plugins that block javascript' href='http://ds.tl/help-third-party-plugins' target='_blank'>support article</a>.</li>
</ul>
<p>To regain access, please make sure that cookies and JavaScript are enabled before reloading the page.</p>


        </div>
	
        <div id="interstitial-inprogress" style="display: none">
          <style>
    #interstitial-inprogress {
      width:100%;
      height:100%;
      position:absolute;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      z-index:9999;
      background:white url("/_Incapsula_Resource?NWFURVBO=images/error_pages/bg.png") no-repeat center;
    }
    #interstitial-inprogress-box{
      font-size:32px;
      box-shadow:0 4px 14px 0 #0000001A,0 8px 24px 0 #00000021;
      font-family:Inter,Helvetica,Arial,sans-serif;
      position:absolute;
      left:50%;
      top:50%;
      transform:translate(-50%,-50%);
      background-color:white;
      text-align:center;
      width:auto;
      min-width:min(95%,640px);
      max-width:max-content;
      padding:16px;
    }
    #interstitial-inprogress-box h3{
      font-size:48px;
    }
  </style>
  <div id="interstitial-inprogress-box">
    <h3>Please stand by</h3>
    <p>We&apos;re getting everything ready for you. The page is loading, and you&apos;ll be on your way in just a few moments.</p>
    <p>Thanks for your patience!</p>
  </div>

        </div>

        <script>
          function showBlockPage() {
            document.title = "Pardon Our Interruption";
            document.getElementsByClassName("container")[0].style.display = "block";
          }

          if (isSpa) {
            showBlockPage();
          } else {
            window.interstitialTimeout = setTimeout(showBlockPage, 10000);
          }
        </script>
    </body>
</html>
