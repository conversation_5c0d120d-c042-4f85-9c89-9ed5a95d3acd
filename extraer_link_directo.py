#!/usr/bin/env python3
"""
Script para extraer el link directamente del correo en tiempo real
Sin usar JSON guardado, accede al buzón y extrae el link automáticamente
"""

import requests
import json
import re
from datetime import datetime
from bs4 import BeautifulSoup
from dujaw_api_final import DujawAPI

class ExtractorLinkDirecto(DujawAPI):
    def __init__(self):
        super().__init__()
        # Credenciales correctas
        self.email = "<EMAIL>"
        self.password = "unlockgs2024"
        self.mailbox_url = f"https://dujaw.com/mailbox/{self.email}"
        
        print(f"🔗 EXTRACTOR DE LINK DIRECTO")
        print(f"📧 Email: {self.email}")

    def obtener_correos_frescos(self):
        """Obtiene correos directamente del buzón"""
        print(f"\n📧 OBTENIENDO CORREOS FRESCOS")
        print("=" * 40)

        if not self.unlock_mailbox():
            print("❌ Error en unlock")
            return None

        # Acceder al mailbox y obtener HTML
        response = self.session.get(self.mailbox_url)
        if response.status_code != 200:
            print(f"❌ Error accediendo mailbox: {response.status_code}")
            return None

        # Buscar componentes Livewire
        pattern = r'wire:id="([^"]+)"\s+wire:initial-data="([^"]+)"'
        matches = re.findall(pattern, response.text)

        correos_data = []
        livewire_components = []

        for wire_id, wire_initial_data_encoded in matches:
            try:
                import html
                wire_initial_data = html.unescape(wire_initial_data_encoded)
                initial_data = json.loads(wire_initial_data)

                component_name = initial_data.get('fingerprint', {}).get('name', 'unknown')
                livewire_components.append({
                    'wire_id': wire_id,
                    'component_name': component_name,
                    'initial_data': initial_data
                })

                if component_name == 'frontend.app':
                    print(f"✅ Componente de correos encontrado: {component_name}")

                    # Obtener datos del servidor
                    if 'serverMemo' in initial_data and 'data' in initial_data['serverMemo']:
                        server_data = initial_data['serverMemo']['data']

                        if 'messages' in server_data:
                            messages = server_data['messages']
                            print(f"📨 Mensajes iniciales: {len(messages)}")

                            for message in messages:
                                correos_data.append(message)

                # También buscar en effects/html si está disponible
                if 'effects' in initial_data:
                    effects = initial_data['effects']
                    if 'html' in effects:
                        correos_data.append({
                            'source': 'effects_html',
                            'content': effects['html']
                        })

            except Exception as e:
                print(f"⚠️ Error procesando componente {wire_id}: {e}")
                continue

        # Si no hay mensajes, intentar hacer refresh con Livewire
        if not correos_data:
            print(f"📭 No hay mensajes iniciales, intentando refresh...")
            correos_refresh = self.hacer_refresh_livewire(livewire_components)
            if correos_refresh:
                correos_data.extend(correos_refresh)

        return correos_data

    def hacer_refresh_livewire(self, livewire_components):
        """Hace refresh usando Livewire para obtener mensajes"""
        print(f"🔄 Haciendo refresh con Livewire...")

        correos_data = []

        for component in livewire_components:
            component_name = component['component_name']

            if component_name == 'frontend.app':
                print(f"   🔄 Refresh en {component_name}...")

                initial_data = component['initial_data']
                fingerprint = initial_data.get('fingerprint', {})
                server_memo = initial_data.get('serverMemo', {})

                # Headers para Livewire
                headers = {
                    'X-Livewire': 'true',
                    'X-CSRF-TOKEN': self.csrf_token,
                    'Content-Type': 'application/json',
                    'Accept': 'text/html, application/xhtml+xml',
                    'X-Requested-With': 'XMLHttpRequest',
                    'Referer': self.mailbox_url
                }

                # Intentar fetchMessages
                livewire_request = {
                    'fingerprint': fingerprint,
                    'serverMemo': server_memo,
                    'updates': [
                        {
                            'type': 'fireEvent',
                            'payload': {
                                'id': component['wire_id'],
                                'event': 'fetchMessages',
                                'params': []
                            }
                        }
                    ]
                }

                try:
                    livewire_url = f"{self.base_url}/livewire/message/{component_name}"
                    response = self.session.post(livewire_url, json=livewire_request, headers=headers)

                    print(f"   📥 Respuesta fetchMessages: {response.status_code}")

                    if response.status_code == 200:
                        data = response.json()

                        # Buscar mensajes en la respuesta
                        if 'effects' in data and 'html' in data['effects']:
                            html_content = data['effects']['html']
                            correos_data.append({
                                'source': 'livewire_refresh',
                                'content': html_content
                            })
                            print(f"   ✅ HTML actualizado obtenido")

                        # Buscar en serverMemo actualizado
                        if 'serverMemo' in data and 'data' in data['serverMemo']:
                            server_data = data['serverMemo']['data']
                            if 'messages' in server_data:
                                messages = server_data['messages']
                                print(f"   📨 Mensajes después de refresh: {len(messages)}")
                                correos_data.extend(messages)

                except Exception as e:
                    print(f"   ❌ Error en refresh: {e}")

        return correos_data

    def extraer_links_pokemon(self, correos_data):
        """Extrae links de Pokémon de los datos de correos"""
        print(f"\n🔍 BUSCANDO LINKS DE POKÉMON")
        print("=" * 35)
        
        links_encontrados = []
        
        for i, correo in enumerate(correos_data):
            print(f"\n📧 Analizando correo/dato {i+1}...")
            
            # Obtener contenido HTML del correo
            html_content = ""
            
            if 'content' in correo:
                html_content = correo['content']
                print(f"   📄 Tipo: Contenido de mensaje")
                if 'sender_name' in correo:
                    print(f"   👤 De: {correo['sender_name']}")
                if 'subject' in correo:
                    print(f"   📝 Asunto: {correo['subject']}")
            elif 'source' in correo and correo['source'] == 'effects_html':
                html_content = correo['content']
                print(f"   📄 Tipo: HTML de effects")
            
            if not html_content:
                print(f"   ⚠️ Sin contenido HTML")
                continue
            
            # Buscar links en el HTML
            links = self.buscar_links_en_html(html_content)
            
            if links:
                print(f"   🔗 Links encontrados: {len(links)}")
                
                for j, link in enumerate(links, 1):
                    print(f"      {j}. {link[:80]}{'...' if len(link) > 80 else ''}")
                    
                    # Verificar si es link de Pokémon
                    if self.es_link_pokemon(link):
                        print(f"      🎉 ¡LINK DE POKÉMON DETECTADO!")
                        links_encontrados.append({
                            'link': link,
                            'correo_index': i,
                            'sender': correo.get('sender_name', 'N/A'),
                            'subject': correo.get('subject', 'N/A')
                        })
            else:
                print(f"   📭 Sin links encontrados")
        
        return links_encontrados

    def buscar_links_en_html(self, html_content):
        """Busca todos los links en contenido HTML"""
        links = []
        
        try:
            # Método 1: BeautifulSoup para links en tags <a>
            soup = BeautifulSoup(html_content, 'html.parser')
            
            for a_tag in soup.find_all('a', href=True):
                href = a_tag['href']
                if href.startswith('http'):
                    links.append(href)
            
            # Método 2: Regex para capturar URLs que puedan estar en texto
            url_patterns = [
                r'https://club\.pokemon\.com/[^\s<>"\']+',  # Específico para Pokémon
                r'https?://[^\s<>"\']+(?:[^\s<>"\'.,;!?])',  # URLs generales
                r'href="(https://[^"]+)"',  # URLs en atributos href
                r'href=\'(https://[^\']+)\'',  # URLs en atributos href con comillas simples
            ]
            
            for pattern in url_patterns:
                matches = re.findall(pattern, html_content)
                for match in matches:
                    # Si el match es una tupla (de grupos de captura), tomar el primer elemento
                    if isinstance(match, tuple):
                        match = match[0]
                    
                    if match not in links and match.startswith('http'):
                        links.append(match)
            
            # Método 3: Buscar específicamente el patrón de email-change-approval
            pokemon_pattern = r'https://club\.pokemon\.com/us/pokemon-trainer-club/email-change-approval/[a-f0-9]+'
            pokemon_matches = re.findall(pokemon_pattern, html_content)
            
            for match in pokemon_matches:
                if match not in links:
                    links.append(match)
            
        except Exception as e:
            print(f"⚠️ Error buscando links: {e}")
        
        return links

    def es_link_pokemon(self, link):
        """Verifica si un link es de confirmación de Pokémon"""
        return (
            'club.pokemon.com' in link and 
            'email-change-approval' in link and
            link.startswith('https://')
        )

    def validar_link_completo(self, link):
        """Valida que el link esté completo"""
        print(f"\n🔍 VALIDANDO LINK")
        print("=" * 20)
        print(f"🔗 Link: {link}")
        
        # Verificar estructura básica
        if not link.startswith('https://club.pokemon.com/us/pokemon-trainer-club/email-change-approval/'):
            print(f"❌ Estructura de URL incorrecta")
            return False
        
        # Extraer token
        token = link.split('email-change-approval/')[-1]
        print(f"🔑 Token: {token}")
        print(f"📏 Longitud: {len(token)} caracteres")
        
        # Verificar formato hexadecimal
        if re.match(r'^[a-f0-9]+$', token):
            print(f"✅ Formato hexadecimal válido")
        else:
            print(f"❌ Formato no hexadecimal")
            return False
        
        # Verificar longitud mínima
        if len(token) >= 32:
            print(f"✅ Longitud adecuada")
        else:
            print(f"⚠️ Token podría ser demasiado corto")
        
        print(f"✅ Link validado como completo")
        return True

    def probar_link_extraido(self, link):
        """Prueba el link extraído haciendo una petición"""
        print(f"\n🧪 PROBANDO LINK EXTRAÍDO")
        print("=" * 30)
        
        try:
            # Hacer petición HEAD para verificar que el link responde
            response = self.session.head(link, allow_redirects=True)
            print(f"📡 Status Code: {response.status_code}")
            print(f"🌐 URL final: {response.url}")
            
            if response.status_code == 200:
                print(f"✅ Link responde correctamente")
                return True
            else:
                print(f"⚠️ Link responde con código: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error probando link: {e}")
            return False

    def proceso_completo(self):
        """Proceso completo de extracción"""
        print(f"\n🚀 PROCESO COMPLETO DE EXTRACCIÓN")
        print("=" * 50)
        
        # 1. Obtener correos frescos
        correos_data = self.obtener_correos_frescos()
        
        if not correos_data:
            print("❌ No se pudieron obtener correos")
            return None
        
        # 2. Extraer links de Pokémon
        links_pokemon = self.extraer_links_pokemon(correos_data)
        
        if not links_pokemon:
            print("❌ No se encontraron links de Pokémon")
            return None
        
        print(f"\n🎉 ¡LINKS DE POKÉMON ENCONTRADOS: {len(links_pokemon)}!")
        
        # 3. Procesar cada link encontrado
        for i, link_data in enumerate(links_pokemon, 1):
            print(f"\n{'='*60}")
            print(f"🔗 LINK {i}")
            print(f"{'='*60}")
            
            link = link_data['link']
            print(f"📧 De: {link_data['sender']}")
            print(f"📝 Asunto: {link_data['subject']}")
            print(f"🔗 Link: {link}")
            
            # Validar link
            if self.validar_link_completo(link):
                # Probar link
                if self.probar_link_extraido(link):
                    # Guardar link válido
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    filename = f"pokemon_link_valido_{timestamp}.txt"
                    
                    with open(filename, 'w') as f:
                        f.write(f"Link de Pokémon extraído automáticamente\n")
                        f.write(f"Fecha: {datetime.now().isoformat()}\n")
                        f.write(f"De: {link_data['sender']}\n")
                        f.write(f"Asunto: {link_data['subject']}\n")
                        f.write(f"Link: {link}\n")
                    
                    print(f"\n💾 Link guardado: {filename}")
                    print(f"🎉 ¡EXTRACCIÓN EXITOSA!")
                    
                    return link
        
        print(f"\n❌ Ningún link fue válido")
        return None

def main():
    """Función principal"""
    print("🔗 EXTRACTOR DE LINK DIRECTO DE POKÉMON")
    print("=" * 50)
    
    try:
        extractor = ExtractorLinkDirecto()
        link = extractor.proceso_completo()
        
        if link:
            print(f"\n✅ PROCESO COMPLETADO EXITOSAMENTE")
            print(f"🔗 Link final: {link}")
        else:
            print(f"\n❌ No se pudo extraer link válido")
            
    except Exception as e:
        print(f"❌ Error general: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        print(f"\n🏁 Proceso terminado")

if __name__ == "__main__":
    main()
