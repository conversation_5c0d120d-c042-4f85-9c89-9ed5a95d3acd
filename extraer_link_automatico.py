#!/usr/bin/env python3
"""
Script para extraer automáticamente el link de confirmación del correo
"""

import json
import re
from bs4 import BeautifulSoup

def extraer_link_pokemon():
    """Extrae el link de Pokémon del JSON del correo"""
    print("🔗 EXTRACTOR AUTOMÁTICO DE LINK")
    print("=" * 40)
    
    # Cargar el JSON del correo
    json_file = "correos_livewire_20250624_040533.json"
    
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✅ JSON cargado: {json_file}")
        
        # Buscar en los correos encontrados
        correos = data.get('correos_encontrados', [])
        
        for i, correo in enumerate(correos):
            print(f"\n📧 Analizando correo {i+1}...")
            
            # Buscar en los datos del correo
            if 'data' in correo:
                correo_data = correo['data']
                
                # Buscar en serverMemo
                if 'serverMemo' in correo_data and 'data' in correo_data['serverMemo']:
                    server_data = correo_data['serverMemo']['data']
                    
                    if 'messages' in server_data:
                        messages = server_data['messages']
                        
                        for j, message in enumerate(messages):
                            print(f"   📨 Mensaje {j+1}:")
                            print(f"      De: {message.get('sender_name', 'N/A')}")
                            print(f"      Asunto: {message.get('subject', 'N/A')}")
                            
                            # Extraer links del contenido
                            content = message.get('content', '')
                            if content:
                                links = extraer_links_del_html(content)
                                
                                if links:
                                    print(f"      🔗 Links encontrados: {len(links)}")
                                    
                                    for k, link in enumerate(links, 1):
                                        print(f"         {k}. {link}")
                                        
                                        # Verificar si es el link de Pokémon
                                        if 'club.pokemon.com' in link and 'email-change-approval' in link:
                                            print(f"\n🎉 ¡LINK DE POKÉMON ENCONTRADO!")
                                            print(f"🔗 Link completo: {link}")
                                            
                                            # Verificar que esté completo
                                            if verificar_link_completo(link):
                                                print(f"✅ Link verificado como completo")
                                                return link
                                            else:
                                                print(f"⚠️ Link parece incompleto")
                
                # Buscar en effects/html
                if 'effects' in correo_data and 'html' in correo_data['effects']:
                    html_content = correo_data['effects']['html']
                    links = extraer_links_del_html(html_content)
                    
                    for link in links:
                        if 'club.pokemon.com' in link and 'email-change-approval' in link:
                            print(f"\n🎉 ¡LINK DE POKÉMON ENCONTRADO EN HTML!")
                            print(f"🔗 Link completo: {link}")
                            
                            if verificar_link_completo(link):
                                print(f"✅ Link verificado como completo")
                                return link
        
        print(f"\n❌ No se encontró link de Pokémon")
        return None
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def extraer_links_del_html(html_content):
    """Extrae todos los links de un contenido HTML"""
    links = []
    
    try:
        # Método 1: Usar BeautifulSoup
        soup = BeautifulSoup(html_content, 'html.parser')
        
        for a_tag in soup.find_all('a', href=True):
            href = a_tag['href']
            if href.startswith('http'):
                links.append(href)
        
        # Método 2: Usar regex como backup
        url_pattern = r'https?://[^\s<>"\']+(?:[^\s<>"\'.,;!?])'
        regex_links = re.findall(url_pattern, html_content)
        
        for link in regex_links:
            if link not in links:
                links.append(link)
        
        # Método 3: Buscar específicamente links de Pokémon
        pokemon_pattern = r'https://club\.pokemon\.com/[^\s<>"\']+email-change-approval/[a-f0-9]+'
        pokemon_links = re.findall(pokemon_pattern, html_content)
        
        for link in pokemon_links:
            if link not in links:
                links.append(link)
        
    except Exception as e:
        print(f"⚠️ Error extrayendo links: {e}")
    
    return links

def verificar_link_completo(link):
    """Verifica si el link de Pokémon está completo"""
    # Un link completo de email-change-approval debe tener:
    # 1. El dominio correcto
    # 2. La ruta correcta
    # 3. Un token largo (generalmente 64+ caracteres hexadecimales)
    
    if not link.startswith('https://club.pokemon.com/us/pokemon-trainer-club/email-change-approval/'):
        return False
    
    # Extraer el token (parte después de email-change-approval/)
    token_part = link.split('email-change-approval/')[-1]
    
    # Verificar que el token sea suficientemente largo y hexadecimal
    if len(token_part) >= 32 and re.match(r'^[a-f0-9]+$', token_part):
        return True
    
    return False

def probar_link_extraido(link):
    """Prueba el link extraído"""
    if not link:
        print("❌ No hay link para probar")
        return
    
    print(f"\n🧪 PROBANDO LINK EXTRAÍDO")
    print("=" * 30)
    print(f"🔗 Link: {link}")
    
    # Análisis del link
    parts = link.split('/')
    print(f"📊 Análisis:")
    print(f"   Dominio: {'/'.join(parts[:3])}")
    print(f"   Ruta: {'/'.join(parts[3:-1])}")
    print(f"   Token: {parts[-1]}")
    print(f"   Longitud token: {len(parts[-1])} caracteres")
    
    # Verificar formato
    token = parts[-1]
    if re.match(r'^[a-f0-9]+$', token):
        print(f"   ✅ Token formato hexadecimal válido")
    else:
        print(f"   ❌ Token formato inválido")
    
    if len(token) >= 32:
        print(f"   ✅ Token longitud adecuada")
    else:
        print(f"   ⚠️ Token podría ser demasiado corto")

def main():
    """Función principal"""
    print("🔗 EXTRACTOR AUTOMÁTICO DE LINK DE POKÉMON")
    print("=" * 50)
    
    # Extraer el link
    link = extraer_link_pokemon()
    
    if link:
        # Probar el link
        probar_link_extraido(link)
        
        # Guardar el link
        with open('pokemon_link_extraido.txt', 'w') as f:
            f.write(link)
        
        print(f"\n💾 Link guardado en: pokemon_link_extraido.txt")
        print(f"\n🎉 ¡EXTRACCIÓN EXITOSA!")
        print(f"🔗 Link completo: {link}")
        
    else:
        print(f"\n❌ No se pudo extraer el link")

if __name__ == "__main__":
    main()
