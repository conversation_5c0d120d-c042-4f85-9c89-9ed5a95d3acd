#!/usr/bin/env python3
"""
Test script for new Dujaw credentials
Tests both the basic API and enhanced API with the new credentials
"""

import json
import time
from datetime import datetime

def test_basic_api():
    """Test the basic Dujaw API"""
    print("🧪 TESTING BASIC DUJAW API")
    print("=" * 40)
    
    try:
        from dujaw_api_final import DujawAPI
        
        # Create API instance
        api = DujawAPI()
        
        print(f"📧 Email: {api.mailbox_url.split('/')[-1]}")
        print(f"🔐 Password: {api.password}")
        
        # Test unlock
        print("\n1. Testing unlock...")
        if api.unlock_mailbox():
            print("✅ Unlock successful!")
            
            # Test mailbox access
            print("\n2. Testing mailbox access...")
            mailbox_info = api.access_mailbox()
            
            if mailbox_info:
                print("✅ Mailbox access successful!")
                print(f"📧 Messages found: {len(mailbox_info.get('messages', []))}")
                print(f"🔧 Actions available: {len(mailbox_info.get('actions', []))}")
                
                # Test status
                print("\n3. Testing status...")
                status = api.get_mailbox_status()
                print(f"📊 Status: {status['status']}")
                print(f"🔐 Authenticated: {status['authenticated']}")
                print(f"📧 Email: {status['email']}")
                
                # Show some messages if available
                messages = mailbox_info.get('messages', [])
                if messages:
                    print(f"\n📬 First few messages:")
                    for i, msg in enumerate(messages[:3]):
                        print(f"   {i+1}. {msg.get('content', 'No content')[:100]}...")
                else:
                    print("\n📭 No messages in mailbox")
                
                # Show available actions
                actions = mailbox_info.get('actions', [])
                if actions:
                    print(f"\n🔧 Available actions:")
                    for i, action in enumerate(actions[:5]):
                        print(f"   {i+1}. {action.get('text', 'No text')} ({action.get('type', 'unknown')})")
                
            else:
                print("❌ Failed to access mailbox")
                return False
            
            # Test logout
            print("\n4. Testing logout...")
            if api.logout():
                print("✅ Logout successful!")
            else:
                print("⚠️ Logout failed")
                
        else:
            print("❌ Unlock failed!")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error in basic API test: {e}")
        return False

def test_enhanced_api():
    """Test the enhanced Dujaw API"""
    print("\n\n🚀 TESTING ENHANCED DUJAW API")
    print("=" * 40)
    
    try:
        from dujaw_api_enhanced import EnhancedDujawAPI
        
        # Create enhanced API instance
        api = EnhancedDujawAPI()
        
        print(f"📧 Email: {api.mailbox_url.split('/')[-1]}")
        print(f"🔐 Password: {api.password}")
        
        # Test unlock
        print("\n1. Testing enhanced unlock...")
        if api.unlock_mailbox():
            print("✅ Enhanced unlock successful!")
            
            # Test message retrieval
            print("\n2. Testing message retrieval...")
            messages = api.get_messages(apply_filters=False)
            print(f"📧 Retrieved {len(messages)} messages")
            
            # Test statistics
            print("\n3. Testing statistics...")
            stats = api.get_statistics()
            print(f"📊 Statistics:")
            print(f"   Total messages: {stats['total_messages']}")
            print(f"   Unread messages: {stats['unread_messages']}")
            print(f"   Priority breakdown: {stats['priority_breakdown']}")
            print(f"   Monitoring active: {stats['monitoring_active']}")
            print(f"   Filters active: {stats['filters_active']}")
            
            # Show message details if available
            if messages:
                print(f"\n📬 Message details:")
                for i, msg in enumerate(messages[:3]):
                    print(f"   Message {i+1}:")
                    print(f"     ID: {msg.id}")
                    print(f"     Subject: {msg.subject}")
                    print(f"     Sender: {msg.sender}")
                    print(f"     Priority: {msg.priority.value}")
                    print(f"     Content: {msg.content[:100]}...")
                    print()
            
        else:
            print("❌ Enhanced unlock failed!")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error in enhanced API test: {e}")
        return False

def test_web_api():
    """Test the web API endpoints"""
    print("\n\n🌐 TESTING WEB API ENDPOINTS")
    print("=" * 40)
    
    try:
        import requests
        
        base_url = "http://localhost:8000"
        
        # Test status endpoint
        print("1. Testing status endpoint...")
        response = requests.get(f"{base_url}/api/status")
        if response.status_code == 200:
            print("✅ Status endpoint working!")
            status = response.json()
            print(f"   Status: {status.get('status', 'unknown')}")
            print(f"   Authenticated: {status.get('authenticated', False)}")
        else:
            print(f"❌ Status endpoint failed: {response.status_code}")
            return False
        
        # Test unlock endpoint
        print("\n2. Testing unlock endpoint...")
        unlock_data = {"password": "EMVaB#6G3"}
        response = requests.post(f"{base_url}/api/unlock", json=unlock_data)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ Web unlock successful!")
                print(f"   Message: {result.get('message')}")
            else:
                print(f"❌ Web unlock failed: {result.get('message')}")
                return False
        else:
            print(f"❌ Unlock endpoint failed: {response.status_code}")
            return False
        
        # Test mailbox endpoint
        print("\n3. Testing mailbox endpoint...")
        response = requests.get(f"{base_url}/api/mailbox")
        if response.status_code == 200:
            mailbox_data = response.json()
            print("✅ Mailbox endpoint working!")
            print(f"   Messages: {len(mailbox_data.get('messages', []))}")
            print(f"   Actions: {len(mailbox_data.get('actions', []))}")
        else:
            print(f"❌ Mailbox endpoint failed: {response.status_code}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error in web API test: {e}")
        return False

def main():
    """Main test function"""
    print("🎯 DUJAW CREDENTIALS TEST SUITE")
    print("New credentials: <EMAIL> / EMVaB#6G3")
    print("=" * 60)
    
    results = {
        'basic_api': False,
        'enhanced_api': False,
        'web_api': False
    }
    
    # Test basic API
    results['basic_api'] = test_basic_api()
    
    # Test enhanced API
    results['enhanced_api'] = test_enhanced_api()
    
    # Test web API (if server is running)
    try:
        import requests
        requests.get("http://localhost:8000/api/status", timeout=5)
        results['web_api'] = test_web_api()
    except:
        print("\n\n🌐 WEB API TEST SKIPPED")
        print("=" * 40)
        print("⚠️ Web server not running at localhost:8000")
        print("   Start with: uvicorn app:app --host 0.0.0.0 --port 8000")
    
    # Summary
    print("\n\n📋 TEST RESULTS SUMMARY")
    print("=" * 30)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
    
    total_tests = len([r for r in results.values() if r is not False])
    passed_tests = sum(results.values())
    
    print(f"\nOverall: {passed_tests}/{total_tests} tests passed")
    
    if all(r for r in results.values() if r is not False):
        print("\n🎉 ALL TESTS PASSED! New credentials are working perfectly!")
    else:
        print("\n⚠️ Some tests failed. Check the output above for details.")
    
    print("\n💡 Next steps:")
    print("   - Use the web interface at http://localhost:8000")
    print("   - Try the enhanced API features")
    print("   - Set up notifications and monitoring")

if __name__ == "__main__":
    main()
