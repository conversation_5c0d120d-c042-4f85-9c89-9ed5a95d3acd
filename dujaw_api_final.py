#!/usr/bin/env python3
"""
API final para automatizar dujaw.com
Basado en el análisis completo del flujo web capturado
"""

import requests
import json
import time
from datetime import datetime
from bs4 import BeautifulSoup
from typing import Dict, Any, Optional


class DujawAPI:
    def __init__(self, email=None, password=None):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })

        # Default credentials (can be overridden)
        self.email = email or "<EMAIL>"
        self.password = password or "EMVaB#6G3"

        self.base_url = "https://dujaw.com"
        self.mailbox_url = f"https://dujaw.com/mailbox/{self.email}"
        self.unlock_url = "https://dujaw.com/unlock"
        self.csrf_token = None
        self.is_authenticated = False
        
    def get_csrf_token(self) -> Optional[str]:
        """Obtiene el token CSRF de la página de unlock"""
        try:
            print("🔑 Obteniendo token CSRF...")
            response = self.session.get(self.mailbox_url)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')

                # Buscar token CSRF de varias formas
                csrf_input = soup.find('input', {'name': '_token'})
                if not csrf_input:
                    csrf_input = soup.find('input', {'type': 'hidden', 'name': '_token'})
                if not csrf_input:
                    # Buscar en meta tags
                    csrf_meta = soup.find('meta', {'name': 'csrf-token'})
                    if csrf_meta:
                        token = csrf_meta.get('content')
                        print(f"✅ Token CSRF obtenido (meta): {token[:20]}...")
                        return token

                if csrf_input:
                    token = csrf_input.get('value')
                    print(f"✅ Token CSRF obtenido: {token[:20]}...")
                    return token
                else:
                    print("❌ No se encontró token CSRF")
                    print("🔍 Contenido de la página (primeros 500 chars):")
                    print(response.text[:500])
                    return None
            else:
                print(f"❌ Error obteniendo página: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Error obteniendo token CSRF: {e}")
            return None
            
    def unlock_mailbox(self) -> bool:
        """Realiza el unlock del mailbox"""
        try:
            print("🔓 Realizando unlock del mailbox...")
            
            # Obtener token CSRF
            self.csrf_token = self.get_csrf_token()
            if not self.csrf_token:
                return False
                
            # Preparar datos del formulario
            form_data = {
                '_token': self.csrf_token,
                'password': self.password
            }
            
            # Enviar POST al endpoint de unlock
            response = self.session.post(self.unlock_url, data=form_data)
            
            if response.status_code in [200, 302]:
                print("✅ Unlock exitoso")
                self.is_authenticated = True
                
                # Si hay redirect, seguirlo
                if response.status_code == 302 and 'location' in response.headers:
                    redirect_url = response.headers['location']
                    print(f"🔄 Siguiendo redirect a: {redirect_url}")
                    self.session.get(redirect_url)
                    
                return True
            else:
                print(f"❌ Error en unlock: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error en unlock: {e}")
            return False
            
    def access_mailbox(self) -> Optional[Dict[str, Any]]:
        """Accede al mailbox después del unlock"""
        try:
            if not self.is_authenticated:
                print("⚠️ No autenticado, realizando unlock primero...")
                if not self.unlock_mailbox():
                    return None
                    
            print("📧 Accediendo al mailbox...")
            response = self.session.get(self.mailbox_url)
            
            if response.status_code == 200:
                print("✅ Mailbox accedido correctamente")
                
                # Parsear contenido del mailbox
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Extraer información del mailbox
                mailbox_info = self.parse_mailbox_content(soup)
                return mailbox_info
            else:
                print(f"❌ Error accediendo mailbox: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Error accediendo mailbox: {e}")
            return None
            
    def parse_mailbox_content(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Parsea el contenido del mailbox"""
        try:
            mailbox_info = {
                'email_address': '<EMAIL>',
                'messages': [],
                'actions': [],
                'timestamp': datetime.now().isoformat()
            }
            
            # Buscar mensajes de email
            # (Esto dependerá de la estructura HTML específica del sitio)
            message_elements = soup.find_all(['div', 'tr', 'li'], class_=lambda x: x and any(
                keyword in x.lower() for keyword in ['message', 'mail', 'email', 'inbox']
            ))
            
            for element in message_elements:
                text = element.get_text(strip=True)
                if text and len(text) > 10:  # Filtrar elementos vacíos
                    mailbox_info['messages'].append({
                        'content': text[:200],  # Primeros 200 caracteres
                        'html': str(element)[:500]  # HTML limitado
                    })
                    
            # Buscar acciones disponibles (botones, enlaces)
            action_elements = soup.find_all(['a', 'button'], href=True)
            action_elements.extend(soup.find_all('button'))
            
            for element in action_elements:
                text = element.get_text(strip=True)
                href = element.get('href', '')
                onclick = element.get('onclick', '')
                
                if text:
                    mailbox_info['actions'].append({
                        'text': text,
                        'href': href,
                        'onclick': onclick,
                        'type': element.name
                    })
                    
            print(f"📊 Mailbox parseado: {len(mailbox_info['messages'])} mensajes, {len(mailbox_info['actions'])} acciones")
            return mailbox_info
            
        except Exception as e:
            print(f"❌ Error parseando mailbox: {e}")
            return {'error': str(e)}
            
    def refresh_mailbox(self) -> Optional[Dict[str, Any]]:
        """Refresca el mailbox para obtener nuevos mensajes"""
        print("🔄 Refrescando mailbox...")
        return self.access_mailbox()
        
    def get_message_details(self, message_id: str = None) -> Optional[Dict[str, Any]]:
        """Obtiene detalles de un mensaje específico"""
        try:
            # Esta función dependería de la estructura específica del sitio
            # Por ahora, devolvemos la información general del mailbox
            print(f"📧 Obteniendo detalles del mensaje: {message_id or 'último'}")
            return self.access_mailbox()
            
        except Exception as e:
            print(f"❌ Error obteniendo detalles del mensaje: {e}")
            return None
            
    def delete_message(self, message_id: str) -> bool:
        """Elimina un mensaje específico"""
        try:
            print(f"🗑️ Eliminando mensaje: {message_id}")
            # Implementar según la API específica del sitio
            # Por ahora, simulamos la acción
            print("⚠️ Función de eliminación no implementada - requiere análisis adicional")
            return False
            
        except Exception as e:
            print(f"❌ Error eliminando mensaje: {e}")
            return False
            
    def compose_message(self, to: str, subject: str, body: str) -> bool:
        """Compone y envía un nuevo mensaje"""
        try:
            print(f"✉️ Componiendo mensaje para: {to}")
            # Implementar según la API específica del sitio
            print("⚠️ Función de composición no implementada - requiere análisis adicional")
            return False
            
        except Exception as e:
            print(f"❌ Error componiendo mensaje: {e}")
            return False
            
    def get_mailbox_status(self) -> Dict[str, Any]:
        """Obtiene el estado general del mailbox"""
        try:
            mailbox_info = self.access_mailbox()
            if mailbox_info:
                return {
                    'status': 'active',
                    'authenticated': self.is_authenticated,
                    'email': '<EMAIL>',
                    'message_count': len(mailbox_info.get('messages', [])),
                    'last_check': datetime.now().isoformat(),
                    'available_actions': len(mailbox_info.get('actions', []))
                }
            else:
                return {
                    'status': 'error',
                    'authenticated': self.is_authenticated,
                    'last_check': datetime.now().isoformat()
                }
                
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e),
                'last_check': datetime.now().isoformat()
            }
            
    def logout(self) -> bool:
        """Cierra la sesión"""
        try:
            print("🚪 Cerrando sesión...")
            self.session.cookies.clear()
            self.is_authenticated = False
            self.csrf_token = None
            print("✅ Sesión cerrada")
            return True
            
        except Exception as e:
            print(f"❌ Error cerrando sesión: {e}")
            return False


def main():
    """Función principal de demostración"""
    print("🚀 DUJAW API - AUTOMATIZACIÓN DE MAILBOX")
    print("=" * 50)
    
    # Crear instancia de la API
    api = DujawAPI()
    
    try:
        # Realizar unlock
        print("\n1. Realizando unlock...")
        if api.unlock_mailbox():
            print("✅ Unlock exitoso")
            
            # Acceder al mailbox
            print("\n2. Accediendo al mailbox...")
            mailbox_info = api.access_mailbox()
            
            if mailbox_info:
                print("✅ Mailbox accedido")
                print(f"📧 Mensajes encontrados: {len(mailbox_info.get('messages', []))}")
                print(f"🔧 Acciones disponibles: {len(mailbox_info.get('actions', []))}")
                
                # Mostrar algunas acciones disponibles
                actions = mailbox_info.get('actions', [])[:5]  # Primeras 5
                if actions:
                    print("\n🔧 Acciones disponibles:")
                    for action in actions:
                        print(f"   - {action['text']} ({action['type']})")
                        
                # Obtener estado del mailbox
                print("\n3. Obteniendo estado del mailbox...")
                status = api.get_mailbox_status()
                print(f"📊 Estado: {json.dumps(status, indent=2)}")
                
                # Refrescar mailbox
                print("\n4. Refrescando mailbox...")
                refreshed_info = api.refresh_mailbox()
                if refreshed_info:
                    print("✅ Mailbox refrescado")
                    
            else:
                print("❌ Error accediendo al mailbox")
                
        else:
            print("❌ Error en unlock")
            
    except Exception as e:
        print(f"❌ Error general: {e}")
        
    finally:
        # Cerrar sesión
        api.logout()
        
    print("\n🏁 Demostración completada")


if __name__ == "__main__":
    main()
