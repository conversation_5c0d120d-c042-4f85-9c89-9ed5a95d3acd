#!/usr/bin/env python3
"""
Script para confirmar directamente los links específicos encontrados
"""

import requests
import time
from datetime import datetime
from bs4 import BeautifulSoup

def confirmar_link_pokemon(link, password):
    """Confirma un link específico de Pokémon"""
    print(f"🔐 Confirmando: {link}")
    print(f"🔑 Password: {password}")
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9'
    })
    
    try:
        # 1. Acceder a la página
        print(f"   📥 Accediendo...")
        response = session.get(link)
        
        if response.status_code != 200:
            print(f"   ❌ Error {response.status_code}")
            return False
        
        # 2. Parsear formulario
        soup = BeautifulSoup(response.text, 'html.parser')
        form = None
        
        for f in soup.find_all('form'):
            if f.find('input', {'type': 'password'}):
                form = f
                break
        
        if not form:
            print(f"   ❌ No se encontró formulario")
            return False
        
        # 3. Obtener token CSRF fresco
        csrf_token = None
        csrf_input = form.find('input', {'name': 'csrfmiddlewaretoken'})
        if csrf_input:
            csrf_token = csrf_input.get('value')
            print(f"   🔒 Token CSRF: {csrf_token[:20]}...")
        
        # 4. Preparar datos
        form_data = {}
        
        for input_elem in form.find_all('input'):
            name = input_elem.get('name')
            value = input_elem.get('value', '')
            input_type = input_elem.get('type', 'text')
            
            if not name:
                continue
            
            if input_type == 'password':
                form_data[name] = password
                print(f"   🔑 Password configurado: {name}")
            elif name == 'csrfmiddlewaretoken':
                form_data[name] = csrf_token if csrf_token else value
                print(f"   🔒 CSRF: {name}")
            else:
                form_data[name] = value
                if input_type in ['email', 'text'] and value:
                    print(f"   📧 {name}: {value}")
                elif input_type == 'submit' and value:
                    print(f"   🔘 {name}: {value}")
        
        # 5. Configurar headers adicionales
        session.headers.update({
            'Referer': link,
            'Origin': 'https://club.pokemon.com',
            'Content-Type': 'application/x-www-form-urlencoded'
        })
        
        if csrf_token:
            session.headers['X-CSRFToken'] = csrf_token
        
        # 6. Enviar confirmación
        print(f"   🚀 Enviando confirmación...")
        time.sleep(1)  # Pausa para evitar rate limiting
        
        confirm_response = session.post(link, data=form_data, allow_redirects=True)
        
        print(f"   📥 Status: {confirm_response.status_code}")
        print(f"   🌐 URL final: {confirm_response.url}")
        
        # 7. Guardar respuesta
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"confirmacion_directa_{timestamp}.html"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"<!-- Link: {link} -->\n")
            f.write(f"<!-- Password: {password} -->\n")
            f.write(f"<!-- Status: {confirm_response.status_code} -->\n")
            f.write(f"<!-- CSRF: {csrf_token} -->\n\n")
            f.write(confirm_response.text)
        
        print(f"   💾 Respuesta: {filename}")
        
        # 8. Analizar resultado
        html_lower = confirm_response.text.lower()
        
        # Verificar redirección
        if confirm_response.url != link:
            print(f"   ✅ ¡REDIRECCIÓN DETECTADA - CONFIRMACIÓN EXITOSA!")
            return True
        
        # Verificar mensajes de error
        elif 'could not be completed' in html_lower:
            print(f"   ⚠️ 'Could not be completed' - Puede ya estar procesado")
            return False
        
        elif 'invalid password' in html_lower or 'incorrect password' in html_lower:
            print(f"   ❌ Password incorrecto")
            return False
        
        elif 'expired' in html_lower:
            print(f"   ❌ Link expirado")
            return False
        
        # Verificar si el formulario desapareció
        response_soup = BeautifulSoup(confirm_response.text, 'html.parser')
        still_has_form = bool(response_soup.find('input', {'type': 'password'}))
        
        if not still_has_form:
            print(f"   ✅ ¡FORMULARIO DESAPARECIÓ - CONFIRMACIÓN EXITOSA!")
            return True
        
        elif confirm_response.status_code == 200:
            print(f"   ⚠️ Confirmación enviada - Resultado incierto")
            return False
        
        else:
            print(f"   ❌ Error HTTP {confirm_response.status_code}")
            return False
    
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False
    
    finally:
        session.close()

def main():
    """Función principal"""
    print("🚀 CONFIRMADOR DIRECTO DE LINKS POKÉMON")
    print("=" * 60)
    
    # Credenciales
    password = "!DbCyY&48"

    # Links específicos encontrados
    links = [
        "https://club.pokemon.com/us/pokemon-trainer-club/email-change-approval/92806322e391d5b0914edbf0342d9aed"
    ]
    
    print(f"🔑 Password Pokémon: {password}")
    print(f"🔗 Links a confirmar: {len(links)}")
    
    confirmaciones_exitosas = 0
    
    for i, link in enumerate(links, 1):
        print(f"\n{i}️⃣ Procesando link {i}/{len(links)}:")
        print("-" * 50)
        
        if confirmar_link_pokemon(link, password):
            confirmaciones_exitosas += 1
        
        # Pausa entre confirmaciones
        if i < len(links):
            time.sleep(2)
    
    # Resultado final
    print(f"\n📊 RESULTADO FINAL:")
    print("=" * 40)
    print(f"🔗 Links procesados: {len(links)}")
    print(f"✅ Confirmaciones exitosas: {confirmaciones_exitosas}")
    
    if confirmaciones_exitosas > 0:
        print(f"\n🎉 ¡PROCESO COMPLETADO EXITOSAMENTE!")
        print(f"✅ {confirmaciones_exitosas} confirmación(es) exitosa(s)")
        print(f"📧 Email debería cambiar a: <EMAIL>")
        print(f"💡 Verificar en pokemon.com para confirmar")
    else:
        print(f"\n❌ No se completaron confirmaciones exitosas")
        print(f"💡 Revisar archivos de respuesta para más detalles")

if __name__ == "__main__":
    main()
