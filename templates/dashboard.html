<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="/static/css/dashboard.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-envelope me-2"></i>
                Dujaw Mailbox Manager
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-circle status-indicator" id="connectionStatus"></i>
                    <span id="connectionText">Connecting...</span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Control Panel -->
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-cog me-2"></i>
                            Control Panel
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Unlock Section -->
                        <div class="mb-3">
                            <label for="passwordInput" class="form-label">Password</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="passwordInput"
                                       placeholder="Enter password" value="EMVaB#6G3">
                                <button class="btn btn-success" type="button" id="unlockBtn">
                                    <i class="fas fa-unlock me-1"></i>
                                    Unlock
                                </button>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" id="refreshBtn">
                                <i class="fas fa-sync-alt me-1"></i>
                                Refresh Mailbox
                            </button>
                            <button class="btn btn-info" id="statusBtn">
                                <i class="fas fa-info-circle me-1"></i>
                                Get Status
                            </button>
                            <button class="btn btn-warning" id="logoutBtn">
                                <i class="fas fa-sign-out-alt me-1"></i>
                                Logout
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Status Card -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-line me-2"></i>
                            Status
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="stat-item">
                                    <div class="stat-number" id="messageCount">0</div>
                                    <div class="stat-label">Messages</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="stat-item">
                                    <div class="stat-number" id="actionCount">0</div>
                                    <div class="stat-label">Actions</div>
                                </div>
                            </div>
                        </div>
                        <hr>
                        <div class="small text-muted">
                            <div><strong>Email:</strong> <span id="emailAddress"><EMAIL></span></div>
                            <div><strong>Last Update:</strong> <span id="lastUpdate">Never</span></div>
                            <div><strong>Status:</strong> <span id="authStatus" class="badge bg-secondary">Not Connected</span></div>
                        </div>
                    </div>
                </div>

                <!-- Auto-refresh Toggle -->
                <div class="card">
                    <div class="card-body">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="autoRefreshToggle" checked>
                            <label class="form-check-label" for="autoRefreshToggle">
                                Auto-refresh (30s)
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-8">
                <!-- Messages Section -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-inbox me-2"></i>
                            Messages
                        </h5>
                        <button class="btn btn-sm btn-outline-primary" id="composeBtn">
                            <i class="fas fa-plus me-1"></i>
                            Compose
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="messagesContainer">
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-envelope-open-text fa-3x mb-3"></i>
                                <p>No messages loaded. Click "Unlock" to access your mailbox.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions Section -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-tools me-2"></i>
                            Available Actions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="actionsContainer">
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-cogs fa-3x mb-3"></i>
                                <p>No actions available. Access your mailbox to see available options.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Compose Modal -->
    <div class="modal fade" id="composeModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit me-2"></i>
                        Compose Message
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="composeForm">
                        <div class="mb-3">
                            <label for="composeTo" class="form-label">To</label>
                            <input type="email" class="form-control" id="composeTo" required>
                        </div>
                        <div class="mb-3">
                            <label for="composeSubject" class="form-label">Subject</label>
                            <input type="text" class="form-control" id="composeSubject" required>
                        </div>
                        <div class="mb-3">
                            <label for="composeBody" class="form-label">Message</label>
                            <textarea class="form-control" id="composeBody" rows="5" required></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="sendMessageBtn">
                        <i class="fas fa-paper-plane me-1"></i>
                        Send Message
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="notificationToast" class="toast" role="alert">
            <div class="toast-header">
                <i class="fas fa-bell me-2"></i>
                <strong class="me-auto">Notification</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="toastMessage">
                <!-- Toast message will be inserted here -->
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/dashboard.js"></script>
</body>
</html>
