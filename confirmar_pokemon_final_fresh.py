#!/usr/bin/env python3
"""
Script final para confirmar el cambio de email de Pokémon con token CSRF fresco
"""

import requests
import time
from bs4 import BeautifulSoup
from datetime import datetime

def confirmar_pokemon_con_token_fresco():
    """Confirma el cambio de email obteniendo un token CSRF completamente fresco"""
    print("🔐 CONFIRMACIÓN FINAL CON TOKEN FRESCO")
    print("=" * 60)
    
    # Configurar sesión completamente nueva
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Cache-Control': 'no-cache'
    })
    
    # URL del formulario
    pokemon_url = "https://club.pokemon.com/us/pokemon-trainer-club/email-change-approval/49d9fd87c3028b1267f9825fddf6e825"
    password = "EMVaB#6G3"
    
    print(f"🌐 URL: {pokemon_url}")
    print(f"🔑 Password: {password}")
    
    try:
        # 1. Acceder a la página para obtener token fresco
        print(f"\n1️⃣ Obteniendo token CSRF fresco...")
        response = session.get(pokemon_url)
        
        print(f"📥 Status: {response.status_code}")
        print(f"🌐 URL: {response.url}")
        
        if response.status_code != 200:
            print(f"❌ Error accediendo: {response.status_code}")
            return False
        
        # 2. Parsear HTML y extraer token CSRF
        print(f"2️⃣ Extrayendo token CSRF...")
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Buscar token CSRF
        csrf_token = None
        csrf_input = soup.find('input', {'name': 'csrfmiddlewaretoken'})
        if csrf_input:
            csrf_token = csrf_input.get('value')
            print(f"✅ Token CSRF encontrado: {csrf_token[:20]}...")
        else:
            print(f"❌ No se encontró token CSRF")
            return False
        
        # 3. Buscar formulario de confirmación
        print(f"3️⃣ Analizando formulario...")
        confirmation_form = None
        for form in soup.find_all('form'):
            inputs = form.find_all('input')
            has_password = any(inp.get('type') == 'password' for inp in inputs)
            has_csrf = any(inp.get('name') == 'csrfmiddlewaretoken' for inp in inputs)
            
            if has_password and has_csrf:
                confirmation_form = form
                break
        
        if not confirmation_form:
            print(f"❌ No se encontró formulario de confirmación")
            return False
        
        print(f"✅ Formulario encontrado")
        
        # 4. Preparar datos del formulario con token fresco
        print(f"4️⃣ Preparando datos del formulario...")
        form_data = {
            'csrfmiddlewaretoken': csrf_token
        }
        
        for input_elem in confirmation_form.find_all('input'):
            name = input_elem.get('name')
            value = input_elem.get('value', '')
            input_type = input_elem.get('type', 'text')
            
            if not name or name == 'csrfmiddlewaretoken':
                continue  # Ya agregamos el CSRF token fresco
            
            if input_type == 'password':
                form_data[name] = password
                print(f"🔑 Password: {name} = {password}")
            elif input_type in ['email', 'text', 'hidden']:
                form_data[name] = value
                print(f"📧 Field: {name} = {value}")
            elif input_type == 'submit' and value:
                form_data[name] = value
                print(f"🔘 Submit: {name} = {value}")
        
        # 5. Configurar headers adicionales para el POST
        session.headers.update({
            'Referer': pokemon_url,
            'Origin': 'https://club.pokemon.com',
            'X-CSRFToken': csrf_token,
            'Content-Type': 'application/x-www-form-urlencoded'
        })
        
        # 6. Determinar URL de envío
        action = confirmation_form.get('action', '')
        method = confirmation_form.get('method', 'POST').upper()
        
        if action:
            if action.startswith('http'):
                submit_url = action
            elif action.startswith('/'):
                submit_url = f"https://club.pokemon.com{action}"
            else:
                submit_url = f"{pokemon_url.rstrip('/')}/{action}"
        else:
            submit_url = pokemon_url
        
        print(f"5️⃣ Enviando confirmación...")
        print(f"📤 URL: {submit_url}")
        print(f"📋 Método: {method}")
        print(f"📊 Campos: {len(form_data)}")
        print(f"🔒 CSRF Token: {csrf_token[:20]}...")
        
        # 7. Enviar confirmación con pequeña pausa
        time.sleep(1)  # Pausa para evitar rate limiting
        
        if method == 'POST':
            confirm_response = session.post(submit_url, data=form_data, allow_redirects=True)
        else:
            confirm_response = session.get(submit_url, params=form_data, allow_redirects=True)
        
        print(f"6️⃣ Analizando respuesta...")
        print(f"📥 Status: {confirm_response.status_code}")
        print(f"🌐 URL final: {confirm_response.url}")
        print(f"📄 Tamaño: {len(confirm_response.text)} caracteres")
        
        # 8. Guardar respuesta
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"pokemon_confirmacion_fresh_{timestamp}.html"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"<!-- Confirmación con token fresco - {timestamp} -->\n")
            f.write(f"<!-- URL original: {pokemon_url} -->\n")
            f.write(f"<!-- CSRF Token: {csrf_token[:20]}... -->\n")
            f.write(f"<!-- Status: {confirm_response.status_code} -->\n\n")
            f.write(confirm_response.text)
        
        print(f"💾 Respuesta guardada: {filename}")
        
        # 9. Análisis detallado del resultado
        print(f"7️⃣ Análisis detallado del resultado...")
        
        # Verificar si la URL cambió (redirección)
        url_changed = confirm_response.url != pokemon_url
        print(f"🌐 URL cambió: {url_changed}")
        
        if url_changed:
            print(f"   Original: {pokemon_url}")
            print(f"   Final: {confirm_response.url}")
        
        # Buscar indicadores específicos
        html_lower = confirm_response.text.lower()
        
        # Indicadores de éxito más específicos
        success_indicators = [
            'email change has been approved',
            'email address has been changed',
            'successfully confirmed',
            'change approved',
            'email updated',
            'confirmation successful',
            'thank you for confirming'
        ]
        
        # Indicadores de error más específicos
        error_indicators = [
            'could not be completed',
            'invalid password',
            'incorrect password',
            'request has expired',
            'link has expired',
            'already been processed',
            'error occurred'
        ]
        
        success_found = [ind for ind in success_indicators if ind in html_lower]
        error_found = [ind for ind in error_indicators if ind in html_lower]
        
        print(f"🔍 Indicadores de éxito: {success_found}")
        print(f"⚠️ Indicadores de error: {error_found}")
        
        # Buscar el formulario de confirmación en la respuesta
        response_soup = BeautifulSoup(confirm_response.text, 'html.parser')
        still_has_form = bool(response_soup.find('input', {'type': 'password'}))
        
        print(f"📋 Formulario aún presente: {still_has_form}")
        
        # Determinar resultado final
        if success_found and not error_found:
            print(f"\n🎉 ¡CONFIRMACIÓN EXITOSA!")
            print(f"✅ Indicadores de éxito: {', '.join(success_found)}")
            return True
        elif not still_has_form and not error_found:
            print(f"\n✅ ¡POSIBLE ÉXITO!")
            print(f"📋 El formulario desapareció (procesado)")
            print(f"🌐 URL final: {confirm_response.url}")
            return True
        elif error_found:
            print(f"\n❌ Error en confirmación:")
            print(f"⚠️ Errores: {', '.join(error_found)}")
            return False
        elif url_changed and confirm_response.status_code == 200:
            print(f"\n✅ Redirección detectada - posible éxito")
            return True
        else:
            print(f"\n⚠️ Resultado incierto")
            print(f"💡 Revisar archivo: {filename}")
            return False
    
    except Exception as e:
        print(f"\n❌ Error durante confirmación: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        session.close()

def main():
    """Función principal"""
    print("🚀 CONFIRMACIÓN FINAL CON TOKEN CSRF FRESCO")
    print("Basado en la página activa mostrada en la imagen")
    print("=" * 70)
    
    success = confirmar_pokemon_con_token_fresco()
    
    if success:
        print(f"\n🎉 ¡PROCESO COMPLETADO EXITOSAMENTE!")
        print(f"✅ La confirmación se envió correctamente")
        print(f"📧 Email debería cambiar a: <EMAIL>")
        print(f"💡 Verificar en pokemon.com para confirmar")
    else:
        print(f"\n❌ PROCESO NO COMPLETADO")
        print(f"💡 Revisar archivo de respuesta para más detalles")
        print(f"🔍 El formulario puede requerir confirmación manual")
    
    print(f"\n🏁 Proceso finalizado")

if __name__ == "__main__":
    main()
