#!/usr/bin/env python3
"""
Script para buscar correos leídos en el buzón
Analiza exhaustivamente todo el HTML para encontrar correos marcados como leídos
"""

import requests
import json
import re
from datetime import datetime
from bs4 import BeautifulSoup

class BuscadorCorreosLeidos:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
        self.email = "<EMAIL>"
        self.password = "unlockgs2024"
        self.mailbox_url = f"https://dujaw.com/mailbox/{self.email}"
        self.unlock_url = "https://dujaw.com/unlock"

    def acceder_mailbox_autenticado(self):
        """Accede al mailbox y hace unlock si es necesario"""
        print(f"🔍 BUSCANDO CORREOS LEÍDOS")
        print("=" * 40)
        print(f"📧 Email: {self.email}")
        
        # Acceder al mailbox
        response = self.session.get(self.mailbox_url)
        if response.status_code != 200:
            print(f"❌ Error accediendo: {response.status_code}")
            return None
        
        # Verificar si necesita unlock
        soup = BeautifulSoup(response.text, 'html.parser')
        password_input = soup.find('input', {'type': 'password'})
        
        if password_input:
            print("🔓 Haciendo unlock...")
            # Obtener token CSRF
            csrf_input = soup.find('input', {'name': '_token'})
            if not csrf_input:
                print("❌ No se encontró token CSRF")
                return None
            
            csrf_token = csrf_input.get('value')
            
            # Hacer unlock
            form_data = {
                '_token': csrf_token,
                'password': self.password
            }
            
            response = self.session.post(self.unlock_url, data=form_data, allow_redirects=True)
            if response.status_code != 200:
                print(f"❌ Error en unlock: {response.status_code}")
                return None
        
        print("✅ Mailbox accesible")
        return response.text

    def analizar_html_completo(self, html_content):
        """Analiza todo el HTML buscando correos leídos"""
        print(f"\n🔍 ANÁLISIS EXHAUSTIVO DEL HTML")
        print("=" * 40)
        
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Guardar HTML para análisis manual
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"mailbox_completo_{timestamp}.html"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(html_content)
        print(f"💾 HTML guardado: {filename}")
        
        resultados = {
            'timestamp': timestamp,
            'correos_encontrados': [],
            'elementos_sospechosos': [],
            'datos_livewire': {},
            'scripts_relevantes': []
        }
        
        # 1. Buscar en datos de Livewire
        print(f"\n1️⃣ Buscando en datos Livewire...")
        self.buscar_en_livewire(html_content, resultados)
        
        # 2. Buscar elementos con clases relacionadas a correos
        print(f"\n2️⃣ Buscando elementos con clases de correo...")
        self.buscar_elementos_correo(soup, resultados)
        
        # 3. Buscar texto que contenga direcciones de email
        print(f"\n3️⃣ Buscando direcciones de email...")
        self.buscar_direcciones_email(html_content, resultados)
        
        # 4. Buscar patrones de fecha/hora
        print(f"\n4️⃣ Buscando patrones de fecha/hora...")
        self.buscar_fechas(html_content, resultados)
        
        # 5. Buscar en scripts JavaScript
        print(f"\n5️⃣ Buscando en scripts JavaScript...")
        self.buscar_en_scripts(soup, resultados)
        
        # 6. Buscar elementos ocultos o con display:none
        print(f"\n6️⃣ Buscando elementos ocultos...")
        self.buscar_elementos_ocultos(soup, resultados)
        
        # 7. Buscar en atributos data-*
        print(f"\n7️⃣ Buscando en atributos data-*...")
        self.buscar_atributos_data(soup, resultados)
        
        return resultados

    def buscar_en_livewire(self, html_content, resultados):
        """Busca correos en los datos de Livewire"""
        pattern = r'wire:initial-data="([^"]+)"'
        matches = re.findall(pattern, html_content)
        
        for match in matches:
            try:
                import html as html_module
                decoded = html_module.unescape(match)
                data = json.loads(decoded)
                
                # Buscar mensajes en los datos
                if 'serverMemo' in data and 'data' in data['serverMemo']:
                    server_data = data['serverMemo']['data']
                    
                    # Verificar si hay mensajes
                    if 'messages' in server_data:
                        messages = server_data['messages']
                        if messages:
                            print(f"   ✅ Encontrados {len(messages)} mensajes en Livewire")
                            resultados['correos_encontrados'].extend(messages)
                        else:
                            print(f"   📭 Array de mensajes vacío")
                    
                    # Verificar mensajes eliminados
                    if 'deleted' in server_data:
                        deleted = server_data['deleted']
                        if deleted:
                            print(f"   🗑️ Encontrados {len(deleted)} mensajes eliminados")
                            resultados['correos_encontrados'].extend(deleted)
                    
                    # Guardar todos los datos de Livewire
                    component_name = data.get('fingerprint', {}).get('name', 'unknown')
                    resultados['datos_livewire'][component_name] = server_data
                    
            except (json.JSONDecodeError, Exception) as e:
                continue

    def buscar_elementos_correo(self, soup, resultados):
        """Busca elementos HTML que puedan contener correos"""
        # Selectores para buscar correos
        selectores = [
            '[class*="message"]', '[class*="mail"]', '[class*="email"]',
            '[class*="inbox"]', '[class*="read"]', '[class*="unread"]',
            '[id*="message"]', '[id*="mail"]', '[id*="email"]',
            '.message', '.mail', '.email', '.inbox',
            'tr', 'li', 'div[data-message]', '[data-email]'
        ]
        
        for selector in selectores:
            elementos = soup.select(selector)
            if elementos:
                print(f"   📧 Selector '{selector}': {len(elementos)} elementos")
                
                for elemento in elementos:
                    texto = elemento.get_text(strip=True)
                    if texto and len(texto) > 10:
                        # Buscar indicadores de correo
                        if any(indicador in texto.lower() for indicador in 
                               ['from:', 'to:', 'subject:', '@', 'message', 'email']):
                            resultados['elementos_sospechosos'].append({
                                'selector': selector,
                                'tag': elemento.name,
                                'classes': elemento.get('class', []),
                                'id': elemento.get('id', ''),
                                'texto': texto[:200],
                                'html': str(elemento)[:300]
                            })

    def buscar_direcciones_email(self, html_content, resultados):
        """Busca direcciones de email en el HTML"""
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails = re.findall(email_pattern, html_content)
        
        emails_unicos = list(set(emails))
        if emails_unicos:
            print(f"   📧 Encontradas {len(emails_unicos)} direcciones únicas:")
            for email in emails_unicos:
                print(f"      {email}")
                
                # Buscar contexto alrededor del email
                pattern = rf'.{{0,100}}{re.escape(email)}.{{0,100}}'
                contextos = re.findall(pattern, html_content, re.IGNORECASE)
                for contexto in contextos[:3]:  # Primeros 3 contextos
                    resultados['elementos_sospechosos'].append({
                        'tipo': 'email_context',
                        'email': email,
                        'contexto': contexto.strip()
                    })

    def buscar_fechas(self, html_content, resultados):
        """Busca patrones de fecha que puedan indicar correos"""
        # Patrones de fecha comunes
        date_patterns = [
            r'\d{1,2}[/-]\d{1,2}[/-]\d{2,4}',  # DD/MM/YYYY
            r'\d{4}[/-]\d{1,2}[/-]\d{1,2}',    # YYYY/MM/DD
            r'\w+ \d{1,2}, \d{4}',             # Month DD, YYYY
            r'\d{1,2} \w+ \d{4}',              # DD Month YYYY
            r'\d{1,2}:\d{2}',                  # HH:MM
        ]
        
        fechas_encontradas = []
        for pattern in date_patterns:
            matches = re.findall(pattern, html_content)
            fechas_encontradas.extend(matches)
        
        if fechas_encontradas:
            print(f"   📅 Encontrados {len(fechas_encontradas)} patrones de fecha")
            # Buscar contexto alrededor de las fechas
            for fecha in fechas_encontradas[:5]:  # Primeras 5
                pattern = rf'.{{0,50}}{re.escape(fecha)}.{{0,50}}'
                contextos = re.findall(pattern, html_content)
                for contexto in contextos[:1]:  # Primer contexto
                    if any(word in contexto.lower() for word in ['message', 'mail', 'from', 'to']):
                        resultados['elementos_sospechosos'].append({
                            'tipo': 'fecha_context',
                            'fecha': fecha,
                            'contexto': contexto.strip()
                        })

    def buscar_en_scripts(self, soup, resultados):
        """Busca datos de correos en scripts JavaScript"""
        scripts = soup.find_all('script')
        
        for i, script in enumerate(scripts):
            script_content = script.string or ""
            if any(keyword in script_content.lower() for keyword in 
                   ['message', 'email', 'mail', 'fetch', 'data', 'inbox']):
                
                # Buscar objetos JSON en el script
                json_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
                json_matches = re.findall(json_pattern, script_content)
                
                for json_match in json_matches:
                    try:
                        data = json.loads(json_match)
                        if isinstance(data, dict) and any(key in str(data).lower() for key in ['message', 'email', 'mail']):
                            resultados['scripts_relevantes'].append({
                                'script_index': i,
                                'data': data,
                                'content_preview': script_content[:200]
                            })
                    except:
                        continue

    def buscar_elementos_ocultos(self, soup, resultados):
        """Busca elementos que puedan estar ocultos"""
        # Elementos con display:none o visibility:hidden
        ocultos = soup.find_all(style=lambda x: x and ('display:none' in x or 'visibility:hidden' in x))
        ocultos.extend(soup.find_all(class_=lambda x: x and 'hidden' in ' '.join(x).lower()))
        
        for elemento in ocultos:
            texto = elemento.get_text(strip=True)
            if texto and any(indicador in texto.lower() for indicador in ['message', 'email', 'from', 'to', '@']):
                resultados['elementos_sospechosos'].append({
                    'tipo': 'elemento_oculto',
                    'tag': elemento.name,
                    'classes': elemento.get('class', []),
                    'style': elemento.get('style', ''),
                    'texto': texto[:200]
                })

    def buscar_atributos_data(self, soup, resultados):
        """Busca en atributos data-* que puedan contener información de correos"""
        elementos_con_data = soup.find_all(attrs=lambda x: x and any(key.startswith('data-') for key in x.keys()))
        
        for elemento in elementos_con_data:
            for attr, value in elemento.attrs.items():
                if attr.startswith('data-') and isinstance(value, str):
                    if any(keyword in value.lower() for keyword in ['message', 'email', 'mail', '@']):
                        resultados['elementos_sospechosos'].append({
                            'tipo': 'atributo_data',
                            'tag': elemento.name,
                            'atributo': attr,
                            'valor': value[:200],
                            'texto': elemento.get_text(strip=True)[:100]
                        })

    def mostrar_resultados(self, resultados):
        """Muestra los resultados del análisis"""
        print(f"\n📊 RESULTADOS DEL ANÁLISIS")
        print("=" * 40)
        
        # Correos encontrados
        if resultados['correos_encontrados']:
            print(f"🎉 ¡CORREOS ENCONTRADOS: {len(resultados['correos_encontrados'])}!")
            for i, correo in enumerate(resultados['correos_encontrados'], 1):
                print(f"\n📧 CORREO {i}:")
                if isinstance(correo, dict):
                    for key, value in correo.items():
                        print(f"   {key}: {value}")
                else:
                    print(f"   {correo}")
        else:
            print("📭 No se encontraron correos en datos Livewire")
        
        # Elementos sospechosos
        if resultados['elementos_sospechosos']:
            print(f"\n🔍 ELEMENTOS SOSPECHOSOS: {len(resultados['elementos_sospechosos'])}")
            for i, elemento in enumerate(resultados['elementos_sospechosos'][:10], 1):  # Primeros 10
                print(f"\n{i}. Tipo: {elemento.get('tipo', 'elemento')}")
                for key, value in elemento.items():
                    if key != 'tipo':
                        if isinstance(value, str) and len(value) > 100:
                            print(f"   {key}: {value[:100]}...")
                        else:
                            print(f"   {key}: {value}")
        
        # Datos Livewire
        if resultados['datos_livewire']:
            print(f"\n🧩 COMPONENTES LIVEWIRE: {len(resultados['datos_livewire'])}")
            for component, data in resultados['datos_livewire'].items():
                print(f"   {component}: {json.dumps(data, indent=2)[:200]}...")
        
        # Scripts relevantes
        if resultados['scripts_relevantes']:
            print(f"\n📜 SCRIPTS RELEVANTES: {len(resultados['scripts_relevantes'])}")
            for script in resultados['scripts_relevantes']:
                print(f"   Script {script['script_index']}: {script['content_preview']}")
        
        # Guardar resultados
        filename = f"analisis_correos_leidos_{resultados['timestamp']}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(resultados, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Análisis completo guardado: {filename}")

def main():
    """Función principal"""
    print("🔍 BUSCADOR DE CORREOS LEÍDOS")
    print("=" * 50)
    
    try:
        buscador = BuscadorCorreosLeidos()
        
        # Acceder al mailbox
        html_content = buscador.acceder_mailbox_autenticado()
        if not html_content:
            return
        
        # Analizar HTML completo
        resultados = buscador.analizar_html_completo(html_content)
        
        # Mostrar resultados
        buscador.mostrar_resultados(resultados)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
