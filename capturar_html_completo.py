#!/usr/bin/env python3
"""
Script para capturar el HTML completo del mailbox y analizarlo
Busca correos, componentes Livewire y cualquier contenido relevante
"""

import requests
import json
import re
from datetime import datetime
from bs4 import BeautifulSoup
from dujaw_interactive import DujawAPIParametrized

def capturar_y_analizar_html():
    """Captura y analiza el HTML completo del mailbox"""
    
    # Credenciales
    EMAIL = "<EMAIL>"
    PASSWORD = "EMVaB#6G3"
    
    print("🔍 CAPTURA Y ANÁLISIS COMPLETO DEL HTML")
    print("=" * 50)
    print(f"📧 Email: {EMAIL}")
    print(f"🔐 Password: {PASSWORD}")
    
    try:
        # Crear API y hacer unlock
        api = DujawAPIParametrized(EMAIL, PASSWORD)
        
        if not api.unlock_mailbox():
            print("❌ Error en unlock")
            return
        
        print("\n📄 Capturando HTML completo...")
        response = api.session.get(api.mailbox_url)
        
        if response.status_code != 200:
            print(f"❌ Error accediendo mailbox: {response.status_code}")
            return
        
        html_content = response.text
        print(f"✅ HTML capturado ({len(html_content)} caracteres)")
        
        # Guardar HTML completo
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        html_filename = f"mailbox_completo_{timestamp}.html"
        
        with open(html_filename, 'w', encoding='utf-8') as f:
            f.write(html_content)
        print(f"💾 HTML guardado en: {html_filename}")
        
        # Análisis detallado
        print(f"\n🔍 ANÁLISIS DEL CONTENIDO")
        print("=" * 30)
        
        # 1. Buscar componentes Livewire
        print("1️⃣ Buscando componentes Livewire...")
        livewire_patterns = [
            r'wire:id="([^"]+)"',
            r'wire:initial-data="([^"]+)"',
            r'livewire:([^"]+)',
            r'@livewire\([^)]+\)',
            r'Livewire\.component\([^)]+\)'
        ]
        
        livewire_found = {}
        for pattern_name, pattern in zip(['wire_ids', 'initial_data', 'livewire_attrs', 'livewire_directives', 'livewire_components'], livewire_patterns):
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            if matches:
                livewire_found[pattern_name] = matches
                print(f"   ✅ {pattern_name}: {len(matches)} encontrados")
            else:
                print(f"   ❌ {pattern_name}: no encontrados")
        
        # 2. Buscar scripts y datos JavaScript
        print("\n2️⃣ Buscando scripts y datos JavaScript...")
        soup = BeautifulSoup(html_content, 'html.parser')
        
        scripts = soup.find_all('script')
        print(f"   📜 Scripts encontrados: {len(scripts)}")
        
        # Buscar datos en scripts
        script_data = {}
        for i, script in enumerate(scripts):
            script_content = script.string or ""
            if any(keyword in script_content.lower() for keyword in ['message', 'email', 'livewire', 'fetch', 'sync']):
                script_data[f'script_{i}'] = script_content[:500]  # Primeros 500 caracteres
        
        if script_data:
            print(f"   ✅ Scripts relevantes: {len(script_data)}")
        else:
            print(f"   ❌ No se encontraron scripts relevantes")
        
        # 3. Buscar elementos que contengan correos
        print("\n3️⃣ Buscando elementos con contenido de correos...")
        
        # Buscar por texto que contenga @
        email_elements = []
        all_elements = soup.find_all(text=True)
        
        for text in all_elements:
            text_content = str(text).strip()
            if '@' in text_content and len(text_content) > 5:
                parent = text.parent
                email_elements.append({
                    'text': text_content,
                    'tag': parent.name if parent else 'unknown',
                    'classes': parent.get('class', []) if parent else [],
                    'id': parent.get('id', '') if parent else ''
                })
        
        print(f"   📧 Elementos con '@': {len(email_elements)}")
        
        # 4. Buscar formularios y botones
        print("\n4️⃣ Buscando formularios y acciones...")
        
        forms = soup.find_all('form')
        buttons = soup.find_all(['button', 'input'])
        links = soup.find_all('a')
        
        print(f"   📝 Formularios: {len(forms)}")
        print(f"   🔘 Botones: {len(buttons)}")
        print(f"   🔗 Enlaces: {len(links)}")
        
        # 5. Buscar divs y contenedores principales
        print("\n5️⃣ Buscando contenedores principales...")
        
        main_containers = soup.find_all(['div', 'main', 'section'], class_=True)
        interesting_containers = []
        
        for container in main_containers:
            classes = ' '.join(container.get('class', [])).lower()
            container_id = container.get('id', '').lower()
            
            if any(keyword in classes or keyword in container_id 
                   for keyword in ['mail', 'message', 'inbox', 'content', 'main', 'app']):
                interesting_containers.append({
                    'tag': container.name,
                    'classes': container.get('class', []),
                    'id': container.get('id', ''),
                    'content_length': len(container.get_text(strip=True))
                })
        
        print(f"   📦 Contenedores interesantes: {len(interesting_containers)}")
        
        # 6. Buscar meta tags y datos estructurados
        print("\n6️⃣ Buscando meta tags y datos estructurados...")
        
        meta_tags = soup.find_all('meta')
        relevant_meta = []
        
        for meta in meta_tags:
            name = meta.get('name', '').lower()
            content = meta.get('content', '')
            
            if any(keyword in name for keyword in ['csrf', 'token', 'app', 'data']):
                relevant_meta.append({
                    'name': name,
                    'content': content[:100]  # Primeros 100 caracteres
                })
        
        print(f"   🏷️ Meta tags relevantes: {len(relevant_meta)}")
        
        # Crear resumen completo
        resumen = {
            'timestamp': timestamp,
            'html_length': len(html_content),
            'livewire_components': livewire_found,
            'scripts_count': len(scripts),
            'relevant_scripts': len(script_data),
            'email_elements': len(email_elements),
            'forms': len(forms),
            'buttons': len(buttons),
            'links': len(links),
            'interesting_containers': len(interesting_containers),
            'relevant_meta': len(relevant_meta),
            'sample_data': {
                'email_elements': email_elements[:5],  # Primeros 5
                'interesting_containers': interesting_containers[:3],  # Primeros 3
                'relevant_meta': relevant_meta[:5],  # Primeros 5
                'script_samples': {k: v for k, v in list(script_data.items())[:2]}  # Primeros 2
            }
        }
        
        # Guardar resumen
        resumen_filename = f"analisis_mailbox_{timestamp}.json"
        with open(resumen_filename, 'w', encoding='utf-8') as f:
            json.dump(resumen, f, indent=2, ensure_ascii=False)
        
        print(f"\n📊 RESUMEN FINAL")
        print("=" * 20)
        print(f"📄 HTML: {resumen['html_length']} caracteres")
        print(f"🧩 Componentes Livewire: {len(resumen['livewire_components'])}")
        print(f"📜 Scripts: {resumen['scripts_count']}")
        print(f"📧 Elementos con email: {resumen['email_elements']}")
        print(f"📝 Formularios: {resumen['forms']}")
        print(f"🔘 Botones: {resumen['buttons']}")
        print(f"📦 Contenedores: {resumen['interesting_containers']}")
        
        print(f"\n💾 Archivos generados:")
        print(f"   📄 HTML completo: {html_filename}")
        print(f"   📊 Análisis: {resumen_filename}")
        
        # Mostrar algunos elementos de ejemplo
        if email_elements:
            print(f"\n📧 Ejemplos de elementos con email:")
            for i, elem in enumerate(email_elements[:3], 1):
                print(f"   {i}. {elem['text'][:50]}... (tag: {elem['tag']})")
        
        if interesting_containers:
            print(f"\n📦 Contenedores principales:")
            for i, cont in enumerate(interesting_containers[:3], 1):
                classes_str = ' '.join(cont['classes'])
                print(f"   {i}. <{cont['tag']} class='{classes_str}' id='{cont['id']}'> ({cont['content_length']} chars)")
        
        # Cerrar sesión
        api.logout()
        
        return resumen
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """Función principal"""
    print("🚀 INICIANDO CAPTURA COMPLETA")
    print()
    
    resumen = capturar_y_analizar_html()
    
    if resumen:
        print(f"\n✅ CAPTURA COMPLETADA EXITOSAMENTE")
        print(f"💡 Revisa los archivos generados para más detalles")
    else:
        print(f"\n❌ ERROR EN LA CAPTURA")
    
    print()

if __name__ == "__main__":
    main()
