#!/usr/bin/env python3
"""
Script interactivo para Dujaw API con credenciales como parámetros
Permite ingresar email y password como argumentos o de forma interactiva
"""

import sys
import argparse
import getpass
import json
from datetime import datetime
import requests
from bs4 import BeautifulSoup
from typing import Dict, Any, Optional

class DujawAPIParametrized:
    def __init__(self, email: str, password: str):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
        # Credenciales proporcionadas como parámetros
        self.email = email
        self.password = password
        
        self.base_url = "https://dujaw.com"
        self.mailbox_url = f"https://dujaw.com/mailbox/{self.email}"
        self.unlock_url = "https://dujaw.com/unlock"
        self.csrf_token = None
        self.is_authenticated = False
        
        print(f"🔧 API inicializada con:")
        print(f"   📧 Email: {self.email}")
        print(f"   🔐 Password: {'*' * len(self.password)}")
        print(f"   🌐 Mailbox URL: {self.mailbox_url}")

    def get_csrf_token(self) -> Optional[str]:
        """Obtiene el token CSRF de la página de unlock"""
        try:
            print("🔑 Obteniendo token CSRF...")
            response = self.session.get(self.mailbox_url)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')

                # Buscar token CSRF de varias formas
                csrf_input = soup.find('input', {'name': '_token'})
                if not csrf_input:
                    csrf_input = soup.find('input', {'type': 'hidden', 'name': '_token'})
                if not csrf_input:
                    # Buscar en meta tags
                    csrf_meta = soup.find('meta', {'name': 'csrf-token'})
                    if csrf_meta:
                        token = csrf_meta.get('content')
                        print(f"✅ Token CSRF obtenido (meta): {token[:20]}...")
                        return token

                if csrf_input:
                    token = csrf_input.get('value')
                    print(f"✅ Token CSRF obtenido: {token[:20]}...")
                    return token
                else:
                    print("❌ No se encontró token CSRF")
                    return None
            else:
                print(f"❌ Error obteniendo página: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Error obteniendo token CSRF: {e}")
            return None

    def unlock_mailbox(self) -> bool:
        """Realiza el unlock del mailbox"""
        try:
            print("🔓 Realizando unlock del mailbox...")
            
            # Obtener token CSRF
            self.csrf_token = self.get_csrf_token()
            if not self.csrf_token:
                return False
                
            # Preparar datos del formulario
            form_data = {
                '_token': self.csrf_token,
                'password': self.password
            }
            
            # Enviar POST al endpoint de unlock
            response = self.session.post(self.unlock_url, data=form_data)
            
            if response.status_code in [200, 302]:
                print("✅ Unlock exitoso")
                self.is_authenticated = True
                
                # Si hay redirect, seguirlo
                if response.status_code == 302 and 'location' in response.headers:
                    redirect_url = response.headers['location']
                    print(f"🔄 Siguiendo redirect a: {redirect_url}")
                    self.session.get(redirect_url)
                    
                return True
            else:
                print(f"❌ Error en unlock: {response.status_code}")
                print(f"Response: {response.text[:200]}...")
                return False
                
        except Exception as e:
            print(f"❌ Error en unlock: {e}")
            return False

    def access_mailbox(self) -> Optional[Dict[str, Any]]:
        """Accede al mailbox después del unlock"""
        try:
            if not self.is_authenticated:
                print("⚠️ No autenticado, realizando unlock primero...")
                if not self.unlock_mailbox():
                    return None
                    
            print("📧 Accediendo al mailbox...")
            response = self.session.get(self.mailbox_url)
            
            if response.status_code == 200:
                print("✅ Mailbox accedido correctamente")
                
                # Parsear contenido del mailbox
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Extraer información del mailbox
                mailbox_info = self.parse_mailbox_content(soup)
                return mailbox_info
            else:
                print(f"❌ Error accediendo mailbox: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Error accediendo mailbox: {e}")
            return None

    def parse_mailbox_content(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Parsea el contenido del mailbox"""
        try:
            mailbox_info = {
                'email_address': self.email,
                'messages': [],
                'actions': [],
                'timestamp': datetime.now().isoformat()
            }
            
            # Buscar mensajes de email
            message_elements = soup.find_all(['div', 'tr', 'li'], class_=lambda x: x and any(
                keyword in x.lower() for keyword in ['message', 'mail', 'email', 'inbox']
            ))
            
            for element in message_elements:
                text = element.get_text(strip=True)
                if text and len(text) > 10:  # Filtrar elementos vacíos
                    mailbox_info['messages'].append({
                        'content': text[:200],  # Primeros 200 caracteres
                        'html': str(element)[:500]  # HTML limitado
                    })
                    
            # Buscar acciones disponibles (botones, enlaces)
            action_elements = soup.find_all(['a', 'button'], href=True)
            action_elements.extend(soup.find_all('button'))
            
            for element in action_elements:
                text = element.get_text(strip=True)
                href = element.get('href', '')
                onclick = element.get('onclick', '')
                
                if text:
                    mailbox_info['actions'].append({
                        'text': text,
                        'href': href,
                        'onclick': onclick,
                        'type': element.name
                    })
                    
            print(f"📊 Mailbox parseado: {len(mailbox_info['messages'])} mensajes, {len(mailbox_info['actions'])} acciones")
            return mailbox_info
            
        except Exception as e:
            print(f"❌ Error parseando mailbox: {e}")
            return {'error': str(e)}

    def get_status(self) -> Dict[str, Any]:
        """Obtiene el estado del mailbox"""
        try:
            mailbox_info = self.access_mailbox()
            if mailbox_info:
                return {
                    'status': 'active',
                    'authenticated': self.is_authenticated,
                    'email': self.email,
                    'message_count': len(mailbox_info.get('messages', [])),
                    'last_check': datetime.now().isoformat(),
                    'available_actions': len(mailbox_info.get('actions', []))
                }
            else:
                return {
                    'status': 'error',
                    'authenticated': self.is_authenticated,
                    'email': self.email,
                    'last_check': datetime.now().isoformat()
                }
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e),
                'email': self.email,
                'last_check': datetime.now().isoformat()
            }

    def logout(self) -> bool:
        """Cierra la sesión"""
        try:
            print("🚪 Cerrando sesión...")
            self.session.cookies.clear()
            self.is_authenticated = False
            self.csrf_token = None
            print("✅ Sesión cerrada")
            return True
        except Exception as e:
            print(f"❌ Error cerrando sesión: {e}")
            return False

def get_credentials_interactive():
    """Obtiene credenciales de forma interactiva"""
    print("🔐 INGRESO DE CREDENCIALES")
    print("=" * 30)
    
    email = input("📧 Email (default: <EMAIL>): ").strip()
    if not email:
        email = "<EMAIL>"
    
    password = getpass.getpass("🔐 Password (default: EMVaB#6G3): ").strip()
    if not password:
        password = "EMVaB#6G3"
    
    return email, password

def main():
    """Función principal"""
    parser = argparse.ArgumentParser(
        description="Dujaw API con credenciales parametrizadas",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Ejemplos de uso:
  python dujaw_interactive.py --email <EMAIL> --password EMVaB#6G3
  python dujaw_interactive.py -e <EMAIL> -p EMVaB#6G3
  python dujaw_interactive.py --interactive
  python dujaw_interactive.py (modo interactivo por defecto)
        """
    )
    
    parser.add_argument('-e', '--email', 
                       help='Email del mailbox (default: <EMAIL>)')
    parser.add_argument('-p', '--password', 
                       help='Password para unlock (default: EMVaB#6G3)')
    parser.add_argument('-i', '--interactive', action='store_true',
                       help='Modo interactivo para ingresar credenciales')
    parser.add_argument('--json', action='store_true',
                       help='Salida en formato JSON')
    parser.add_argument('--status-only', action='store_true',
                       help='Solo mostrar estado, no contenido completo')
    
    args = parser.parse_args()
    
    print("🚀 DUJAW API PARAMETRIZADA")
    print("=" * 50)
    
    # Obtener credenciales
    if args.interactive or (not args.email and not args.password):
        email, password = get_credentials_interactive()
    else:
        email = args.email or "<EMAIL>"
        password = args.password or "EMVaB#6G3"
    
    # Crear instancia de API
    api = DujawAPIParametrized(email, password)
    
    try:
        # Realizar unlock
        print("\n1. 🔓 Realizando unlock...")
        if api.unlock_mailbox():
            print("✅ Unlock exitoso!")
            
            if args.status_only:
                # Solo mostrar estado
                print("\n2. 📊 Obteniendo estado...")
                status = api.get_status()
                
                if args.json:
                    print(json.dumps(status, indent=2))
                else:
                    print(f"✅ Estado del mailbox:")
                    print(f"   📧 Email: {status['email']}")
                    print(f"   🔐 Autenticado: {status['authenticated']}")
                    print(f"   📬 Mensajes: {status['message_count']}")
                    print(f"   🔧 Acciones: {status['available_actions']}")
                    print(f"   ⏰ Última verificación: {status['last_check']}")
            else:
                # Acceso completo al mailbox
                print("\n2. 📧 Accediendo al mailbox...")
                mailbox_info = api.access_mailbox()
                
                if mailbox_info:
                    if args.json:
                        print(json.dumps(mailbox_info, indent=2, default=str))
                    else:
                        print("✅ Mailbox accedido!")
                        print(f"📧 Mensajes encontrados: {len(mailbox_info.get('messages', []))}")
                        print(f"🔧 Acciones disponibles: {len(mailbox_info.get('actions', []))}")
                        
                        # Mostrar mensajes
                        messages = mailbox_info.get('messages', [])
                        if messages:
                            print(f"\n📬 Mensajes:")
                            for i, msg in enumerate(messages[:5]):  # Primeros 5
                                print(f"   {i+1}. {msg['content'][:100]}...")
                        else:
                            print("\n📭 No hay mensajes en el mailbox")
                        
                        # Mostrar acciones
                        actions = mailbox_info.get('actions', [])
                        if actions:
                            print(f"\n🔧 Acciones disponibles:")
                            for i, action in enumerate(actions[:5]):  # Primeras 5
                                print(f"   {i+1}. {action['text']} ({action['type']})")
                else:
                    print("❌ Error accediendo al mailbox")
        else:
            print("❌ Error en unlock")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n⚠️ Interrumpido por el usuario")
    except Exception as e:
        print(f"\n❌ Error general: {e}")
        sys.exit(1)
    finally:
        # Cerrar sesión
        api.logout()
        
    print("\n🏁 Proceso completado")

if __name__ == "__main__":
    main()
