#!/usr/bin/env python3
"""
Script para probar diferentes variantes del email
"""

import requests
from bs4 import BeautifulSoup

def probar_email(email, password):
    """Prueba un email específico"""
    print(f"\n🧪 PROBANDO EMAIL: {email}")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    })
    
    mailbox_url = f"https://dujaw.com/mailbox/{email}"
    unlock_url = "https://dujaw.com/unlock"
    
    try:
        # Acceder al mailbox
        print(f"📧 Accediendo a: {mailbox_url}")
        response = session.get(mailbox_url)
        print(f"Status: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ Error accediendo: {response.status_code}")
            return False
        
        # Verificar si ya está autenticado
        if 'unlock' not in response.text.lower():
            print("✅ Ya está autenticado!")
            return True
        
        # Obtener token CSRF
        soup = BeautifulSoup(response.text, 'html.parser')
        csrf_input = soup.find('input', {'name': '_token'})
        
        if not csrf_input:
            print("❌ No se encontró token CSRF")
            return False
        
        csrf_token = csrf_input.get('value')
        print(f"🔑 Token CSRF: {csrf_token[:20]}...")
        
        # Intentar unlock
        form_data = {
            '_token': csrf_token,
            'password': password
        }
        
        print(f"🔓 Intentando unlock con password: {password}")
        response = session.post(unlock_url, data=form_data, allow_redirects=True)
        
        print(f"Status después del unlock: {response.status_code}")
        print(f"URL final: {response.url}")
        
        # Verificar si el unlock fue exitoso
        if 'unlock' not in response.text.lower():
            print("✅ UNLOCK EXITOSO!")
            
            # Buscar indicadores de correos
            if 'fetching' in response.text.lower():
                print("📧 Mailbox está cargando mensajes...")
            
            if 'frontend.app' in response.text:
                print("🧩 Componente Livewire encontrado")
            
            return True
        else:
            print("❌ Unlock falló")
            
            # Buscar mensajes de error
            if 'invalid password' in response.text.lower():
                print("🔐 Error: Password inválido")
            elif 'error' in response.text.lower():
                print("⚠️ Se encontró algún error en la página")
            
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Función principal"""
    print("🧪 PROBANDO DIFERENTES EMAILS Y PASSWORDS")
    print("=" * 60)
    
    # Diferentes variantes del email
    emails_to_test = [
        "<EMAIL>",  # Original que me diste
        "<EMAIL>",  # Variante encontrada en el HTML
        "<EMAIL>",  # Con mayúsculas
        "<EMAIL>"   # Todo mayúsculas
    ]
    
    # Passwords a probar
    passwords_to_test = [
        "unlockgs2024",  # Password de Dujaw
        "EMVaB#6G3"      # Password de confirmación
    ]
    
    resultados = []
    
    for email in emails_to_test:
        for password in passwords_to_test:
            print(f"\n{'='*60}")
            print(f"🔍 COMBINACIÓN: {email} + {password}")
            print(f"{'='*60}")
            
            exito = probar_email(email, password)
            
            resultados.append({
                'email': email,
                'password': password,
                'exito': exito
            })
            
            if exito:
                print(f"🎉 ¡COMBINACIÓN EXITOSA ENCONTRADA!")
                print(f"   📧 Email: {email}")
                print(f"   🔐 Password: {password}")
                break
        
        # Si encontramos una combinación exitosa, no probar más emails
        if any(r['exito'] for r in resultados if r['email'] == email):
            break
    
    # Resumen final
    print(f"\n📊 RESUMEN DE PRUEBAS")
    print("=" * 30)
    
    exitosos = [r for r in resultados if r['exito']]
    
    if exitosos:
        print(f"✅ Combinaciones exitosas:")
        for r in exitosos:
            print(f"   📧 {r['email']} + 🔐 {r['password']}")
    else:
        print(f"❌ No se encontraron combinaciones exitosas")
        print(f"\n🔍 Combinaciones probadas:")
        for r in resultados:
            status = "✅" if r['exito'] else "❌"
            print(f"   {status} {r['email']} + {r['password']}")
    
    print(f"\n💡 Recomendaciones:")
    if exitosos:
        mejor = exitosos[0]
        print(f"   - Usar: {mejor['email']} con password {mejor['password']}")
        print(f"   - Esta combinación está confirmada como funcional")
    else:
        print(f"   - Verificar que las credenciales sean correctas")
        print(f"   - Puede que el mailbox haya expirado")
        print(f"   - Intentar crear un nuevo mailbox temporal")

if __name__ == "__main__":
    main()
