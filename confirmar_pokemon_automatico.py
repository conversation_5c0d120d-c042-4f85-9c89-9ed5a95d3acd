#!/usr/bin/env python3
"""
Script para confirmar automáticamente el cambio de email de Pokémon
Obtiene correos del buzón y confirma automáticamente
"""

import requests
import json
import re
from datetime import datetime
from bs4 import BeautifulSoup
from dujaw_api_final import DujawAPI

def extraer_links_de_correos(correos):
    """Extrae links de los correos obtenidos"""
    links = []
    
    for correo in correos:
        # Obtener contenido del correo
        contenido = ""
        
        # Buscar en diferentes campos posibles
        campos_contenido = ['content', 'html', 'body', 'message', 'text']
        for campo in campos_contenido:
            if campo in correo and correo[campo]:
                contenido += str(correo[campo]) + " "
        
        # Extraer links usando regex
        patron_links = r'https?://[^\s<>"\']+(?:[^\s<>"\'.,;!?])'
        links_encontrados = re.findall(patron_links, contenido)
        
        for link in links_encontrados:
            if link not in links:
                links.append(link)
    
    return links

def confirmar_pokemon_automatico(pokemon_link):
    """Confirma automáticamente el cambio de email de Pokémon"""
    print(f"\n🔐 CONFIRMANDO CAMBIO DE EMAIL AUTOMÁTICAMENTE")
    print("=" * 60)
    print(f"🔗 Link: {pokemon_link}")
    
    # Configurar sesión
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
    })
    
    try:
        # 1. Acceder a la página de confirmación
        print(f"1️⃣ Accediendo a página de confirmación...")
        response = session.get(pokemon_link)
        
        print(f"📥 Status: {response.status_code}")
        print(f"🌐 URL: {response.url}")
        
        if response.status_code != 200:
            print(f"❌ Error accediendo: {response.status_code}")
            return False
        
        # 2. Parsear formulario
        print(f"2️⃣ Analizando formulario...")
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Buscar formulario de confirmación
        confirmation_form = None
        for form in soup.find_all('form'):
            inputs = form.find_all('input')
            has_password = any(inp.get('type') == 'password' for inp in inputs)
            has_email = any('email' in inp.get('name', '').lower() for inp in inputs)
            
            if has_password and has_email:
                confirmation_form = form
                break
        
        if not confirmation_form:
            print(f"❌ No se encontró formulario de confirmación")
            return False
        
        print(f"✅ Formulario encontrado")
        
        # 3. Extraer datos del formulario
        print(f"3️⃣ Preparando datos del formulario...")
        form_data = {}
        password_confirmacion = "EMVaB#6G3"
        
        for input_elem in confirmation_form.find_all('input'):
            name = input_elem.get('name')
            value = input_elem.get('value', '')
            input_type = input_elem.get('type', 'text')
            
            if not name:
                continue
            
            if input_type == 'password':
                form_data[name] = password_confirmacion
                print(f"🔑 Password: {name} = {password_confirmacion}")
            elif input_type == 'hidden':
                form_data[name] = value
                print(f"🔒 Hidden: {name} = {value[:30]}...")
            elif input_type in ['email', 'text']:
                form_data[name] = value
                print(f"📧 Field: {name} = {value}")
            elif input_type == 'submit' and value:
                form_data[name] = value
                print(f"🔘 Submit: {name} = {value}")
        
        # 4. Determinar URL de envío
        action = confirmation_form.get('action', '')
        method = confirmation_form.get('method', 'POST').upper()
        
        if action:
            if action.startswith('http'):
                submit_url = action
            elif action.startswith('/'):
                submit_url = f"https://club.pokemon.com{action}"
            else:
                submit_url = f"{pokemon_link.rstrip('/')}/{action}"
        else:
            submit_url = pokemon_link
        
        print(f"4️⃣ Enviando confirmación...")
        print(f"📤 URL: {submit_url}")
        print(f"📋 Método: {method}")
        print(f"📊 Campos: {len(form_data)}")
        
        # 5. Enviar confirmación
        if method == 'POST':
            confirm_response = session.post(submit_url, data=form_data, allow_redirects=True)
        else:
            confirm_response = session.get(submit_url, params=form_data, allow_redirects=True)
        
        print(f"5️⃣ Analizando respuesta...")
        print(f"📥 Status: {confirm_response.status_code}")
        print(f"🌐 URL final: {confirm_response.url}")
        
        # 6. Guardar respuesta
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"pokemon_confirmacion_auto_{timestamp}.html"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"<!-- Confirmación automática - {timestamp} -->\n")
            f.write(f"<!-- URL original: {pokemon_link} -->\n")
            f.write(f"<!-- Status: {confirm_response.status_code} -->\n\n")
            f.write(confirm_response.text)
        
        print(f"💾 Respuesta guardada: {filename}")
        
        # 7. Analizar resultado
        print(f"6️⃣ Analizando resultado...")
        html_lower = confirm_response.text.lower()
        
        success_indicators = [
            'successfully', 'confirmed', 'approved', 'email has been changed',
            'email updated', 'change complete', 'verification successful'
        ]
        
        error_indicators = [
            'error', 'invalid password', 'incorrect password', 'failed',
            'expired', 'not found', 'could not be completed'
        ]
        
        success_found = [ind for ind in success_indicators if ind in html_lower]
        error_found = [ind for ind in error_indicators if ind in html_lower]
        
        print(f"🔍 Indicadores de éxito: {success_found}")
        print(f"⚠️ Indicadores de error: {error_found}")
        
        # Determinar resultado
        if success_found and not error_found:
            print(f"🎉 ¡CONFIRMACIÓN EXITOSA!")
            return True
        elif error_found:
            print(f"❌ Error detectado: {', '.join(error_found)}")
            return False
        elif confirm_response.status_code == 200:
            # Verificar si hay redirección o cambio de contenido
            if confirm_response.url != pokemon_link:
                print(f"✅ Redirección detectada - posible éxito")
                return True
            else:
                print(f"⚠️ Resultado incierto - revisar archivo guardado")
                return False
        else:
            print(f"❌ Status HTTP no exitoso: {confirm_response.status_code}")
            return False
    
    except Exception as e:
        print(f"❌ Error en confirmación: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        session.close()

def verificar_cambio_completado():
    """Verifica si el cambio se completó verificando el nuevo buzón"""
    print(f"\n7️⃣ Verificando si el cambio se completó...")
    
    nuevo_email = "<EMAIL>"
    password = "unlockgs2024"
    
    try:
        api_nuevo = DujawAPI(nuevo_email, password)
        mailbox_nuevo = api_nuevo.access_mailbox()

        if mailbox_nuevo is not None:
            correos_nuevo = mailbox_nuevo.get('messages', [])
            print(f"✅ ¡Buzón nuevo accesible!")
            print(f"📧 Nuevo email activo: {nuevo_email}")
            print(f"📊 Correos en buzón nuevo: {len(correos_nuevo)}")
            return True
        else:
            print(f"❌ Buzón nuevo no accesible aún")
            return False
            
    except Exception as e:
        print(f"❌ Error verificando buzón nuevo: {e}")
        return False

def main():
    """Función principal"""
    print("🚀 CONFIRMADOR AUTOMÁTICO DE EMAIL POKÉMON")
    print("=" * 70)
    
    # Configuración
    email = "<EMAIL>"
    password = "unlockgs2024"
    
    print(f"📧 Email: {email}")
    print(f"🔑 Password: {password}")
    
    try:
        # 1. Obtener correos del buzón
        print(f"\n1️⃣ Obteniendo correos del buzón...")
        api = DujawAPI(email, password)
        mailbox_data = api.access_mailbox()

        if not mailbox_data:
            print(f"❌ No se pudo acceder al buzón")
            return

        correos = mailbox_data.get('messages', [])
        print(f"✅ Buzón accedido - Correos encontrados: {len(correos)}")

        # Mostrar resumen de correos
        print(f"\n📊 CORREOS ENCONTRADOS:")
        if correos:
            for i, correo in enumerate(correos, 1):
                sender = correo.get('sender_name', correo.get('from', 'N/A'))
                subject = correo.get('subject', correo.get('title', 'N/A'))
                print(f"   {i}. De: {sender} | Asunto: {subject}")
        else:
            print(f"   No hay correos en el buzón")
        
        # 2. Extraer links
        print(f"\n2️⃣ Extrayendo links...")
        links = extraer_links_de_correos(correos)
        
        if not links:
            print(f"❌ No se encontraron links")
            return
        
        print(f"✅ Links extraídos: {len(links)}")
        
        # 3. Buscar link de Pokémon
        pokemon_link = None
        for link in links:
            if 'pokemon.com' in link and 'email-change-approval' in link:
                pokemon_link = link
                break
        
        if not pokemon_link:
            print(f"❌ No se encontró link de confirmación de Pokémon")
            print(f"🔗 Links disponibles:")
            for i, link in enumerate(links[:5], 1):
                print(f"   {i}. {link}")
            return
        
        print(f"🎯 Link de Pokémon encontrado: {pokemon_link}")
        
        # 4. Confirmar automáticamente
        confirmacion_exitosa = confirmar_pokemon_automatico(pokemon_link)
        
        # 5. Verificar resultado
        cambio_completado = verificar_cambio_completado()
        
        # 6. Mostrar resultado final
        print(f"\n📊 RESULTADO FINAL")
        print("=" * 40)
        
        if confirmacion_exitosa and cambio_completado:
            print(f"🎉 ¡PROCESO COMPLETADO EXITOSAMENTE!")
            print(f"✅ Confirmación enviada correctamente")
            print(f"✅ Cambio de email verificado")
            print(f"📧 Email cambiado a: <EMAIL>")
        elif confirmacion_exitosa:
            print(f"✅ Confirmación enviada exitosamente")
            print(f"⏳ Cambio puede estar procesándose")
            print(f"💡 Verificar manualmente en pokemon.com")
        elif cambio_completado:
            print(f"🎉 ¡Cambio ya estaba completado!")
            print(f"✅ Email activo: <EMAIL>")
        else:
            print(f"❌ Proceso no completado")
            print(f"💡 Revisar archivos guardados para más detalles")
            print(f"🔍 Intentar confirmación manual")
        
        # Guardar resumen
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        resumen = {
            'timestamp': timestamp,
            'email_original': email,
            'correos_encontrados': len(correos) if correos else 0,
            'links_extraidos': len(links),
            'pokemon_link': pokemon_link,
            'confirmacion_exitosa': confirmacion_exitosa,
            'cambio_completado': cambio_completado
        }
        
        with open(f"resumen_confirmacion_{timestamp}.json", 'w', encoding='utf-8') as f:
            json.dump(resumen, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Resumen guardado: resumen_confirmacion_{timestamp}.json")
    
    except Exception as e:
        print(f"❌ Error general: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n🏁 Proceso completado")

if __name__ == "__main__":
    main()
