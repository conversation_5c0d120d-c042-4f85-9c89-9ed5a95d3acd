#!/usr/bin/env python3
"""
Script correcto para obtener correos
Detecta correctamente cuando el mailbox está desbloqueado
"""

import requests
import json
import time
import re
import html
from datetime import datetime
from bs4 import BeautifulSoup

class DujawCorreosCorreto:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
        # Credenciales correctas
        self.email = "<EMAIL>"
        self.password = "unlockgs2024"
        
        self.base_url = "https://dujaw.com"
        self.mailbox_url = f"https://dujaw.com/mailbox/{self.email}"
        self.unlock_url = "https://dujaw.com/unlock"
        self.csrf_token = None
        self.is_authenticated = False
        
        print(f"🔧 Configuración:")
        print(f"   📧 Email: {self.email}")
        print(f"   🔐 Password: {self.password}")

    def verificar_estado_mailbox(self, html_content):
        """Verifica si el mailbox está desbloqueado correctamente"""
        
        # Buscar formulario de unlock (input type="password")
        soup = BeautifulSoup(html_content, 'html.parser')
        password_input = soup.find('input', {'type': 'password'})
        unlock_form = soup.find('form', action=lambda x: x and 'unlock' in str(x))
        
        # Si no hay formulario de password, está desbloqueado
        if not password_input and not unlock_form:
            print("✅ Mailbox está desbloqueado (no hay formulario de password)")
            return True
        
        # Buscar componente frontend.app
        if 'frontend.app' in html_content:
            print("✅ Componente frontend.app encontrado")
            return True
        
        # Buscar el email en los datos de Livewire
        if self.email in html_content:
            print("✅ Email encontrado en los datos del sistema")
            return True
        
        print("❌ Mailbox no está desbloqueado")
        return False

    def acceder_mailbox(self):
        """Accede al mailbox y verifica el estado"""
        print(f"\n📧 ACCEDIENDO AL MAILBOX")
        print("=" * 30)
        
        # Acceder al mailbox
        response = self.session.get(self.mailbox_url)
        print(f"Status: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ Error accediendo: {response.status_code}")
            return None
        
        # Verificar estado
        if self.verificar_estado_mailbox(response.text):
            print("✅ Mailbox accesible")
            self.is_authenticated = True
            return response.text
        
        # Si no está desbloqueado, intentar unlock
        print("🔓 Intentando unlock...")
        return self.hacer_unlock(response.text)

    def hacer_unlock(self, html_content):
        """Hace unlock del mailbox"""
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Buscar token CSRF
        csrf_input = soup.find('input', {'name': '_token'})
        if not csrf_input:
            print("❌ No se encontró token CSRF")
            return None
        
        self.csrf_token = csrf_input.get('value')
        print(f"🔑 Token CSRF: {self.csrf_token[:20]}...")
        
        # Enviar unlock
        form_data = {
            '_token': self.csrf_token,
            'password': self.password
        }
        
        response = self.session.post(self.unlock_url, data=form_data, allow_redirects=True)
        
        if response.status_code == 200:
            if self.verificar_estado_mailbox(response.text):
                print("✅ Unlock exitoso")
                self.is_authenticated = True
                return response.text
            else:
                print("❌ Unlock falló")
                return None
        else:
            print(f"❌ Error en unlock: {response.status_code}")
            return None

    def extraer_datos_livewire(self, html_content):
        """Extrae datos del componente Livewire frontend.app"""
        print(f"\n🧩 EXTRAYENDO DATOS LIVEWIRE")
        print("=" * 35)
        
        # Buscar componente frontend.app
        pattern = r'wire:id="([^"]+)"\s+wire:initial-data="([^"]+)"[^>]*'
        
        for match in re.finditer(pattern, html_content):
            wire_id = match.group(1)
            initial_data_encoded = match.group(2)
            
            try:
                # Decodificar HTML entities
                initial_data_json = html.unescape(initial_data_encoded)
                initial_data = json.loads(initial_data_json)
                
                # Verificar si es frontend.app
                if initial_data.get('fingerprint', {}).get('name') == 'frontend.app':
                    print(f"✅ Componente frontend.app encontrado")
                    print(f"   Wire ID: {wire_id}")
                    
                    server_data = initial_data['serverMemo']['data']
                    print(f"   Email: {server_data['email']}")
                    print(f"   Mensajes actuales: {len(server_data['messages'])}")
                    print(f"   Mensajes eliminados: {len(server_data.get('deleted', []))}")
                    print(f"   Error: {server_data.get('error', 'ninguno')}")
                    print(f"   Inicial: {server_data.get('initial', False)}")
                    
                    return {
                        'wire_id': wire_id,
                        'initial_data': initial_data,
                        'server_data': server_data
                    }
                    
            except json.JSONDecodeError as e:
                print(f"❌ Error decodificando JSON: {e}")
                continue
        
        print("❌ No se encontró componente frontend.app")
        return None

    def hacer_llamada_livewire(self, livewire_data, event_name, params=None):
        """Hace llamada a Livewire"""
        wire_id = livewire_data['wire_id']
        initial_data = livewire_data['initial_data']
        
        # Construir petición
        livewire_request = {
            'fingerprint': initial_data['fingerprint'],
            'serverMemo': initial_data['serverMemo'],
            'updates': [
                {
                    'type': 'fireEvent',
                    'payload': {
                        'id': wire_id,
                        'event': event_name,
                        'params': params or []
                    }
                }
            ]
        }
        
        # Headers
        headers = {
            'X-Livewire': 'true',
            'X-CSRF-TOKEN': self.csrf_token,
            'Content-Type': 'application/json',
            'Accept': 'text/html, application/xhtml+xml',
            'X-Requested-With': 'XMLHttpRequest',
            'Referer': self.mailbox_url
        }
        
        # URL
        livewire_url = f"{self.base_url}/livewire/message/{initial_data['fingerprint']['name']}"
        
        try:
            print(f"🔄 Llamada Livewire: {event_name}")
            response = self.session.post(livewire_url, json=livewire_request, headers=headers)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ Respuesta exitosa")
                    
                    # Actualizar datos si están disponibles
                    if 'serverMemo' in data:
                        livewire_data['initial_data']['serverMemo'] = data['serverMemo']
                        livewire_data['server_data'] = data['serverMemo']['data']
                    
                    return data
                    
                except json.JSONDecodeError:
                    print(f"⚠️ Respuesta no es JSON")
                    return response.text
            else:
                print(f"❌ Error: {response.status_code}")
                print(f"Respuesta: {response.text[:200]}...")
                return None
                
        except Exception as e:
            print(f"❌ Error en llamada: {e}")
            return None

    def obtener_correos_completo(self):
        """Proceso completo para obtener correos"""
        print(f"\n🚀 PROCESO COMPLETO DE OBTENCIÓN DE CORREOS")
        print("=" * 50)
        
        # Paso 1: Acceder al mailbox
        html_content = self.acceder_mailbox()
        if not html_content:
            return None
        
        # Paso 2: Extraer datos Livewire
        livewire_data = self.extraer_datos_livewire(html_content)
        if not livewire_data:
            return None
        
        # Paso 3: Verificar mensajes actuales
        mensajes_iniciales = livewire_data['server_data']['messages']
        print(f"\n📬 Mensajes iniciales: {len(mensajes_iniciales)}")
        
        if mensajes_iniciales:
            print("🎉 ¡Ya hay mensajes disponibles!")
            return {
                'timestamp': datetime.now().isoformat(),
                'email': self.email,
                'mensajes': mensajes_iniciales,
                'total_mensajes': len(mensajes_iniciales),
                'fuente': 'datos_iniciales'
            }
        
        # Paso 4: Sincronizar email
        print(f"\n1️⃣ Sincronizando email...")
        sync_response = self.hacer_llamada_livewire(livewire_data, 'syncEmail', [self.email])
        
        if sync_response:
            print("✅ syncEmail exitoso")
            # Verificar si ahora hay mensajes
            if livewire_data['server_data']['messages']:
                mensajes = livewire_data['server_data']['messages']
                print(f"📬 Mensajes después de sync: {len(mensajes)}")
                return {
                    'timestamp': datetime.now().isoformat(),
                    'email': self.email,
                    'mensajes': mensajes,
                    'total_mensajes': len(mensajes),
                    'fuente': 'sync_email'
                }
        
        # Paso 5: Obtener mensajes (múltiples intentos)
        print(f"\n2️⃣ Obteniendo mensajes...")
        
        for intento in range(5):
            print(f"\n🔄 Intento {intento + 1}/5")
            
            fetch_response = self.hacer_llamada_livewire(livewire_data, 'fetchMessages')
            
            if fetch_response and isinstance(fetch_response, dict):
                # Verificar mensajes en la respuesta actualizada
                if livewire_data['server_data']['messages']:
                    mensajes = livewire_data['server_data']['messages']
                    print(f"🎉 ¡{len(mensajes)} mensajes encontrados!")
                    return {
                        'timestamp': datetime.now().isoformat(),
                        'email': self.email,
                        'mensajes': mensajes,
                        'total_mensajes': len(mensajes),
                        'fuente': f'fetch_messages_intento_{intento + 1}'
                    }
                else:
                    print("📭 No hay mensajes aún")
            
            # Esperar entre intentos
            if intento < 4:
                print("⏳ Esperando 3 segundos...")
                time.sleep(3)
        
        # No se encontraron mensajes
        return {
            'timestamp': datetime.now().isoformat(),
            'email': self.email,
            'mensajes': [],
            'total_mensajes': 0,
            'fuente': 'sin_mensajes'
        }

    def mostrar_resultados(self, resultado):
        """Muestra los resultados"""
        print(f"\n📊 RESULTADOS FINALES")
        print("=" * 30)
        
        if not resultado:
            print("❌ No se obtuvieron resultados")
            return
        
        print(f"📧 Email: {resultado['email']}")
        print(f"📬 Total mensajes: {resultado['total_mensajes']}")
        print(f"🔍 Fuente: {resultado['fuente']}")
        print(f"⏰ Timestamp: {resultado['timestamp']}")
        
        if resultado['mensajes']:
            print(f"\n🎉 ¡SE ENCONTRARON {resultado['total_mensajes']} MENSAJES!")
            print("=" * 40)
            
            for i, mensaje in enumerate(resultado['mensajes'], 1):
                print(f"\n📧 MENSAJE {i}:")
                if isinstance(mensaje, dict):
                    for key, value in mensaje.items():
                        if isinstance(value, str) and len(value) > 100:
                            print(f"   {key}: {value[:100]}...")
                        else:
                            print(f"   {key}: {value}")
                else:
                    print(f"   Contenido: {mensaje}")
        else:
            print(f"\n📭 NO SE ENCONTRARON MENSAJES")
            print("💡 Posibles razones:")
            print("   - El buzón está vacío")
            print("   - Los mensajes se eliminaron automáticamente")
            print("   - El buzón temporal ha expirado")
        
        # Guardar resultados
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"correos_resultado_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(resultado, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Resultados guardados en: {filename}")

def main():
    """Función principal"""
    print("🚀 OBTENER CORREOS - VERSIÓN CORRECTA")
    print("=" * 50)
    
    try:
        # Crear instancia
        dujaw = DujawCorreosCorreto()
        
        # Obtener correos
        resultado = dujaw.obtener_correos_completo()
        
        # Mostrar resultados
        dujaw.mostrar_resultados(resultado)
        
    except Exception as e:
        print(f"❌ Error general: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n🏁 Proceso completado")

if __name__ == "__main__":
    main()
