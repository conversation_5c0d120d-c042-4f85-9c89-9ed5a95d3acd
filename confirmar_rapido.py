#!/usr/bin/env python3
"""
Script rápido para confirmar emails - Solo edita las credenciales abajo
"""

# =============================================================================
# 🔧 EDITA ESTAS CREDENCIALES
# =============================================================================

# Tu email temporal de Dujaw
EMAIL = "<EMAIL>"

# Password para desbloquear el buzón en Dujaw
DUJAW_PASSWORD = "unlockgs2024"

# Password para confirmar cambios (el que usas en formularios de confirmación)
CONFIRMATION_PASSWORD = "EMVaB#6G3"

# =============================================================================
# 🚀 NO EDITES NADA ABAJO DE ESTA LÍNEA
# =============================================================================

import requests
import json
import re
from datetime import datetime
from bs4 import BeautifulSoup
from dujaw_api_final import DujawAPI

def extraer_links_pokemon(correos):
    """Extrae links de confirmación de Pokémon"""
    links = []
    
    for correo in correos:
        contenido = str(correo.get('content', ''))
        
        # Buscar links de Pokémon específicamente
        patron = r'https://club\.pokemon\.com/[^\s<>"\']+email-change-approval/[a-f0-9]+'
        links_encontrados = re.findall(patron, contenido)
        
        for link in links_encontrados:
            if link not in links:
                links.append(link)
    
    return links

def confirmar_pokemon(link):
    """Confirma automáticamente un link de Pokémon"""
    print(f"🔐 Confirmando: {link}")
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    })
    
    try:
        # Acceder a la página
        response = session.get(link)
        if response.status_code != 200:
            print(f"❌ Error accediendo: {response.status_code}")
            return False
        
        # Parsear formulario
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Buscar formulario de confirmación
        form = None
        for f in soup.find_all('form'):
            if f.find('input', {'type': 'password'}):
                form = f
                break
        
        if not form:
            print(f"❌ No se encontró formulario")
            return False
        
        # Preparar datos
        form_data = {}
        
        for input_elem in form.find_all('input'):
            name = input_elem.get('name')
            value = input_elem.get('value', '')
            input_type = input_elem.get('type', 'text')
            
            if not name:
                continue
            
            if input_type == 'password':
                form_data[name] = CONFIRMATION_PASSWORD
            else:
                form_data[name] = value
        
        # Enviar confirmación
        confirm_response = session.post(link, data=form_data, allow_redirects=True)
        
        print(f"📥 Status: {confirm_response.status_code}")
        print(f"🌐 URL final: {confirm_response.url}")
        
        # Verificar resultado
        if confirm_response.url != link:
            print(f"✅ ¡Redirección detectada - Confirmación exitosa!")
            return True
        elif 'could not be completed' in confirm_response.text.lower():
            print(f"⚠️ Mensaje: 'Could not be completed' - Puede ya estar procesado")
            return False
        else:
            print(f"⚠️ Resultado incierto")
            return False
    
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    finally:
        session.close()

def main():
    """Función principal"""
    print("🚀 CONFIRMADOR RÁPIDO DE EMAIL")
    print("=" * 50)
    print(f"📧 Email: {EMAIL}")
    print(f"🔑 Password Dujaw: {DUJAW_PASSWORD}")
    print(f"🔐 Password Confirmación: {CONFIRMATION_PASSWORD}")
    
    try:
        # 1. Obtener correos
        print(f"\n1️⃣ Obteniendo correos...")
        api = DujawAPI(EMAIL, DUJAW_PASSWORD)
        mailbox_data = api.access_mailbox()
        
        if not mailbox_data:
            print(f"❌ No se pudo acceder al buzón")
            return
        
        correos = mailbox_data.get('messages', [])
        print(f"✅ Encontrados {len(correos)} correos")
        
        # 2. Extraer links de Pokémon
        print(f"\n2️⃣ Buscando links de Pokémon...")
        links_pokemon = extraer_links_pokemon(correos)
        
        if not links_pokemon:
            print(f"❌ No se encontraron links de Pokémon")
            return
        
        print(f"✅ Encontrados {len(links_pokemon)} links de Pokémon")
        
        # 3. Confirmar cada link
        print(f"\n3️⃣ Confirmando links...")
        confirmaciones_exitosas = 0
        
        for i, link in enumerate(links_pokemon, 1):
            print(f"\n🔄 Link {i}/{len(links_pokemon)}:")
            if confirmar_pokemon(link):
                confirmaciones_exitosas += 1
        
        # 4. Resultado final
        print(f"\n📊 RESULTADO:")
        print(f"🔗 Links procesados: {len(links_pokemon)}")
        print(f"✅ Confirmaciones exitosas: {confirmaciones_exitosas}")
        
        if confirmaciones_exitosas > 0:
            print(f"\n🎉 ¡PROCESO COMPLETADO!")
        else:
            print(f"\n⚠️ No se completaron confirmaciones")
    
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
