#!/usr/bin/env python3
"""
Script mejorado para obtener correos del mailbox
Maneja correctamente el unlock y captura el contenido real
"""

import requests
import json
import time
from datetime import datetime
from bs4 import BeautifulSoup
import re

class DujawMailboxMejorado:
    def __init__(self, email, password):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
        self.email = email
        self.password = password
        self.base_url = "https://dujaw.com"
        self.mailbox_url = f"https://dujaw.com/mailbox/{self.email}"
        self.unlock_url = "https://dujaw.com/unlock"
        self.csrf_token = None
        self.is_authenticated = False
        
        print(f"🔧 Mailbox inicializado:")
        print(f"   📧 Email: {self.email}")
        print(f"   🌐 URL: {self.mailbox_url}")

    def get_csrf_token_from_page(self, url):
        """Obtiene el token CSRF de cualquier página"""
        try:
            response = self.session.get(url)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Buscar token en input hidden
                csrf_input = soup.find('input', {'name': '_token'})
                if csrf_input:
                    return csrf_input.get('value')
                
                # Buscar token en meta tag
                csrf_meta = soup.find('meta', {'name': 'csrf-token'})
                if csrf_meta:
                    return csrf_meta.get('content')
                    
        except Exception as e:
            print(f"❌ Error obteniendo CSRF: {e}")
        
        return None

    def unlock_mailbox_completo(self):
        """Proceso completo de unlock con seguimiento de redirects"""
        print("\n🔓 PROCESO COMPLETO DE UNLOCK")
        print("=" * 40)
        
        # Paso 1: Acceder al mailbox para obtener el formulario de unlock
        print("1️⃣ Accediendo al mailbox...")
        response = self.session.get(self.mailbox_url)
        print(f"   Status: {response.status_code}")
        print(f"   URL final: {response.url}")
        
        if response.status_code != 200:
            print(f"❌ Error accediendo mailbox: {response.status_code}")
            return False
        
        # Verificar si ya estamos autenticados
        if 'unlock' not in response.text.lower():
            print("✅ Ya estamos autenticados!")
            self.is_authenticated = True
            return True
        
        # Paso 2: Obtener token CSRF
        print("2️⃣ Obteniendo token CSRF...")
        self.csrf_token = self.get_csrf_token_from_page(self.mailbox_url)
        
        if not self.csrf_token:
            print("❌ No se pudo obtener token CSRF")
            return False
        
        print(f"✅ Token CSRF: {self.csrf_token[:20]}...")
        
        # Paso 3: Enviar formulario de unlock
        print("3️⃣ Enviando formulario de unlock...")
        
        form_data = {
            '_token': self.csrf_token,
            'password': self.password
        }
        
        # Enviar POST con allow_redirects=False para manejar redirects manualmente
        response = self.session.post(
            self.unlock_url, 
            data=form_data, 
            allow_redirects=False
        )
        
        print(f"   Status: {response.status_code}")
        
        # Paso 4: Manejar respuesta y redirects
        if response.status_code == 302:
            print("4️⃣ Siguiendo redirect...")
            redirect_url = response.headers.get('location', '')
            print(f"   Redirect a: {redirect_url}")
            
            if redirect_url:
                # Seguir el redirect
                if redirect_url.startswith('/'):
                    redirect_url = self.base_url + redirect_url
                
                final_response = self.session.get(redirect_url)
                print(f"   Status final: {final_response.status_code}")
                print(f"   URL final: {final_response.url}")
                
                if final_response.status_code == 200:
                    # Verificar si el unlock fue exitoso
                    if 'unlock' not in final_response.text.lower():
                        print("✅ Unlock exitoso!")
                        self.is_authenticated = True
                        return True
                    else:
                        print("❌ Unlock falló - aún se pide password")
                        return False
                        
        elif response.status_code == 200:
            print("4️⃣ Respuesta directa (sin redirect)...")
            if 'unlock' not in response.text.lower():
                print("✅ Unlock exitoso!")
                self.is_authenticated = True
                return True
            else:
                print("❌ Unlock falló - aún se pide password")
                return False
        else:
            print(f"❌ Error en unlock: {response.status_code}")
            return False
        
        return False

    def obtener_contenido_mailbox(self):
        """Obtiene el contenido real del mailbox después del unlock"""
        print("\n📧 OBTENIENDO CONTENIDO DEL MAILBOX")
        print("=" * 40)
        
        if not self.is_authenticated:
            print("⚠️ No autenticado, intentando unlock...")
            if not self.unlock_mailbox_completo():
                return None
        
        # Acceder al mailbox autenticado
        print("📥 Accediendo al mailbox autenticado...")
        response = self.session.get(self.mailbox_url)
        
        print(f"Status: {response.status_code}")
        print(f"URL: {response.url}")
        print(f"Tamaño: {len(response.text)} caracteres")
        
        if response.status_code != 200:
            print(f"❌ Error accediendo mailbox: {response.status_code}")
            return None
        
        # Guardar HTML del mailbox autenticado
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"mailbox_autenticado_{timestamp}.html"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(response.text)
        
        print(f"💾 HTML guardado: {filename}")
        
        return {
            'html_content': response.text,
            'filename': filename,
            'timestamp': timestamp,
            'url': response.url,
            'status_code': response.status_code
        }

    def analizar_contenido_mailbox(self, contenido):
        """Analiza el contenido del mailbox para encontrar correos"""
        print("\n🔍 ANALIZANDO CONTENIDO DEL MAILBOX")
        print("=" * 40)
        
        if not contenido:
            print("❌ No hay contenido para analizar")
            return None
        
        html_content = contenido['html_content']
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Buscar diferentes patrones de correos
        correos_encontrados = []
        
        # 1. Buscar elementos que contengan direcciones de email
        print("1️⃣ Buscando direcciones de email...")
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails_found = re.findall(email_pattern, html_content)
        
        if emails_found:
            print(f"   ✅ Emails encontrados: {len(emails_found)}")
            for email in set(emails_found):  # Eliminar duplicados
                print(f"      📧 {email}")
        else:
            print("   ❌ No se encontraron direcciones de email")
        
        # 2. Buscar elementos con clases relacionadas a mensajes
        print("2️⃣ Buscando elementos de mensajes...")
        message_selectors = [
            '[class*="message"]',
            '[class*="mail"]',
            '[class*="email"]',
            '[class*="inbox"]',
            '[id*="message"]',
            '[id*="mail"]',
            '[id*="email"]'
        ]
        
        message_elements = []
        for selector in message_selectors:
            elements = soup.select(selector)
            if elements:
                print(f"   ✅ Selector '{selector}': {len(elements)} elementos")
                message_elements.extend(elements)
        
        # 3. Buscar texto que indique mensajes
        print("3️⃣ Buscando texto indicativo de mensajes...")
        message_indicators = [
            'from:', 'to:', 'subject:', 'sender:', 'recipient:',
            'message', 'email', 'mail', 'inbox', 'received'
        ]
        
        text_elements = soup.find_all(string=True)
        relevant_texts = []
        
        for text in text_elements:
            text_lower = str(text).lower().strip()
            if any(indicator in text_lower for indicator in message_indicators):
                if len(text_lower) > 5:  # Filtrar textos muy cortos
                    relevant_texts.append(str(text).strip())
        
        if relevant_texts:
            print(f"   ✅ Textos relevantes: {len(relevant_texts)}")
            for text in relevant_texts[:5]:  # Mostrar primeros 5
                print(f"      💬 {text[:80]}...")
        else:
            print("   ❌ No se encontraron textos relevantes")
        
        # 4. Buscar formularios y botones de acciones
        print("4️⃣ Buscando acciones disponibles...")
        forms = soup.find_all('form')
        buttons = soup.find_all(['button', 'input[type="submit"]', 'input[type="button"]'])
        links = soup.find_all('a')
        
        print(f"   📝 Formularios: {len(forms)}")
        print(f"   🔘 Botones: {len(buttons)}")
        print(f"   🔗 Enlaces: {len(links)}")
        
        # Buscar acciones específicas de correo
        email_actions = []
        for button in buttons:
            text = button.get_text(strip=True) or button.get('value', '')
            if any(action in text.lower() for action in ['read', 'delete', 'reply', 'forward', 'refresh', 'new']):
                email_actions.append(f"Botón: {text}")
        
        for link in links:
            text = link.get_text(strip=True)
            href = link.get('href', '')
            if any(action in text.lower() for action in ['read', 'delete', 'reply', 'forward', 'refresh', 'new']):
                email_actions.append(f"Enlace: {text} -> {href}")
        
        if email_actions:
            print(f"   ✅ Acciones de correo: {len(email_actions)}")
            for action in email_actions[:5]:
                print(f"      🔧 {action}")
        
        # 5. Buscar scripts con datos de correos
        print("5️⃣ Buscando scripts con datos...")
        scripts = soup.find_all('script')
        relevant_scripts = []
        
        for script in scripts:
            script_content = script.string or ""
            if any(keyword in script_content.lower() for keyword in ['message', 'email', 'mail', 'fetch', 'data']):
                relevant_scripts.append(script_content[:200])  # Primeros 200 caracteres
        
        if relevant_scripts:
            print(f"   ✅ Scripts relevantes: {len(relevant_scripts)}")
        else:
            print("   ❌ No se encontraron scripts relevantes")
        
        # Crear resumen del análisis
        analisis = {
            'timestamp': contenido['timestamp'],
            'emails_encontrados': list(set(emails_found)),
            'elementos_mensaje': len(message_elements),
            'textos_relevantes': relevant_texts[:10],  # Primeros 10
            'acciones_correo': email_actions,
            'scripts_relevantes': len(relevant_scripts),
            'formularios': len(forms),
            'botones': len(buttons),
            'enlaces': len(links)
        }
        
        # Guardar análisis
        analisis_filename = f"analisis_correos_{contenido['timestamp']}.json"
        with open(analisis_filename, 'w', encoding='utf-8') as f:
            json.dump(analisis, f, indent=2, ensure_ascii=False)
        
        print(f"\n📊 RESUMEN DEL ANÁLISIS:")
        print(f"   📧 Emails encontrados: {len(analisis['emails_encontrados'])}")
        print(f"   📝 Elementos de mensaje: {analisis['elementos_mensaje']}")
        print(f"   💬 Textos relevantes: {len(analisis['textos_relevantes'])}")
        print(f"   🔧 Acciones de correo: {len(analisis['acciones_correo'])}")
        print(f"   💾 Análisis guardado: {analisis_filename}")
        
        return analisis

def main():
    """Función principal"""
    print("🚀 OBTENER CORREOS - VERSIÓN MEJORADA")
    print("=" * 50)
    
    # Credenciales
    EMAIL = "<EMAIL>"
    PASSWORD = "EMVaB#6G3"
    
    print(f"📧 Email: {EMAIL}")
    print(f"🔐 Password: {PASSWORD}")
    
    try:
        # Crear instancia del mailbox
        mailbox = DujawMailboxMejorado(EMAIL, PASSWORD)
        
        # Hacer unlock completo
        if mailbox.unlock_mailbox_completo():
            print("\n✅ Unlock completado exitosamente")
            
            # Obtener contenido del mailbox
            contenido = mailbox.obtener_contenido_mailbox()
            
            if contenido:
                print("\n✅ Contenido del mailbox obtenido")
                
                # Analizar contenido
                analisis = mailbox.analizar_contenido_mailbox(contenido)
                
                if analisis:
                    print("\n✅ Análisis completado")
                    
                    # Mostrar resumen final
                    if analisis['emails_encontrados']:
                        print(f"\n🎉 ¡Se encontraron {len(analisis['emails_encontrados'])} direcciones de email!")
                        for email in analisis['emails_encontrados']:
                            print(f"   📧 {email}")
                    else:
                        print(f"\n📭 No se encontraron correos en el buzón")
                        print(f"💡 El buzón podría estar vacío o los correos podrían cargarse dinámicamente")
                else:
                    print("\n❌ Error en el análisis")
            else:
                print("\n❌ Error obteniendo contenido del mailbox")
        else:
            print("\n❌ Error en el unlock")
            
    except Exception as e:
        print(f"\n❌ Error general: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n🏁 Proceso completado")

if __name__ == "__main__":
    main()
