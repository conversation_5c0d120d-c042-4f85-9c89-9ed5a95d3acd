#!/usr/bin/env python3
"""
Script personalizable para confirmar cambios de email automáticamente
Configura tus credenciales al inicio del script
"""

import sys
import os
import argparse
from datetime import datetime

# Agregar el directorio dujaw-email-monitor al path
sys.path.append(os.path.join(os.path.dirname(__file__), 'dujaw-email-monitor', 'src'))

from dujaw_email_monitor.src.utils import setup_logging
from dujaw_email_monitor.src.dujaw_client import DujawClient
from dujaw_email_monitor.src.email_extractor import EmailExtractor
from dujaw_email_monitor.src.link_follower import LinkFollower

# =============================================================================
# 🔧 CONFIGURACIÓN PERSONALIZABLE
# =============================================================================

# Configuración por defecto (puedes cambiar estos valores)
DEFAULT_CONFIG = {
    'email': '<EMAIL>',
    'dujaw_password': 'unlockgs2024',
    'confirmation_password': 'EMVaB#6G3',
    'log_level': 'INFO'
}

def procesar_correos_y_confirmar(email, dujaw_password, confirmation_password, log_level='INFO'):
    """
    Procesa correos y confirma automáticamente cambios de email
    
    Args:
        email: Email del buzón temporal
        dujaw_password: Password para desbloquear el buzón
        confirmation_password: Password para confirmaciones
        log_level: Nivel de logging
    """
    logger = setup_logging(log_level)
    
    print("🚀 PROCESADOR AUTOMÁTICO DE CONFIRMACIONES")
    print("=" * 60)
    print(f"📧 Email: {email}")
    print(f"🔑 Password Dujaw: {dujaw_password}")
    print(f"🔐 Password Confirmación: {confirmation_password}")
    print(f"📊 Log Level: {log_level}")
    
    try:
        # 1. Obtener correos del buzón
        print(f"\n1️⃣ Accediendo al buzón...")
        with DujawClient(email, dujaw_password) as client:
            emails = client.get_emails(force_refresh=True)
            
            if not emails:
                print(f"❌ No se encontraron correos en el buzón")
                return False
            
            print(f"✅ Encontrados {len(emails)} correos")
            
            # Mostrar resumen
            print(f"\n📊 CORREOS ENCONTRADOS:")
            for i, email_data in enumerate(emails, 1):
                sender = email_data.get('sender_name', 'N/A')
                subject = email_data.get('subject', 'N/A')
                print(f"   {i}. De: {sender} | Asunto: {subject}")
        
        # 2. Extraer links de confirmación
        print(f"\n2️⃣ Extrayendo links de confirmación...")
        extractor = EmailExtractor()
        all_links = extractor.extract_links_from_emails(emails)
        
        if not all_links:
            print(f"❌ No se encontraron links en los correos")
            return False
        
        print(f"✅ Encontrados {len(all_links)} links")
        
        # Filtrar links de confirmación
        confirmation_links = extractor.get_best_confirmation_links(all_links)
        
        if not confirmation_links:
            print(f"❌ No se encontraron links de confirmación")
            return False
        
        print(f"🎯 Encontrados {len(confirmation_links)} links de confirmación")
        
        # 3. Seguir y confirmar links
        print(f"\n3️⃣ Procesando confirmaciones...")
        confirmaciones_exitosas = 0
        
        with LinkFollower(confirmation_password) as follower:
            for i, link_data in enumerate(confirmation_links, 1):
                link = link_data['link']
                print(f"\n🔄 Procesando link {i}/{len(confirmation_links)}...")
                print(f"🔗 {link}")
                
                # Seguir link
                analysis = follower.follow_link(link)
                
                if analysis and analysis['confirmation_detected']:
                    print(f"✅ Página de confirmación detectada")
                    
                    # Intentar confirmación automática
                    confirmation_result = follower.attempt_confirmation(analysis, email)
                    
                    if confirmation_result:
                        if confirmation_result['success_detected']:
                            print(f"🎉 ¡Confirmación exitosa!")
                            confirmaciones_exitosas += 1
                        else:
                            print(f"⚠️ Confirmación enviada, resultado incierto")
                    else:
                        print(f"❌ No se pudo procesar la confirmación")
                else:
                    print(f"⚠️ No es una página de confirmación válida")
        
        # 4. Mostrar resultado final
        print(f"\n📊 RESULTADO FINAL:")
        print("=" * 40)
        print(f"📧 Email procesado: {email}")
        print(f"📊 Correos encontrados: {len(emails)}")
        print(f"🔗 Links de confirmación: {len(confirmation_links)}")
        print(f"✅ Confirmaciones exitosas: {confirmaciones_exitosas}")
        
        if confirmaciones_exitosas > 0:
            print(f"\n🎉 ¡PROCESO COMPLETADO EXITOSAMENTE!")
            print(f"✅ {confirmaciones_exitosas} confirmación(es) procesada(s)")
            return True
        else:
            print(f"\n⚠️ No se completaron confirmaciones exitosas")
            return False
    
    except Exception as e:
        logger.error(f"Error en el proceso: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Función principal con argumentos de línea de comandos"""
    parser = argparse.ArgumentParser(
        description='Procesador automático de confirmaciones de email',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Ejemplos de uso:

  # Usar configuración por defecto
  python confirmar_email_personalizado.py

  # Especificar email y passwords
  python confirmar_email_personalizado.py \\
    --email <EMAIL> \\
    --dujaw-password mipass123 \\
    --confirmation-password MiPass#456

  # Con logging detallado
  python confirmar_email_personalizado.py \\
    --email <EMAIL> \\
    --log-level DEBUG
        """
    )
    
    # Argumentos opcionales
    parser.add_argument(
        '--email',
        default=DEFAULT_CONFIG['email'],
        help=f'Email del buzón temporal (default: {DEFAULT_CONFIG["email"]})'
    )
    
    parser.add_argument(
        '--dujaw-password',
        default=DEFAULT_CONFIG['dujaw_password'],
        help=f'Password para desbloquear el buzón (default: {DEFAULT_CONFIG["dujaw_password"]})'
    )
    
    parser.add_argument(
        '--confirmation-password',
        default=DEFAULT_CONFIG['confirmation_password'],
        help=f'Password para confirmaciones (default: {DEFAULT_CONFIG["confirmation_password"]})'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default=DEFAULT_CONFIG['log_level'],
        help=f'Nivel de logging (default: {DEFAULT_CONFIG["log_level"]})'
    )
    
    args = parser.parse_args()
    
    # Ejecutar proceso
    success = procesar_correos_y_confirmar(
        email=args.email,
        dujaw_password=args.dujaw_password,
        confirmation_password=args.confirmation_password,
        log_level=args.log_level
    )
    
    if success:
        print(f"\n✅ Proceso completado exitosamente")
    else:
        print(f"\n❌ Proceso no completado")
        sys.exit(1)

if __name__ == "__main__":
    main()
