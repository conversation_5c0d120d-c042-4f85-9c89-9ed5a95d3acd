#!/bin/bash

# Dujaw API Docker Deployment Script
# This script helps deploy the Dujaw API stack using Docker Compose

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    log_success "Docker and Docker Compose are installed"
}

# Create necessary directories
create_directories() {
    log_info "Creating necessary directories..."
    
    directories=(
        "logs"
        "webhook_logs"
        "ssl"
        "grafana/dashboards"
        "grafana/datasources"
    )
    
    for dir in "${directories[@]}"; do
        mkdir -p "$dir"
        log_info "Created directory: $dir"
    done
    
    log_success "Directories created"
}

# Check required files
check_files() {
    log_info "Checking required files..."
    
    required_files=(
        "app.py"
        "dujaw_api_final.py"
        "dujaw_api_enhanced.py"
        "webhook_server.py"
        "requirements.txt"
        "docker-compose.yml"
        "Dockerfile"
        "Dockerfile.webhook"
        "nginx.conf"
        "config.json"
    )
    
    missing_files=()
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            missing_files+=("$file")
        fi
    done
    
    if [[ ${#missing_files[@]} -gt 0 ]]; then
        log_error "Missing required files:"
        for file in "${missing_files[@]}"; do
            echo "  - $file"
        done
        exit 1
    fi
    
    log_success "All required files found"
}

# Build and start services
start_services() {
    log_info "Building and starting services..."
    
    # Build images
    log_info "Building Docker images..."
    docker-compose build
    
    # Start services
    log_info "Starting services..."
    docker-compose up -d
    
    log_success "Services started"
}

# Check service health
check_health() {
    log_info "Checking service health..."
    
    # Wait a bit for services to start
    sleep 10
    
    services=(
        "dujaw-web:8000"
        "dujaw-webhook:5000"
        "dujaw-redis:6379"
    )
    
    for service in "${services[@]}"; do
        container_name=$(echo "$service" | cut -d':' -f1)
        port=$(echo "$service" | cut -d':' -f2)
        
        if docker ps | grep -q "$container_name"; then
            log_success "$container_name is running"
        else
            log_error "$container_name is not running"
        fi
    done
    
    # Test HTTP endpoints
    log_info "Testing HTTP endpoints..."
    
    if curl -f http://localhost:8000/api/status &> /dev/null; then
        log_success "Dujaw Web API is responding"
    else
        log_warning "Dujaw Web API is not responding yet (may still be starting)"
    fi
    
    if curl -f http://localhost:5000/health &> /dev/null; then
        log_success "Webhook server is responding"
    else
        log_warning "Webhook server is not responding yet (may still be starting)"
    fi
}

# Show service URLs
show_urls() {
    log_info "Service URLs:"
    echo "  🌐 Dujaw Web Interface: http://localhost:8000"
    echo "  📡 Webhook Server: http://localhost:5000"
    echo "  🔧 API Documentation: http://localhost:8000/docs"
    echo "  📊 Prometheus (if enabled): http://localhost:9090"
    echo "  📈 Grafana (if enabled): http://localhost:3000 (admin/admin123)"
    echo "  🔍 Redis: localhost:6379"
}

# Show logs
show_logs() {
    log_info "Showing service logs..."
    docker-compose logs -f
}

# Stop services
stop_services() {
    log_info "Stopping services..."
    docker-compose down
    log_success "Services stopped"
}

# Clean up (remove containers, images, volumes)
cleanup() {
    log_warning "This will remove all containers, images, and volumes!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "Cleaning up..."
        docker-compose down -v --rmi all
        docker system prune -f
        log_success "Cleanup completed"
    else
        log_info "Cleanup cancelled"
    fi
}

# Update services
update_services() {
    log_info "Updating services..."
    docker-compose pull
    docker-compose build --no-cache
    docker-compose up -d
    log_success "Services updated"
}

# Main menu
show_menu() {
    echo
    echo "🚀 Dujaw API Docker Deployment"
    echo "=============================="
    echo "1. Deploy (build and start all services)"
    echo "2. Start services"
    echo "3. Stop services"
    echo "4. Restart services"
    echo "5. Show logs"
    echo "6. Check status"
    echo "7. Update services"
    echo "8. Cleanup (remove everything)"
    echo "9. Show service URLs"
    echo "0. Exit"
    echo
}

# Main script
main() {
    echo "🎯 Dujaw API Docker Deployment Script"
    echo "====================================="
    
    # Check prerequisites
    check_docker
    
    if [[ $# -eq 0 ]]; then
        # Interactive mode
        while true; do
            show_menu
            read -p "Choose an option: " choice
            
            case $choice in
                1)
                    check_files
                    create_directories
                    start_services
                    check_health
                    show_urls
                    ;;
                2)
                    docker-compose up -d
                    log_success "Services started"
                    ;;
                3)
                    stop_services
                    ;;
                4)
                    docker-compose restart
                    log_success "Services restarted"
                    ;;
                5)
                    show_logs
                    ;;
                6)
                    check_health
                    ;;
                7)
                    update_services
                    ;;
                8)
                    cleanup
                    ;;
                9)
                    show_urls
                    ;;
                0)
                    log_info "Goodbye!"
                    exit 0
                    ;;
                *)
                    log_error "Invalid option"
                    ;;
            esac
        done
    else
        # Command line mode
        case $1 in
            deploy)
                check_files
                create_directories
                start_services
                check_health
                show_urls
                ;;
            start)
                docker-compose up -d
                ;;
            stop)
                stop_services
                ;;
            logs)
                show_logs
                ;;
            status)
                check_health
                ;;
            update)
                update_services
                ;;
            cleanup)
                cleanup
                ;;
            urls)
                show_urls
                ;;
            *)
                echo "Usage: $0 [deploy|start|stop|logs|status|update|cleanup|urls]"
                exit 1
                ;;
        esac
    fi
}

# Run main function
main "$@"
