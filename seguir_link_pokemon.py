#!/usr/bin/env python3
"""
Script para seguir el link de confirmación de Pokémon
Sigue el link encontrado en el correo y procesa la respuesta
"""

import requests
import json
from datetime import datetime
from bs4 import BeautifulSoup
from dujaw_api_final import DujawAPI

class PokemonLinkFollower(DujawAPI):
    def __init__(self):
        super().__init__()
        # Credenciales correctas
        self.email = "<EMAIL>"
        self.password = "unlockgs2024"
        self.confirmation_password = "EMVaB#6G3"  # Password de confirmación
        self.mailbox_url = f"https://dujaw.com/mailbox/{self.email}"
        
        # Link del correo de Pokémon
        self.pokemon_link = "https://club.pokemon.com/us/pokemon-trainer-club/email-change-approval/49d9fd87c3028b1267f9825"
        
        print(f"🔗 SEGUIDOR DE LINK DE POKÉMON")
        print(f"📧 Email: {self.email}")
        print(f"🔐 Password Dujaw: {self.password}")
        print(f"🔑 Password Confirmación: {self.confirmation_password}")
        print(f"🌐 Link a seguir: {self.pokemon_link}")

    def seguir_link_pokemon(self):
        """Sigue el link de confirmación de Pokémon"""
        print(f"\n🔗 SIGUIENDO LINK DE POKÉMON")
        print("=" * 50)
        
        try:
            # Hacer request al link de Pokémon
            print(f"📡 Accediendo al link...")
            response = self.session.get(self.pokemon_link, allow_redirects=True)
            
            print(f"📥 Status: {response.status_code}")
            print(f"🌐 URL final: {response.url}")
            print(f"📄 Tamaño respuesta: {len(response.text)} caracteres")
            
            # Guardar respuesta
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"pokemon_response_{timestamp}.html"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(response.text)
            
            print(f"💾 Respuesta guardada: {filename}")
            
            if response.status_code == 200:
                return self.procesar_respuesta_pokemon(response.text, response.url)
            else:
                print(f"❌ Error en la respuesta: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Error siguiendo link: {e}")
            return None

    def procesar_respuesta_pokemon(self, html_content, final_url):
        """Procesa la respuesta del link de Pokémon"""
        print(f"\n🔍 PROCESANDO RESPUESTA DE POKÉMON")
        print("=" * 40)
        
        soup = BeautifulSoup(html_content, 'html.parser')
        
        resultado = {
            'timestamp': datetime.now().isoformat(),
            'url_final': final_url,
            'titulo': '',
            'mensaje_principal': '',
            'formularios': [],
            'botones': [],
            'enlaces': [],
            'campos_input': [],
            'mensajes_error': [],
            'mensajes_exito': [],
            'requiere_confirmacion': False
        }
        
        # 1. Extraer título
        title_tag = soup.find('title')
        if title_tag:
            resultado['titulo'] = title_tag.get_text(strip=True)
            print(f"📄 Título: {resultado['titulo']}")
        
        # 2. Buscar mensajes principales
        main_content = soup.find(['main', 'div'], class_=lambda x: x and any(
            keyword in ' '.join(x).lower() for keyword in ['content', 'main', 'body', 'container']
        ))
        
        if main_content:
            texto_principal = main_content.get_text(strip=True)
            resultado['mensaje_principal'] = texto_principal[:500]  # Primeros 500 chars
            print(f"📝 Mensaje principal: {texto_principal[:200]}...")
        
        # 3. Buscar formularios
        formularios = soup.find_all('form')
        print(f"📝 Formularios encontrados: {len(formularios)}")
        
        for i, form in enumerate(formularios):
            form_info = {
                'index': i,
                'action': form.get('action', ''),
                'method': form.get('method', 'GET'),
                'inputs': []
            }
            
            # Buscar inputs en el formulario
            inputs = form.find_all(['input', 'select', 'textarea'])
            for input_elem in inputs:
                input_info = {
                    'type': input_elem.get('type', input_elem.name),
                    'name': input_elem.get('name', ''),
                    'value': input_elem.get('value', ''),
                    'placeholder': input_elem.get('placeholder', ''),
                    'required': input_elem.has_attr('required')
                }
                form_info['inputs'].append(input_info)
                resultado['campos_input'].append(input_info)
            
            resultado['formularios'].append(form_info)
            print(f"   📋 Formulario {i}: {form_info['method']} -> {form_info['action']}")
            print(f"      Inputs: {len(form_info['inputs'])}")
        
        # 4. Buscar botones
        botones = soup.find_all(['button', 'input'], type=lambda x: x in ['submit', 'button'])
        botones.extend(soup.find_all('a', class_=lambda x: x and 'button' in ' '.join(x).lower()))
        
        print(f"🔘 Botones encontrados: {len(botones)}")
        
        for boton in botones:
            texto = boton.get_text(strip=True) or boton.get('value', '')
            href = boton.get('href', '')
            
            boton_info = {
                'texto': texto,
                'tipo': boton.name,
                'href': href,
                'onclick': boton.get('onclick', '')
            }
            
            resultado['botones'].append(boton_info)
            print(f"   🔘 {texto} ({boton.name})")
        
        # 5. Buscar enlaces importantes
        enlaces = soup.find_all('a', href=True)
        enlaces_importantes = []
        
        for enlace in enlaces:
            href = enlace.get('href', '')
            texto = enlace.get_text(strip=True)
            
            # Filtrar enlaces importantes
            if any(keyword in texto.lower() for keyword in 
                   ['confirm', 'approve', 'reject', 'cancel', 'continue', 'next', 'submit']):
                enlace_info = {
                    'texto': texto,
                    'href': href,
                    'absoluto': href if href.startswith('http') else f"https://club.pokemon.com{href}"
                }
                enlaces_importantes.append(enlace_info)
                resultado['enlaces'].append(enlace_info)
        
        print(f"🔗 Enlaces importantes: {len(enlaces_importantes)}")
        for enlace in enlaces_importantes:
            print(f"   🔗 {enlace['texto']} -> {enlace['href']}")
        
        # 6. Buscar mensajes de error o éxito
        error_selectors = [
            '.error', '.alert-danger', '.alert-error', '[class*="error"]',
            '.warning', '.alert-warning', '[class*="warning"]'
        ]
        
        for selector in error_selectors:
            elementos = soup.select(selector)
            for elem in elementos:
                texto = elem.get_text(strip=True)
                if texto:
                    resultado['mensajes_error'].append(texto)
        
        success_selectors = [
            '.success', '.alert-success', '[class*="success"]',
            '.confirmation', '.alert-info', '[class*="confirm"]'
        ]
        
        for selector in success_selectors:
            elementos = soup.select(selector)
            for elem in elementos:
                texto = elem.get_text(strip=True)
                if texto:
                    resultado['mensajes_exito'].append(texto)
        
        # 7. Determinar si requiere confirmación
        if any(keyword in html_content.lower() for keyword in 
               ['password', 'confirm', 'verify', 'approve', 'authentication']):
            resultado['requiere_confirmacion'] = True
            print(f"🔐 Requiere confirmación adicional")
        
        # 8. Buscar campos de password específicamente
        password_inputs = soup.find_all('input', type='password')
        if password_inputs:
            print(f"🔐 Campos de password encontrados: {len(password_inputs)}")
            for pwd_input in password_inputs:
                name = pwd_input.get('name', '')
                placeholder = pwd_input.get('placeholder', '')
                print(f"   🔑 {name}: {placeholder}")
        
        return resultado

    def intentar_confirmacion(self, resultado):
        """Intenta realizar la confirmación si es necesario"""
        print(f"\n🔐 INTENTANDO CONFIRMACIÓN")
        print("=" * 30)
        
        if not resultado['requiere_confirmacion']:
            print("ℹ️ No se requiere confirmación adicional")
            return None
        
        # Buscar formulario de confirmación
        formulario_confirmacion = None
        for form in resultado['formularios']:
            # Buscar formulario que tenga campo de password
            tiene_password = any(inp['type'] == 'password' for inp in form['inputs'])
            if tiene_password:
                formulario_confirmacion = form
                break
        
        if not formulario_confirmacion:
            print("❌ No se encontró formulario de confirmación")
            return None
        
        print(f"📋 Formulario de confirmación encontrado")
        print(f"   Action: {formulario_confirmacion['action']}")
        print(f"   Method: {formulario_confirmacion['method']}")
        
        # Preparar datos del formulario
        form_data = {}
        
        for input_info in formulario_confirmacion['inputs']:
            name = input_info['name']
            input_type = input_info['type']
            
            if input_type == 'password':
                # Usar el password de confirmación
                form_data[name] = self.confirmation_password
                print(f"   🔑 {name}: {self.confirmation_password}")
            elif input_type == 'hidden':
                # Mantener valores hidden
                form_data[name] = input_info['value']
                print(f"   🔒 {name}: {input_info['value']}")
            elif input_type in ['text', 'email']:
                # Campos de texto, usar email si es apropiado
                if 'email' in name.lower():
                    form_data[name] = self.email
                    print(f"   📧 {name}: {self.email}")
                else:
                    form_data[name] = input_info['value'] or ''
                    print(f"   📝 {name}: {form_data[name]}")
        
        # Determinar URL de envío
        action = formulario_confirmacion['action']
        if action.startswith('/'):
            submit_url = f"https://club.pokemon.com{action}"
        elif action.startswith('http'):
            submit_url = action
        else:
            submit_url = f"https://club.pokemon.com/us/pokemon-trainer-club/{action}"
        
        print(f"📤 Enviando confirmación a: {submit_url}")
        
        try:
            # Enviar formulario
            method = formulario_confirmacion['method'].upper()
            
            if method == 'POST':
                response = self.session.post(submit_url, data=form_data, allow_redirects=True)
            else:
                response = self.session.get(submit_url, params=form_data, allow_redirects=True)
            
            print(f"📥 Respuesta: {response.status_code}")
            print(f"🌐 URL final: {response.url}")
            
            # Guardar respuesta de confirmación
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"pokemon_confirmacion_{timestamp}.html"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(response.text)
            
            print(f"💾 Respuesta de confirmación guardada: {filename}")
            
            # Analizar respuesta
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Buscar mensajes de éxito o error
                if 'success' in response.text.lower() or 'approved' in response.text.lower():
                    print("✅ ¡Confirmación exitosa!")
                elif 'error' in response.text.lower() or 'invalid' in response.text.lower():
                    print("❌ Error en la confirmación")
                else:
                    print("⚠️ Respuesta ambigua")
                
                return {
                    'status_code': response.status_code,
                    'url_final': response.url,
                    'contenido': response.text[:500],
                    'filename': filename
                }
            else:
                print(f"❌ Error HTTP: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Error enviando confirmación: {e}")
            return None

    def proceso_completo(self):
        """Proceso completo: unlock, seguir link, confirmar"""
        print(f"\n🚀 PROCESO COMPLETO DE SEGUIMIENTO DE LINK")
        print("=" * 60)
        
        # 1. Unlock del mailbox
        print(f"\n1️⃣ Unlock del mailbox...")
        if not self.unlock_mailbox():
            print("❌ Error en unlock")
            return None
        
        # 2. Seguir link de Pokémon
        print(f"\n2️⃣ Siguiendo link de Pokémon...")
        resultado = self.seguir_link_pokemon()
        
        if not resultado:
            print("❌ Error siguiendo link")
            return None
        
        # 3. Mostrar información encontrada
        print(f"\n3️⃣ Información encontrada:")
        print(f"   📄 Título: {resultado['titulo']}")
        print(f"   📝 Formularios: {len(resultado['formularios'])}")
        print(f"   🔘 Botones: {len(resultado['botones'])}")
        print(f"   🔗 Enlaces: {len(resultado['enlaces'])}")
        print(f"   🔐 Requiere confirmación: {resultado['requiere_confirmacion']}")
        
        # 4. Intentar confirmación si es necesario
        if resultado['requiere_confirmacion']:
            print(f"\n4️⃣ Intentando confirmación...")
            confirmacion = self.intentar_confirmacion(resultado)
            
            if confirmacion:
                resultado['confirmacion'] = confirmacion
        
        # 5. Guardar resultado completo
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        resultado_filename = f"pokemon_proceso_completo_{timestamp}.json"
        
        with open(resultado_filename, 'w', encoding='utf-8') as f:
            json.dump(resultado, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Resultado completo guardado: {resultado_filename}")
        
        return resultado

def main():
    """Función principal"""
    print("🔗 SEGUIDOR DE LINK DE POKÉMON")
    print("=" * 50)
    
    try:
        follower = PokemonLinkFollower()
        resultado = follower.proceso_completo()
        
        if resultado:
            print(f"\n🎉 ¡Proceso completado exitosamente!")
            
            if 'confirmacion' in resultado:
                print(f"✅ Confirmación realizada")
            else:
                print(f"ℹ️ No se requirió confirmación adicional")
        else:
            print(f"\n❌ Error en el proceso")
            
    except Exception as e:
        print(f"❌ Error general: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n🏁 Proceso completado")

if __name__ == "__main__":
    main()
