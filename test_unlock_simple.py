#!/usr/bin/env python3
"""
Test simple para verificar unlock
"""

import requests
from bs4 import BeautifulSoup

def test_unlock():
    """Test simple de unlock"""
    
    EMAIL = "<EMAIL>"  # Email exacto del sistema
    PASSWORD = "unlockgs2024"
    
    print(f"🧪 TEST SIMPLE DE UNLOCK")
    print(f"📧 Email: {EMAIL}")
    print(f"🔐 Password: {PASSWORD}")
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    mailbox_url = f"https://dujaw.com/mailbox/{EMAIL}"
    unlock_url = "https://dujaw.com/unlock"
    
    try:
        # Paso 1: Acceder al mailbox
        print(f"\n1️⃣ Accediendo al mailbox...")
        response = session.get(mailbox_url)
        print(f"Status: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ Error: {response.status_code}")
            return
        
        # Verificar si ya está desbloqueado
        if 'unlock' not in response.text.lower():
            print("✅ Ya está desbloqueado!")
            return
        
        # Paso 2: Obtener token CSRF
        print(f"\n2️⃣ Obteniendo token CSRF...")
        soup = BeautifulSoup(response.text, 'html.parser')
        csrf_input = soup.find('input', {'name': '_token'})
        
        if not csrf_input:
            print("❌ No se encontró token CSRF")
            return
        
        csrf_token = csrf_input.get('value')
        print(f"Token: {csrf_token[:20]}...")
        
        # Paso 3: Enviar unlock
        print(f"\n3️⃣ Enviando unlock...")
        form_data = {
            '_token': csrf_token,
            'password': PASSWORD
        }
        
        response = session.post(unlock_url, data=form_data, allow_redirects=False)
        print(f"Status POST: {response.status_code}")
        
        # Verificar headers de respuesta
        print(f"Headers:")
        for key, value in response.headers.items():
            print(f"  {key}: {value}")
        
        # Paso 4: Seguir redirect si existe
        if response.status_code == 302:
            print(f"\n4️⃣ Siguiendo redirect...")
            location = response.headers.get('location', '')
            print(f"Redirect a: {location}")
            
            if location:
                if location.startswith('/'):
                    location = 'https://dujaw.com' + location
                
                final_response = session.get(location)
                print(f"Status final: {final_response.status_code}")
                
                # Verificar si el unlock fue exitoso
                if 'unlock' not in final_response.text.lower():
                    print("✅ UNLOCK EXITOSO!")
                    
                    # Guardar HTML para análisis
                    with open('mailbox_desbloqueado.html', 'w', encoding='utf-8') as f:
                        f.write(final_response.text)
                    print("💾 HTML guardado en: mailbox_desbloqueado.html")
                    
                    # Buscar indicadores de correos
                    if '@' in final_response.text:
                        print("📧 Se encontraron direcciones de email")
                    
                    if 'message' in final_response.text.lower():
                        print("📬 Se encontraron referencias a mensajes")
                    
                    if 'livewire' in final_response.text.lower():
                        print("🧩 Se encontró Livewire")
                    
                else:
                    print("❌ Unlock falló")
                    
                    # Buscar mensaje de error
                    if 'invalid password' in final_response.text.lower():
                        print("🔐 Password inválido")
                    
                    # Guardar para debug
                    with open('unlock_failed.html', 'w', encoding='utf-8') as f:
                        f.write(final_response.text)
                    print("💾 HTML de error guardado en: unlock_failed.html")
        
        elif response.status_code == 200:
            print(f"\n4️⃣ Respuesta directa...")
            if 'unlock' not in response.text.lower():
                print("✅ UNLOCK EXITOSO!")
            else:
                print("❌ Unlock falló")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_unlock()
