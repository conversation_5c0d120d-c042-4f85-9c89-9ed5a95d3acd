#!/usr/bin/env python3
"""
Script para obtener correos replicando las llamadas exactas de Livewire
Basado en el análisis del HTML real del sitio
"""

import requests
import json
import time
from datetime import datetime
from dujaw_api_final import DujawAPI


class LivewireRealFetcher(DujawAPI):
    def __init__(self, email=None):
        super().__init__()
        if email:
            self.mailbox_url = f"https://dujaw.com/mailbox/{email}"
            self.target_url = self.mailbox_url
        
    def extraer_datos_livewire_del_html(self):
        """Extrae los datos exactos de Livewire del HTML"""
        print("🔍 Extrayendo datos exactos de Livewire...")
        
        response = self.session.get(self.mailbox_url)
        if response.status_code != 200:
            print(f"❌ Error accediendo mailbox: {response.status_code}")
            return None
            
        html_content = response.text
        
        # Buscar el componente frontend.app que maneja los mensajes
        import re
        
        # Buscar el wire:initial-data del componente frontend.app
        app_pattern = r'wire:id="([^"]+)"\s+wire:initial-data="([^"]+)"[^>]*'

        # Buscar todas las coincidencias y filtrar por frontend.app
        app_match = None
        for match in re.finditer(app_pattern, html_content):
            wire_id = match.group(1)
            initial_data_encoded = match.group(2)

            # Verificar si contiene frontend.app
            if 'frontend.app' in initial_data_encoded:
                app_match = match
                break
        
        if app_match:
            wire_id = app_match.group(1)
            initial_data_encoded = app_match.group(2)
            
            # Decodificar HTML entities
            import html
            initial_data_json = html.unescape(initial_data_encoded)
            
            try:
                initial_data = json.loads(initial_data_json)
                print(f"✅ Datos del componente frontend.app encontrados")
                print(f"   Wire ID: {wire_id}")
                print(f"   Email: {initial_data['serverMemo']['data']['email']}")
                print(f"   Messages: {len(initial_data['serverMemo']['data']['messages'])}")
                
                return {
                    'wire_id': wire_id,
                    'initial_data': initial_data,
                    'component_name': 'frontend.app'
                }
                
            except json.JSONDecodeError as e:
                print(f"❌ Error decodificando JSON: {e}")
                print(f"   JSON: {initial_data_json[:200]}...")
                
        else:
            print("❌ No se encontró el componente frontend.app")
            
        return None
        
    def hacer_llamada_fetchMessages(self, livewire_data):
        """Hace la llamada exacta fetchMessages como lo hace el JavaScript"""
        print("🔄 Haciendo llamada fetchMessages...")
        
        wire_id = livewire_data['wire_id']
        initial_data = livewire_data['initial_data']
        
        # Construir la petición exacta como la hace Livewire
        livewire_request = {
            'fingerprint': initial_data['fingerprint'],
            'serverMemo': initial_data['serverMemo'],
            'updates': [
                {
                    'type': 'fireEvent',
                    'payload': {
                        'id': wire_id,
                        'event': 'fetchMessages',
                        'params': []
                    }
                }
            ]
        }
        
        # Headers exactos como los usa Livewire
        headers = {
            'X-Livewire': 'true',
            'X-CSRF-TOKEN': self.csrf_token,
            'Content-Type': 'application/json',
            'Accept': 'text/html, application/xhtml+xml',
            'X-Requested-With': 'XMLHttpRequest',
            'Referer': self.mailbox_url
        }
        
        # URL de Livewire
        livewire_url = f"{self.base_url}/livewire/message/{initial_data['fingerprint']['name']}"
        
        try:
            print(f"🔄 POST a: {livewire_url}")
            response = self.session.post(livewire_url, json=livewire_request, headers=headers)
            
            print(f"📥 Respuesta: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ Respuesta JSON exitosa")
                    
                    # Verificar si hay mensajes en la respuesta
                    if 'effects' in data and 'html' in data['effects']:
                        print(f"📧 HTML actualizado recibido")
                        return data
                    elif 'serverMemo' in data and 'data' in data['serverMemo']:
                        server_data = data['serverMemo']['data']
                        if 'messages' in server_data:
                            messages = server_data['messages']
                            print(f"📧 Mensajes encontrados: {len(messages)}")
                            return data
                            
                    print(f"📊 Estructura de respuesta: {list(data.keys())}")
                    return data
                    
                except json.JSONDecodeError:
                    print(f"⚠️ Respuesta no es JSON: {response.text[:200]}...")
                    return response.text
            else:
                print(f"❌ Error: {response.status_code}")
                print(f"   Respuesta: {response.text[:200]}...")
                
        except Exception as e:
            print(f"❌ Error en llamada: {e}")
            
        return None
        
    def hacer_llamada_syncEmail(self, livewire_data):
        """Hace la llamada syncEmail"""
        print("🔄 Haciendo llamada syncEmail...")
        
        wire_id = livewire_data['wire_id']
        initial_data = livewire_data['initial_data']
        email = initial_data['serverMemo']['data']['email']
        
        livewire_request = {
            'fingerprint': initial_data['fingerprint'],
            'serverMemo': initial_data['serverMemo'],
            'updates': [
                {
                    'type': 'fireEvent',
                    'payload': {
                        'id': wire_id,
                        'event': 'syncEmail',
                        'params': [email]
                    }
                }
            ]
        }
        
        headers = {
            'X-Livewire': 'true',
            'X-CSRF-TOKEN': self.csrf_token,
            'Content-Type': 'application/json',
            'Accept': 'text/html, application/xhtml+xml',
            'X-Requested-With': 'XMLHttpRequest',
            'Referer': self.mailbox_url
        }
        
        livewire_url = f"{self.base_url}/livewire/message/{initial_data['fingerprint']['name']}"
        
        try:
            response = self.session.post(livewire_url, json=livewire_request, headers=headers)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ syncEmail exitoso")
                    return data
                except:
                    return response.text
                    
        except Exception as e:
            print(f"❌ Error en syncEmail: {e}")
            
        return None
        
    def obtener_correos_completo(self):
        """Método principal para obtener correos usando Livewire real"""
        print("📧 OBTENIENDO CORREOS VÍA LIVEWIRE REAL")
        print("=" * 60)
        
        if not self.unlock_mailbox():
            print("❌ Error en unlock")
            return None
            
        # Extraer datos de Livewire del HTML
        livewire_data = self.extraer_datos_livewire_del_html()
        if not livewire_data:
            print("❌ No se pudieron extraer datos de Livewire")
            return None
            
        resultados = {
            'timestamp': datetime.now().isoformat(),
            'livewire_data': livewire_data,
            'sync_response': None,
            'fetch_responses': []
        }
        
        # 1. Hacer syncEmail primero (como hace el JavaScript)
        print("\n1️⃣ Ejecutando syncEmail...")
        sync_response = self.hacer_llamada_syncEmail(livewire_data)
        if sync_response:
            resultados['sync_response'] = sync_response
            print("✅ syncEmail completado")
        else:
            print("❌ syncEmail falló")
            
        # 2. Hacer fetchMessages múltiples veces (como el polling)
        print("\n2️⃣ Ejecutando fetchMessages...")
        
        for intento in range(5):
            print(f"\n🔄 Intento {intento + 1}/5 - {datetime.now().strftime('%H:%M:%S')}")
            
            fetch_response = self.hacer_llamada_fetchMessages(livewire_data)
            if fetch_response:
                resultados['fetch_responses'].append({
                    'intento': intento + 1,
                    'timestamp': datetime.now().isoformat(),
                    'data': fetch_response
                })
                
                # Verificar si encontramos mensajes
                if isinstance(fetch_response, dict):
                    if self.tiene_mensajes(fetch_response):
                        print(f"🎉 ¡Mensajes encontrados en intento {intento + 1}!")
                        break
                        
            # Esperar entre intentos (como el polling real)
            if intento < 4:
                print("⏳ Esperando 3 segundos...")
                time.sleep(3)
                
        return resultados
        
    def tiene_mensajes(self, response_data):
        """Verifica si la respuesta contiene mensajes"""
        if not isinstance(response_data, dict):
            return False
            
        # Buscar mensajes en serverMemo
        if 'serverMemo' in response_data and 'data' in response_data['serverMemo']:
            server_data = response_data['serverMemo']['data']
            if 'messages' in server_data and len(server_data['messages']) > 0:
                return True
                
        # Buscar mensajes en effects
        if 'effects' in response_data:
            effects = response_data['effects']
            if 'html' in effects and len(effects['html']) > 100:
                # Si hay HTML significativo, podría contener mensajes
                return True
                
        return False
        
    def extraer_mensajes_de_respuesta(self, response_data):
        """Extrae mensajes de la respuesta de Livewire"""
        mensajes = []
        
        if not isinstance(response_data, dict):
            return mensajes
            
        # Extraer de serverMemo
        if 'serverMemo' in response_data and 'data' in response_data['serverMemo']:
            server_data = response_data['serverMemo']['data']
            if 'messages' in server_data:
                mensajes.extend(server_data['messages'])
                
        # Extraer de effects.html si existe
        if 'effects' in response_data and 'html' in response_data['effects']:
            html_content = response_data['effects']['html']
            # Aquí podrías parsear el HTML para extraer mensajes
            # Por ahora, guardamos el HTML para análisis manual
            mensajes.append({
                'type': 'html_update',
                'content': html_content
            })
            
        return mensajes
        
    def mostrar_resultados(self, resultados):
        """Muestra los resultados obtenidos"""
        print(f"\n📧 RESULTADOS DE LIVEWIRE REAL")
        print("=" * 50)
        
        if not resultados:
            print("❌ No se obtuvieron resultados")
            return
            
        # Mostrar información del componente
        livewire_data = resultados.get('livewire_data', {})
        print(f"🧩 Componente: {livewire_data.get('component_name', 'N/A')}")
        print(f"🆔 Wire ID: {livewire_data.get('wire_id', 'N/A')}")
        
        # Mostrar respuesta de syncEmail
        sync_response = resultados.get('sync_response')
        if sync_response:
            print(f"\n✅ syncEmail ejecutado correctamente")
        else:
            print(f"\n❌ syncEmail falló")
            
        # Mostrar respuestas de fetchMessages
        fetch_responses = resultados.get('fetch_responses', [])
        print(f"\n📥 fetchMessages: {len(fetch_responses)} respuestas")
        
        total_mensajes = 0
        
        for i, fetch_resp in enumerate(fetch_responses, 1):
            print(f"\n  📧 Respuesta {i}:")
            print(f"     Timestamp: {fetch_resp['timestamp']}")
            
            data = fetch_resp['data']
            mensajes = self.extraer_mensajes_de_respuesta(data)
            total_mensajes += len(mensajes)
            
            print(f"     Mensajes: {len(mensajes)}")
            
            if mensajes:
                for j, mensaje in enumerate(mensajes, 1):
                    if isinstance(mensaje, dict):
                        if mensaje.get('type') == 'html_update':
                            print(f"       {j}. HTML Update ({len(mensaje['content'])} chars)")
                        else:
                            print(f"       {j}. {str(mensaje)[:100]}...")
                    else:
                        print(f"       {j}. {str(mensaje)[:100]}...")
                        
        print(f"\n📊 RESUMEN:")
        print(f"   Total mensajes encontrados: {total_mensajes}")
        
        # Guardar resultados
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"correos_livewire_real_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(resultados, f, indent=2, ensure_ascii=False)
            
        print(f"💾 Resultados guardados en: {filename}")
        
        if total_mensajes > 0:
            print(f"\n🎉 ¡Se encontraron {total_mensajes} mensajes!")
        else:
            print(f"\n😞 No se encontraron mensajes")
            print("💡 Los mensajes podrían estar cargándose aún o requerir más tiempo")


def main():
    """Función principal"""
    print("🚀 OBTENER CORREOS VÍA LIVEWIRE REAL")
    print("=" * 60)
    print("📧 Replicando las llamadas exactas del JavaScript del sitio")
    print("=" * 60)

    # Usar el nuevo mailbox con credenciales actualizadas
    email = "<EMAIL>"
    print(f"📧 Mailbox: {email}")
    print("=" * 60)

    fetcher = LivewireRealFetcher(email)
    
    try:
        resultados = fetcher.obtener_correos_completo()
        fetcher.mostrar_resultados(resultados)
        
    except Exception as e:
        print(f"❌ Error general: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        fetcher.logout()
        
    print(f"\n🏁 Proceso completado")


if __name__ == "__main__":
    main()
