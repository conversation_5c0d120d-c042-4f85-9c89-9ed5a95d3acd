#!/usr/bin/env python3
"""
Script para verificar directamente si las confirmaciones fueron exitosas
"""

import requests
from bs4 import BeautifulSoup

def verificar_link_confirmacion(link):
    """Verifica el estado de un link de confirmación"""
    print(f"🔍 Verificando: {link}")
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    })
    
    try:
        response = session.get(link)
        print(f"📥 Status: {response.status_code}")
        print(f"🌐 URL final: {response.url}")
        
        if response.status_code == 200:
            # Verificar si aún muestra formulario de confirmación
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Buscar formulario de password
            password_form = soup.find('input', {'type': 'password'})
            
            # Buscar botones de confirmación
            confirm_buttons = soup.find_all('input', {'type': 'submit'})
            confirm_buttons.extend(soup.find_all('button'))
            
            # Buscar mensajes de estado
            html_lower = response.text.lower()
            
            if password_form:
                print(f"📋 Estado: FORMULARIO ACTIVO - Aún requiere confirmación")
                
                # Mostrar información del formulario
                form = password_form.find_parent('form')
                if form:
                    print(f"📧 Información del formulario:")
                    for input_elem in form.find_all('input'):
                        name = input_elem.get('name', 'N/A')
                        input_type = input_elem.get('type', 'text')
                        value = input_elem.get('value', '')
                        
                        if input_type == 'email' or 'email' in name.lower():
                            print(f"   📧 {name}: {value}")
                        elif input_type == 'password':
                            print(f"   🔑 {name}: [campo de password]")
                        elif input_type == 'submit':
                            print(f"   🔘 {name}: {value}")
                
                return "PENDING"
            
            elif 'could not be completed' in html_lower:
                print(f"⚠️ Estado: ERROR - 'Could not be completed'")
                return "ERROR"
            
            elif 'expired' in html_lower or 'no longer valid' in html_lower:
                print(f"❌ Estado: EXPIRADO - Link ya no válido")
                return "EXPIRED"
            
            elif response.url != link:
                print(f"✅ Estado: PROCESADO - Redirección detectada")
                print(f"   Original: {link}")
                print(f"   Redirigido: {response.url}")
                return "COMPLETED"
            
            else:
                print(f"❓ Estado: INCIERTO - Revisar manualmente")
                return "UNKNOWN"
        
        elif response.status_code == 404:
            print(f"❌ Estado: NO ENCONTRADO - Link inválido")
            return "NOT_FOUND"
        
        else:
            print(f"❌ Estado: ERROR HTTP {response.status_code}")
            return "HTTP_ERROR"
    
    except Exception as e:
        print(f"❌ Error: {e}")
        return "EXCEPTION"
    
    finally:
        session.close()

def main():
    """Función principal"""
    print("🔍 VERIFICADOR DE CONFIRMACIONES DE POKÉMON")
    print("=" * 60)
    
    # Links encontrados por el sistema avanzado
    links = [
        "https://club.pokemon.com/us/pokemon-trainer-club/email-change-approval/fd16d88247485afafddc15754ca7331e",
        "https://club.pokemon.com/us/pokemon-trainer-club/email-change-approval/f1f8d062f900a1b316810989ff178ec3"
    ]
    
    print(f"📊 Verificando {len(links)} links de confirmación...")
    
    resultados = {}
    
    for i, link in enumerate(links, 1):
        print(f"\n{i}️⃣ Link {i}/{len(links)}:")
        print("-" * 40)
        
        estado = verificar_link_confirmacion(link)
        resultados[link] = estado
    
    # Resumen final
    print(f"\n📊 RESUMEN FINAL:")
    print("=" * 40)
    
    estados_count = {}
    for link, estado in resultados.items():
        estados_count[estado] = estados_count.get(estado, 0) + 1
        print(f"🔗 {link}")
        print(f"   Estado: {estado}")
    
    print(f"\n📈 ESTADÍSTICAS:")
    for estado, count in estados_count.items():
        emoji = {
            'COMPLETED': '✅',
            'PENDING': '📋',
            'ERROR': '⚠️',
            'EXPIRED': '❌',
            'UNKNOWN': '❓',
            'NOT_FOUND': '❌',
            'HTTP_ERROR': '❌',
            'EXCEPTION': '❌'
        }.get(estado, '❓')
        
        print(f"   {emoji} {estado}: {count}")
    
    # Determinar resultado general
    if 'COMPLETED' in estados_count:
        print(f"\n🎉 ¡AL MENOS UNA CONFIRMACIÓN COMPLETADA!")
    elif 'PENDING' in estados_count:
        print(f"\n📋 Confirmaciones pendientes - Requieren acción manual")
    else:
        print(f"\n❌ No se detectaron confirmaciones exitosas")

if __name__ == "__main__":
    main()
