#!/usr/bin/env python3
"""
Script para obtener correos haciendo llamadas directas a Livewire
Basado en el análisis de los elementos wire: encontrados
"""

import requests
import json
import re
from datetime import datetime
from bs4 import BeautifulSoup
from dujaw_api_final import DujawAPI


class LivewireEmailFetcher(DujawAPI):
    def __init__(self):
        super().__init__()
        # Actualizar credenciales correctas
        self.email = "<EMAIL>"
        self.password = "unlockgs2024"
        self.mailbox_url = f"https://dujaw.com/mailbox/{self.email}"
        
    def extraer_datos_livewire(self):
        """Extrae datos de Livewire del HTML"""
        print("🔍 Extrayendo datos de Livewire...")

        response = self.session.get(self.mailbox_url)
        if response.status_code != 200:
            print(f"❌ Error accediendo mailbox: {response.status_code}")
            return None

        # Buscar componentes Livewire usando regex (más confiable)
        pattern = r'wire:id="([^"]+)"\s+wire:initial-data="([^"]+)"'
        matches = re.findall(pattern, response.text)

        livewire_data = []

        for wire_id, wire_initial_data_encoded in matches:
            try:
                # Decodificar HTML entities
                import html
                wire_initial_data = html.unescape(wire_initial_data_encoded)

                # Decodificar JSON
                initial_data = json.loads(wire_initial_data)

                component_name = initial_data.get('fingerprint', {}).get('name', 'unknown')

                livewire_data.append({
                    'wire_id': wire_id,
                    'component_name': component_name,
                    'initial_data': initial_data,
                    'encoded_data': wire_initial_data_encoded[:100] + "..."
                })

                print(f"✅ Componente Livewire: {component_name} (ID: {wire_id})")

                # Mostrar datos del servidor si están disponibles
                if 'serverMemo' in initial_data and 'data' in initial_data['serverMemo']:
                    server_data = initial_data['serverMemo']['data']
                    print(f"   📊 Datos del servidor: {json.dumps(server_data, indent=2)[:200]}...")

            except (json.JSONDecodeError, Exception) as e:
                print(f"❌ Error decodificando datos de {wire_id}: {e}")

        return livewire_data
        
    def hacer_llamada_livewire(self, wire_data):
        """Hace una llamada a Livewire para obtener mensajes"""
        component_name = wire_data['component_name']
        wire_id = wire_data['wire_id']

        print(f"🔄 Haciendo llamada Livewire para {component_name} (ID: {wire_id})")

        initial_data = wire_data['initial_data']
        fingerprint = initial_data.get('fingerprint', {})
        server_memo = initial_data.get('serverMemo', {})

        # Métodos específicos según el componente
        metodos_por_componente = {
            'frontend.app': ['fetchMessages', 'syncEmail', 'getMessages'],
            'frontend.actions': ['syncEmail', 'fetchMessages'],
            'frontend.nav': ['refresh']
        }

        metodos = metodos_por_componente.get(component_name, ['fetchMessages'])

        # Headers para Livewire
        headers = {
            'X-Livewire': 'true',
            'X-CSRF-TOKEN': self.csrf_token,
            'Content-Type': 'application/json',
            'Accept': 'text/html, application/xhtml+xml',
            'X-Requested-With': 'XMLHttpRequest',
            'Referer': self.mailbox_url
        }

        # URL específica para el componente
        livewire_url = f"{self.base_url}/livewire/message/{component_name}"

        for metodo in metodos:
            try:
                print(f"   🔄 Probando método: {metodo}")

                # Construir la petición Livewire
                livewire_request = {
                    'fingerprint': fingerprint,
                    'serverMemo': server_memo,
                    'updates': [
                        {
                            'type': 'fireEvent',
                            'payload': {
                                'id': wire_id,
                                'event': metodo,
                                'params': [self.email] if metodo == 'syncEmail' else []
                            }
                        }
                    ]
                }

                response = self.session.post(livewire_url, json=livewire_request, headers=headers)

                print(f"   📥 Respuesta {metodo}: {response.status_code}")

                if response.status_code == 200:
                    try:
                        data = response.json()
                        print(f"   ✅ JSON exitoso para {metodo}: {len(str(data))} chars")

                        # Verificar si hay datos útiles
                        if self.verificar_datos_utiles(data):
                            print(f"   🎉 ¡Datos útiles encontrados con {metodo}!")
                            return data

                    except json.JSONDecodeError:
                        print(f"   ⚠️ Respuesta no JSON para {metodo}")

                elif response.status_code == 500:
                    print(f"   ❌ Error 500 para {metodo} - método no válido")
                else:
                    print(f"   ❌ Error {response.status_code} para {metodo}")

            except Exception as e:
                print(f"   ❌ Error en {metodo}: {e}")

        return None

    def verificar_datos_utiles(self, data):
        """Verifica si los datos contienen información útil"""
        if not isinstance(data, dict):
            return False

        # Verificar en serverMemo
        if 'serverMemo' in data and 'data' in data['serverMemo']:
            server_data = data['serverMemo']['data']

            # Verificar si hay mensajes
            if 'messages' in server_data and server_data['messages']:
                return True

            # Verificar si hay cambios en el estado
            if 'error' in server_data and server_data['error']:
                return True

        # Verificar en effects
        if 'effects' in data and data['effects']:
            return True

        return False
        
    def probar_otros_metodos_livewire(self, wire_data):
        """Prueba otros métodos de Livewire"""
        print("🔄 Probando otros métodos de Livewire...")
        
        metodos_a_probar = [
            'getMessages',
            'loadMessages',
            'refreshMessages',
            'fetchEmails',
            'getEmails',
            'loadEmails',
            'refreshEmails',
            'getInbox',
            'loadInbox',
            'refreshInbox'
        ]
        
        initial_data = wire_data['initial_data']
        fingerprint = initial_data.get('fingerprint', {})
        server_memo = initial_data.get('serverMemo', {})
        
        headers = {
            'X-Livewire': 'true',
            'X-CSRF-TOKEN': self.csrf_token,
            'Content-Type': 'application/json',
            'Accept': 'text/html, application/xhtml+xml',
            'X-Requested-With': 'XMLHttpRequest'
        }
        
        url = f"{self.base_url}/livewire/update"
        
        for metodo in metodos_a_probar:
            try:
                livewire_request = {
                    'fingerprint': fingerprint,
                    'serverMemo': server_memo,
                    'updates': [
                        {
                            'type': 'callMethod',
                            'payload': {
                                'method': metodo,
                                'params': []
                            }
                        }
                    ]
                }
                
                print(f"🔄 Probando método: {metodo}")
                response = self.session.post(url, json=livewire_request, headers=headers)
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        if data and 'effects' in data:
                            print(f"✅ Método {metodo} exitoso!")
                            return data
                    except:
                        pass
                        
            except Exception as e:
                continue
                
        return None
        
    def buscar_endpoints_ajax_adicionales(self):
        """Busca endpoints AJAX adicionales"""
        print("🔍 Buscando endpoints AJAX adicionales...")
        
        # Endpoints específicos para sistemas de email temporal
        endpoints_especificos = [
            f"/mailbox/{self.mailbox_url.split('/')[-1]}",
            f"/api/mailbox/{self.mailbox_url.split('/')[-1]}",
            f"/fetch/{self.mailbox_url.split('/')[-1]}",
            f"/messages/{self.mailbox_url.split('/')[-1]}",
            f"/emails/{self.mailbox_url.split('/')[-1]}",
            "/api/fetch-messages",
            "/api/get-messages",
            "/fetch-messages",
            "/get-messages",
            "/check-messages",
            "/poll-messages"
        ]
        
        for endpoint in endpoints_especificos:
            try:
                full_url = f"{self.base_url}{endpoint}"
                print(f"🔍 Probando: {endpoint}")
                
                response = self.session.get(full_url)
                
                if response.status_code == 200:
                    print(f"✅ Endpoint activo: {endpoint}")
                    
                    # Verificar si contiene datos de correos
                    content_type = response.headers.get('content-type', '').lower()
                    
                    if 'json' in content_type:
                        try:
                            data = response.json()
                            if self.contiene_datos_correo(data):
                                print(f"📧 ¡Correos encontrados en {endpoint}!")
                                return data
                        except:
                            pass
                    else:
                        # Verificar si el HTML contiene información de correos
                        if self.html_contiene_correos(response.text):
                            print(f"📧 ¡HTML con correos en {endpoint}!")
                            return response.text
                            
            except Exception as e:
                continue
                
        return None
        
    def contiene_datos_correo(self, data):
        """Verifica si los datos JSON contienen información de correos"""
        data_str = str(data).lower()
        indicadores = ['message', 'email', 'subject', 'from', 'to', 'sender', 'inbox', 'mail']
        return sum(1 for ind in indicadores if ind in data_str) >= 2
        
    def html_contiene_correos(self, html):
        """Verifica si el HTML contiene correos reales"""
        soup = BeautifulSoup(html, 'html.parser')
        
        # Buscar elementos que sugieran correos reales
        email_indicators = soup.find_all(text=lambda text: text and '@' in text and '.' in text)
        subject_indicators = soup.find_all(text=lambda text: text and 'subject:' in text.lower())
        
        return len(email_indicators) > 1 or len(subject_indicators) > 0
        
    def obtener_correos_completo(self):
        """Método principal para obtener correos"""
        print("📧 OBTENIENDO CORREOS - MÉTODO COMPLETO")
        print("=" * 60)
        
        if not self.unlock_mailbox():
            print("❌ Error en unlock")
            return None
            
        resultados = {
            'timestamp': datetime.now().isoformat(),
            'livewire_data': None,
            'ajax_data': None,
            'correos_encontrados': []
        }
        
        # 1. Extraer datos de Livewire
        print("\n1️⃣ Extrayendo datos de Livewire...")
        livewire_data = self.extraer_datos_livewire()
        
        if livewire_data:
            resultados['livewire_data'] = livewire_data
            
            # Probar llamadas a Livewire
            for wire_data in livewire_data:
                print(f"\n🔄 Probando Livewire: {wire_data['wire_id']}")
                
                # Método principal
                response = self.hacer_llamada_livewire(wire_data)
                if response:
                    resultados['correos_encontrados'].append({
                        'source': 'livewire_main',
                        'wire_id': wire_data['wire_id'],
                        'data': response
                    })
                    
                # Otros métodos
                response2 = self.probar_otros_metodos_livewire(wire_data)
                if response2:
                    resultados['correos_encontrados'].append({
                        'source': 'livewire_alt',
                        'wire_id': wire_data['wire_id'],
                        'data': response2
                    })
                    
        # 2. Buscar endpoints AJAX
        print("\n2️⃣ Buscando endpoints AJAX...")
        ajax_data = self.buscar_endpoints_ajax_adicionales()
        if ajax_data:
            resultados['ajax_data'] = ajax_data
            resultados['correos_encontrados'].append({
                'source': 'ajax',
                'data': ajax_data
            })
            
        return resultados
        
    def mostrar_resultados(self, resultados):
        """Muestra los resultados encontrados"""
        print(f"\n📧 RESULTADOS DE BÚSQUEDA")
        print("=" * 40)
        
        if not resultados:
            print("❌ No se obtuvieron resultados")
            return
            
        correos = resultados.get('correos_encontrados', [])
        print(f"📊 Fuentes de datos encontradas: {len(correos)}")
        
        for i, correo in enumerate(correos, 1):
            print(f"\n📂 Fuente {i}: {correo['source'].upper()}")
            print("-" * 30)
            
            if 'wire_id' in correo:
                print(f"   Wire ID: {correo['wire_id']}")
                
            data = correo['data']
            print(f"   Tipo de datos: {type(data).__name__}")
            print(f"   Contenido: {str(data)[:200]}...")
            
            # Intentar extraer información específica de correos
            if isinstance(data, dict):
                self.analizar_datos_dict(data)
            elif isinstance(data, str):
                self.analizar_datos_string(data)
                
        # Guardar resultados
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"correos_livewire_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(resultados, f, indent=2, ensure_ascii=False)
            
        print(f"\n💾 Resultados guardados en: {filename}")
        
    def analizar_datos_dict(self, data):
        """Analiza datos en formato diccionario"""
        # Buscar claves que sugieran correos
        claves_correo = ['messages', 'emails', 'inbox', 'mail', 'items']
        
        for clave in claves_correo:
            if clave in data:
                print(f"   📧 Clave encontrada: {clave} = {data[clave]}")
                
        # Buscar en effects (común en Livewire)
        if 'effects' in data:
            effects = data['effects']
            print(f"   🔄 Effects: {effects}")
            
        # Buscar en serverMemo
        if 'serverMemo' in data:
            server_memo = data['serverMemo']
            if 'data' in server_memo:
                print(f"   📊 ServerMemo data: {server_memo['data']}")
                
    def analizar_datos_string(self, data):
        """Analiza datos en formato string"""
        # Buscar patrones de email
        import re
        emails = re.findall(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', data)
        if emails:
            print(f"   📧 Emails encontrados: {emails}")

        # Buscar patrones de subject
        subjects = re.findall(r'subject[:\s]*([^\n\r]+)', data, re.IGNORECASE)
        if subjects:
            print(f"   📝 Subjects encontrados: {subjects}")

    def extraer_links_pokemon(self, resultados):
        """Extrae links de confirmación de Pokémon de los resultados"""
        print(f"\n🔍 Buscando links de confirmación de Pokémon...")

        links_pokemon = []

        if not resultados or not resultados.get('correos_encontrados'):
            return links_pokemon

        for correo_data in resultados['correos_encontrados']:
            data = correo_data['data']

            # Convertir datos a string para buscar links
            data_str = str(data)

            # Buscar links de Pokémon
            patron_pokemon = r'https://club\.pokemon\.com/[^\s<>"\']+email-change-approval/[a-f0-9]+'
            links_encontrados = re.findall(patron_pokemon, data_str)

            for link in links_encontrados:
                if link not in links_pokemon:
                    links_pokemon.append(link)
                    print(f"✅ Link de Pokémon encontrado: {link}")

        return links_pokemon

    def confirmar_pokemon_automatico(self, pokemon_link):
        """Confirma automáticamente el cambio de email de Pokémon"""
        print(f"\n🔐 CONFIRMANDO CAMBIO DE EMAIL AUTOMÁTICAMENTE")
        print("=" * 60)
        print(f"🔗 Link: {pokemon_link}")

        try:
            # 1. Acceder a la página de confirmación
            print(f"1️⃣ Accediendo a página de confirmación...")
            response = self.session.get(pokemon_link)

            print(f"📥 Status: {response.status_code}")

            if response.status_code != 200:
                print(f"❌ Error accediendo: {response.status_code}")
                return False

            # 2. Parsear formulario
            print(f"2️⃣ Analizando formulario...")
            soup = BeautifulSoup(response.text, 'html.parser')

            # Buscar formulario de confirmación
            confirmation_form = None
            for form in soup.find_all('form'):
                inputs = form.find_all('input')
                has_password = any(inp.get('type') == 'password' for inp in inputs)
                has_email = any('email' in inp.get('name', '').lower() for inp in inputs)

                if has_password and has_email:
                    confirmation_form = form
                    break

            if not confirmation_form:
                print(f"❌ No se encontró formulario de confirmación")
                return False

            print(f"✅ Formulario encontrado")

            # 3. Extraer datos del formulario
            print(f"3️⃣ Preparando datos del formulario...")
            form_data = {}
            password_confirmacion = "EMVaB#6G3"

            for input_elem in confirmation_form.find_all('input'):
                name = input_elem.get('name')
                value = input_elem.get('value', '')
                input_type = input_elem.get('type', 'text')

                if not name:
                    continue

                if input_type == 'password':
                    form_data[name] = password_confirmacion
                    print(f"🔑 Password: {name} = {password_confirmacion}")
                elif input_type == 'hidden':
                    form_data[name] = value
                    print(f"🔒 Hidden: {name} = {value[:30]}...")
                elif input_type in ['email', 'text']:
                    form_data[name] = value
                    print(f"📧 Field: {name} = {value}")
                elif input_type == 'submit' and value:
                    form_data[name] = value
                    print(f"🔘 Submit: {name} = {value}")

            # 4. Determinar URL de envío
            action = confirmation_form.get('action', '')
            method = confirmation_form.get('method', 'POST').upper()

            if action:
                if action.startswith('http'):
                    submit_url = action
                elif action.startswith('/'):
                    submit_url = f"https://club.pokemon.com{action}"
                else:
                    submit_url = f"{pokemon_link.rstrip('/')}/{action}"
            else:
                submit_url = pokemon_link

            print(f"4️⃣ Enviando confirmación...")
            print(f"📤 URL: {submit_url}")
            print(f"📋 Método: {method}")

            # 5. Enviar confirmación
            if method == 'POST':
                confirm_response = self.session.post(submit_url, data=form_data, allow_redirects=True)
            else:
                confirm_response = self.session.get(submit_url, params=form_data, allow_redirects=True)

            print(f"5️⃣ Respuesta recibida...")
            print(f"📥 Status: {confirm_response.status_code}")
            print(f"🌐 URL final: {confirm_response.url}")

            # 6. Guardar respuesta
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"pokemon_confirmacion_{timestamp}.html"

            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"<!-- Confirmación automática - {timestamp} -->\n")
                f.write(f"<!-- URL original: {pokemon_link} -->\n")
                f.write(f"<!-- Status: {confirm_response.status_code} -->\n\n")
                f.write(confirm_response.text)

            print(f"💾 Respuesta guardada: {filename}")

            # 7. Analizar resultado
            print(f"6️⃣ Analizando resultado...")
            html_lower = confirm_response.text.lower()

            success_indicators = [
                'successfully', 'confirmed', 'approved', 'email has been changed',
                'email updated', 'change complete', 'verification successful'
            ]

            error_indicators = [
                'error', 'invalid password', 'incorrect password', 'failed',
                'expired', 'not found', 'could not be completed'
            ]

            success_found = [ind for ind in success_indicators if ind in html_lower]
            error_found = [ind for ind in error_indicators if ind in html_lower]

            print(f"🔍 Indicadores de éxito: {success_found}")
            print(f"⚠️ Indicadores de error: {error_found}")

            # Determinar resultado
            if success_found and not error_found:
                print(f"🎉 ¡CONFIRMACIÓN EXITOSA!")
                return True
            elif error_found:
                print(f"❌ Error detectado: {', '.join(error_found)}")
                return False
            elif confirm_response.status_code == 200:
                if confirm_response.url != pokemon_link:
                    print(f"✅ Redirección detectada - posible éxito")
                    return True
                else:
                    print(f"⚠️ Resultado incierto - revisar archivo guardado")
                    return False
            else:
                print(f"❌ Status HTTP no exitoso: {confirm_response.status_code}")
                return False

        except Exception as e:
            print(f"❌ Error en confirmación: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """Función principal"""
    print("🚀 OBTENER CORREOS Y CONFIRMAR POKÉMON AUTOMÁTICAMENTE")
    print("=" * 70)

    fetcher = LivewireEmailFetcher()

    try:
        # 1. Obtener correos
        resultados = fetcher.obtener_correos_completo()
        fetcher.mostrar_resultados(resultados)

        if resultados and resultados.get('correos_encontrados'):
            print(f"\n🎉 ¡Se encontraron {len(resultados['correos_encontrados'])} fuentes de datos!")

            # 2. Buscar links de Pokémon
            links_pokemon = fetcher.extraer_links_pokemon(resultados)

            if links_pokemon:
                print(f"\n🎯 ¡Encontrados {len(links_pokemon)} links de Pokémon!")

                # 3. Confirmar automáticamente cada link
                confirmaciones_exitosas = 0
                for i, link in enumerate(links_pokemon, 1):
                    print(f"\n🔄 Procesando link {i}/{len(links_pokemon)}...")

                    if fetcher.confirmar_pokemon_automatico(link):
                        confirmaciones_exitosas += 1

                # 4. Mostrar resultado final
                print(f"\n📊 RESULTADO FINAL:")
                print("=" * 40)
                print(f"� Links de Pokémon encontrados: {len(links_pokemon)}")
                print(f"✅ Confirmaciones exitosas: {confirmaciones_exitosas}")

                if confirmaciones_exitosas > 0:
                    print(f"\n🎉 ¡PROCESO COMPLETADO EXITOSAMENTE!")
                    print(f"✅ {confirmaciones_exitosas} confirmación(es) enviada(s)")
                    print(f"📧 Email debería cambiar a: <EMAIL>")
                    print(f"💡 Verificar en pokemon.com para confirmar")
                else:
                    print(f"\n⚠️ No se pudieron procesar las confirmaciones")
                    print(f"💡 Revisar archivos guardados para más detalles")
            else:
                print(f"\n❌ No se encontraron links de confirmación de Pokémon")
                print(f"💡 Verificar que los correos contengan links válidos")
        else:
            print(f"\n😞 No se encontraron correos")
            print("💡 Los correos podrían requerir JavaScript del lado del cliente")

    except Exception as e:
        print(f"❌ Error general: {e}")
        import traceback
        traceback.print_exc()

    finally:
        fetcher.logout()

    print("\n🏁 Proceso completado")


if __name__ == "__main__":
    main()
