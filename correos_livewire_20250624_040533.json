{"timestamp": "2025-06-24T04:05:26.076214", "livewire_data": [{"wire_id": "nqcQRkBWsrAGWesadWsq", "component_name": "frontend.actions", "initial_data": {"fingerprint": {"id": "nqcQRkBWsrAGWesadWsq", "name": "frontend.actions", "locale": "en", "path": "mailbox", "method": "GET", "v": "acj"}, "effects": {"listeners": ["syncEmail", "checkReCaptcha3"]}, "serverMemo": {"children": [], "errors": [], "htmlHash": "da08db03", "data": {"in_app": false, "user": null, "domain": null, "domains": ["dujaw.com", "fgeta.com", "dennisgls26.com", "aipicz.com", "gamersparky26.com", "withsd.com", "zzetu.com", "dxgamers.com", "ulnik.com", "rdmail.info", "ziuwi.com", "tseru.com", "gohuki.com", "1em0nstore.win", "1em0nstore.trade", "1emonstore.trade"], "email": "<EMAIL>", "emails": ["<EMAIL>"], "captcha": null}, "dataMeta": [], "checksum": "2b0759bb1ae141dc0834b22452c2cffb9af707fbbbb9da51399ba49617aa1648"}}, "encoded_data": "{&quot;fingerprint&quot;:{&quot;id&quot;:&quot;nqcQRkBWsrAGWesadWsq&quot;,&quot;name&quot;:&quot;fro..."}, {"wire_id": "TT7aa2g0ppC7YJLL1kE1", "component_name": "frontend.nav", "initial_data": {"fingerprint": {"id": "TT7aa2g0ppC7YJLL1kE1", "name": "frontend.nav", "locale": "en", "path": "mailbox", "method": "GET", "v": "acj"}, "effects": {"listeners": []}, "serverMemo": {"children": [], "errors": [], "htmlHash": "d2c7a7bd", "data": {"menus": [], "current_route": null}, "dataMeta": {"modelCollections": {"menus": {"class": null, "id": [], "relations": [], "connection": null}}}, "checksum": "471a50635e5616a805ca4c202d3903be169170ec49a8363f1ef1b97a8cc782b4"}}, "encoded_data": "{&quot;fingerprint&quot;:{&quot;id&quot;:&quot;TT7aa2g0ppC7YJLL1kE1&quot;,&quot;name&quot;:&quot;fro..."}, {"wire_id": "hfJzsYZUoRnBT5IzmGfB", "component_name": "frontend.app", "initial_data": {"fingerprint": {"id": "hfJzsYZUoRnBT5IzmGfB", "name": "frontend.app", "locale": "en", "path": "mailbox", "method": "GET", "v": "acj"}, "effects": {"listeners": ["fetchMessages", "syncEmail"]}, "serverMemo": {"children": [], "errors": [], "htmlHash": "36d69e5b", "data": {"messages": [], "deleted": [], "error": "", "email": "<EMAIL>", "initial": false, "overflow": false}, "dataMeta": [], "checksum": "167b0d57e14c3f6637fa6408c3fd0984654294ffb952b0c21cbe81a10ece8aff"}}, "encoded_data": "{&quot;fingerprint&quot;:{&quot;id&quot;:&quot;hfJzsYZUoRnBT5IzmGfB&quot;,&quot;name&quot;:&quot;fro..."}], "ajax_data": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <meta http-equiv=\"X-UA-Compatible\" content=\"ie=edge\">\n        <title>Dujaw Store</title>\n        \n        <link rel=\"icon\" href=\"https://dujaw.com/storage/public/images/joystick.png\">\n        <link href=\"https://cdn.quilljs.com/1.3.6/quill.snow.css\" rel=\"preload\" as=\"style\" onload=\"this.onload=null;this.rel='stylesheet'\">\n    <link rel=\"preload\" as=\"style\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css\" integrity=\"sha512-+4zCK9k+qNFUR5X+cKL9EIR+ZOhtIloNl9GIKS57V1MyNsYpYcUrUeQc9vNfzsWfV28IaLL3i96P9sdNyeRssA==\" crossorigin=\"anonymous\" onload=\"this.onload=null;this.rel='stylesheet'\" />\n    <link rel=\"preload\" as=\"style\" href=\"https://dujaw.com/css/vendor.css\" onload=\"this.onload=null;this.rel='stylesheet'\">\n    <link rel=\"stylesheet\" href=\"https://dujaw.com/css/common.css\">\n    <script src=\"https://dujaw.com/vendor/Shortcode/Shortcode.js\"></script>\n    <script src=\"https://dujaw.com/js/app.js\" defer></script>\n    <style >[wire\\:loading], [wire\\:loading\\.delay], [wire\\:loading\\.inline-block], [wire\\:loading\\.inline], [wire\\:loading\\.block], [wire\\:loading\\.flex], [wire\\:loading\\.table], [wire\\:loading\\.grid], [wire\\:loading\\.inline-flex] {display: none;}[wire\\:loading\\.delay\\.shortest], [wire\\:loading\\.delay\\.shorter], [wire\\:loading\\.delay\\.short], [wire\\:loading\\.delay\\.long], [wire\\:loading\\.delay\\.longer], [wire\\:loading\\.delay\\.longest] {display:none;}[wire\\:offline] {display: none;}[wire\\:dirty]:not(textarea):not(input):not(select) {display: none;}input:-webkit-autofill, select:-webkit-autofill, textarea:-webkit-autofill {animation-duration: 50000s;animation-name: livewireautofill;}@keyframes livewireautofill { from {} }</style>\n    \n        \n        <meta name=\"csrf-token\" content=\"6qAYEvONeT48h15gbYEUnphrGGhVo0VHqUqXluHD\">\n<link rel=\"preconnect\" href=\"https://fonts.googleapis.com\">\n<link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossorigin>\n<link href=\"https://fonts.googleapis.com/css2?family=Kadwa:wght@400;600;700&display=swap\" rel=\"preload\" as=\"style\" onload=\"this.onload=null;this.rel='stylesheet'\">\n<link href=\"https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap\" rel=\"preload\" as=\"style\" onload=\"this.onload=null;this.rel='stylesheet'\">\n<style>\n:root {\n  --head-font: \"Kadwa\";\n  --body-font: \"Poppins\";\n  --primary: #0155b5;\n  --secondary: #2fc10a;\n  --tertiary: #d2ab3e;\n}\n</style>\n<script>\n  let captcha_name = \"off\";\n  let site_key = \"\";\n  if(captcha_name && captcha_name !== \"off\") {\n    site_key = \"\";\n  }\n  let strings = {\"Get back to MailBox\":\"Get back to MailBox\",\"Enter Username\":\"Enter Username\",\"Select Domain\":\"Select Domain\",\"Create\":\"Create\",\"Random\":\"Random\",\"Custom\":\"Custom\",\"Menu\":\"Menu\",\"Cancel\":\"Cancel\",\"Copy\":\"Copy\",\"Refresh\":\"Refresh\",\"New\":\"New\",\"Delete\":\"Delete\",\"Download\":\"Download\",\"Fetching\":\"Fetching\",\"Empty Inbox\":\"Empty Inbox\",\"Error\":\"Error\",\"Success\":\"Success\",\"Close\":\"Close\",\"Email ID Copied to Clipboard\":\"Email ID Copied to Clipboard\",\"Please enter Username\":\"Please enter Username\",\"Please Select a Domain\":\"Please Select a Domain\",\"Username not allowed\":\"Username not allowed\",\"Your Temporary Email Address\":\"Your Temporary Email Address\",\"Attachments\":\"Attachments\",\"Blocked\":\"Blocked\",\"Emails from\":\"Emails from\",\"are blocked by Admin\":\"are blocked by Admin\",\"No Messages\":\"No Messages\",\"Waiting for Incoming Messages\":\"Waiting for Incoming Messages\",\"Scan QR Code to access\":\"Scan QR Code to access\",\"Create your own Temp Mail\":\"Create your own Temp Mail\",\"Your Temprorary Email\":\"Your Temprorary Email\",\"Enter a Username and Select the Domain\":\"Enter a Username and Select the Domain\",\"Username length cannot be less than\":\"Username length cannot be less than\",\"and greator than\":\"and greator than\",\"Create a Random Email\":\"Create a Random Email\",\"Sender\":\"Sender\",\"Subject\":\"Subject\",\"Time\":\"Time\",\"Open\":\"Open\",\"Go Back to Inbox\":\"Go Back to Inbox\",\"Date\":\"Date\",\"Copyright\":\"Copyright\",\"Ad Blocker Detected\":\"Ad Blocker Detected\",\"Disable the Ad Blocker to use \":\"Disable the Ad Blocker to use \",\"Your temporary email address is ready\":\"Your temporary email address is ready\",\"You have reached daily limit of MAX \":\"You have reached daily limit of MAX \",\" temp mail\":\" temp mail\",\"Sorry! That email is already been used by someone else. Please try a different email address.\":\"Sorry! That email is already been used by someone else. Please try a different email address.\",\"Invalid Captcha. Please try again\":\"Invalid Captcha. Please try again\",\"Invalid Password\":\"Invalid Password\",\"Password\":\"Password\",\"Unlock\":\"Unlock\",\"Your Name\":\"Your Name\",\"Enter your Name\":\"Enter your Name\",\"Your Email\":\"Your Email\",\"Enter your Email\":\"Enter your Email\",\"Message\":\"Message\",\"Enter your Message\":\"Enter your Message\",\"Send Message\":\"Send Message\"}\n  const __ = (string) => {\n    if(strings[string] !== undefined) {\n      return strings[string];\n    } else {\n      return string;\n    }\n  }\n</script>\n</head>\n<body>\n    <div class=\"default-theme\">\n        <div class=\"flex flex-wrap\">\n            <div class=\"w-full lg:w-1/4 bg-blue-700 py-6 lg:min-h-screen\" style=\"background-color: #0155b5\">\n                <div class=\"flex justify-center p-3 mb-10\">\n                    <a href=\"https://dujaw.com\">\n                                                <img class=\"w-logo\" src=\"https://dujaw.com/storage/public/images/joystick.png\" alt=\"logo\">\n                                            </a>\n                </div>\n                                <div wire:id=\"8hgMapOIueF0GsRQfDhh\" wire:initial-data=\"{&quot;fingerprint&quot;:{&quot;id&quot;:&quot;8hgMapOIueF0GsRQfDhh&quot;,&quot;name&quot;:&quot;frontend.actions&quot;,&quot;locale&quot;:&quot;en&quot;,&quot;path&quot;:&quot;mailbox&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;v&quot;:&quot;acj&quot;},&quot;effects&quot;:{&quot;listeners&quot;:[&quot;syncEmail&quot;,&quot;checkReCaptcha3&quot;]},&quot;serverMemo&quot;:{&quot;children&quot;:[],&quot;errors&quot;:[],&quot;htmlHash&quot;:&quot;da08db03&quot;,&quot;data&quot;:{&quot;in_app&quot;:false,&quot;user&quot;:null,&quot;domain&quot;:null,&quot;domains&quot;:[&quot;dujaw.com&quot;,&quot;fgeta.com&quot;,&quot;dennisgls26.com&quot;,&quot;aipicz.com&quot;,&quot;gamersparky26.com&quot;,&quot;withsd.com&quot;,&quot;zzetu.com&quot;,&quot;dxgamers.com&quot;,&quot;ulnik.com&quot;,&quot;rdmail.info&quot;,&quot;ziuwi.com&quot;,&quot;tseru.com&quot;,&quot;gohuki.com&quot;,&quot;1em0nstore.win&quot;,&quot;1em0nstore.trade&quot;,&quot;1emonstore.trade&quot;],&quot;email&quot;:&quot;<EMAIL>&quot;,&quot;emails&quot;:[&quot;<EMAIL>&quot;],&quot;captcha&quot;:null},&quot;dataMeta&quot;:[],&quot;checksum&quot;:&quot;8eed15e847235b1328ee7474423b5ed6d178e7867ac931aa2f39e4b7be8d36b6&quot;}}\" x-data=\"{ in_app: false }\">\n    <div x-show.transition.in=\"in_app\" class=\"app-action mt-4 px-8\" style=\"display: none;\">\n                <form wire:submit.prevent=\"create\" class=\"lg:max-w-72 lg:mx-auto\" method=\"post\">\n                        <input class=\"block appearance-none w-full border-0 rounded-md py-4 px-5 bg-white text-white bg-opacity-10 focus:outline-none placeholder-white placeholder-opacity-50\" type=\"text\" name=\"user\" id=\"user\" wire:model.defer=\"user\" placeholder=\"Enter Username\">\n            <div class=\"divider mt-5\"></div>\n            <div class=\"relative\">\n                <div class=\"relative\" x-data=\"{ open: false }\" @click.away=\"open = false\" @close.stop=\"open = false\">\n    <div @click=\"open = ! open\">\n        <input x-ref=\"domain\" type=\"text\" class=\"block appearance-none w-full border-0 bg-white text-white py-4 px-5 pr-8 bg-opacity-10 rounded-md cursor-pointer focus:outline-none select-none placeholder-white placeholder-opacity-50\" placeholder=\"Select Domain\" name=\"domain\" id=\"domain\" wire:model=\"domain\" readonly>\n    </div>\n\n    <div x-show=\"open\"\n            x-transition:enter=\"transition ease-out duration-200\"\n            x-transition:enter-start=\"transform opacity-0 scale-95\"\n            x-transition:enter-end=\"transform opacity-100 scale-100\"\n            x-transition:leave=\"transition ease-in duration-75\"\n            x-transition:leave-start=\"transform opacity-100 scale-100\"\n            x-transition:leave-end=\"transform opacity-0 scale-95\"\n            class=\"absolute z-50 mt-2 w-full rounded-md shadow-lg origin-top-right right-0\"\n            style=\"display: none;\"\n            @click=\"open = false\">\n        <div class=\"rounded-md shadow-xs max-h-96 overflow-y-auto py-1 bg-white\">\n            <a x-on:click=\"$refs.domain.value = 'dujaw.com'; $wire.setDomain('dujaw.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>dujaw.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'fgeta.com'; $wire.setDomain('fgeta.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>fgeta.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'dennisgls26.com'; $wire.setDomain('dennisgls26.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>dennisgls26.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'aipicz.com'; $wire.setDomain('aipicz.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>aipicz.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'gamersparky26.com'; $wire.setDomain('gamersparky26.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>gamersparky26.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'withsd.com'; $wire.setDomain('withsd.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>withsd.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'zzetu.com'; $wire.setDomain('zzetu.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>zzetu.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'dxgamers.com'; $wire.setDomain('dxgamers.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>dxgamers.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'ulnik.com'; $wire.setDomain('ulnik.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>ulnik.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'rdmail.info'; $wire.setDomain('rdmail.info')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>rdmail.info</a>\n                                                <a x-on:click=\"$refs.domain.value = 'ziuwi.com'; $wire.setDomain('ziuwi.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>ziuwi.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'tseru.com'; $wire.setDomain('tseru.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>tseru.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'gohuki.com'; $wire.setDomain('gohuki.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>gohuki.com</a>\n                                                <a x-on:click=\"$refs.domain.value = '1em0nstore.win'; $wire.setDomain('1em0nstore.win')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>1em0nstore.win</a>\n                                                <a x-on:click=\"$refs.domain.value = '1em0nstore.trade'; $wire.setDomain('1em0nstore.trade')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>1em0nstore.trade</a>\n                                                <a x-on:click=\"$refs.domain.value = '1emonstore.trade'; $wire.setDomain('1emonstore.trade')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>1emonstore.trade</a>\n        </div>\n    </div>\n</div>\n                <div class=\"pointer-events-none absolute inset-y-0 right-0 flex items-center px-5 text-white\">\n                    <svg class=\"fill-current h-4 w-4\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\"><path d=\"M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z\"/></svg>\n                </div>\n            </div>\n            <div class=\"divider mt-5\"></div>\n            <input id=\"create\" class=\"block appearance-none w-full rounded-md py-4 px-5 bg-teal-500 text-white cursor-pointer focus:outline-none\" style=\"background-color: #2fc10a\" type=\"submit\" value=\"Create\">\n            <div class=\"divider my-8 flex justify-center\">\n                <div class=\"border-t-2 w-2/3 border-white border-opacity-25\"></div>\n            </div>\n        </form>\n        <form wire:submit.prevent=\"random\" class=\"lg:max-w-72 lg:mx-auto\" method=\"post\">\n            <input id=\"random\" class=\"block appearance-none w-full rounded-md py-4 px-5 bg-yellow-500 text-white cursor-pointer focus:outline-none\" style=\"background-color: #d2ab3e\" type=\"submit\" value=\"Random\">\n        </form>\n                <div class=\"lg:max-w-72 lg:mx-auto\">\n            <button x-on:click=\"in_app = false\" class=\"block appearance-none w-full rounded-md my-5 py-2 px-5 bg-white bg-opacity-10 text-white text-sm cursor-pointer focus:outline-none\">Cancel</button>\n        </div>\n            </div>\n    <div x-show.transition.in=\"!in_app\" class=\"in-app-actions mt-4 px-8\" style=\"display: none;\">\n        <form class=\"lg:max-w-72 lg:mx-auto\" action=\"#\" method=\"post\">\n            <div class=\"relative\">\n                <div class=\"relative\" x-data=\"{ open: false }\" @click.away=\"open = false\" @close.stop=\"open = false\">\n    <div @click=\"open = ! open\">\n        <div class=\"block appearance-none w-full bg-white text-white py-4 px-5 pr-8 bg-opacity-10 rounded-md cursor-pointer focus:outline-none select-none\" id=\"email_id\"><EMAIL></div>\n    </div>\n\n    <div x-show=\"open\"\n            x-transition:enter=\"transition ease-out duration-200\"\n            x-transition:enter-start=\"transform opacity-0 scale-95\"\n            x-transition:enter-end=\"transform opacity-100 scale-100\"\n            x-transition:leave=\"transition ease-in duration-75\"\n            x-transition:leave-start=\"transform opacity-100 scale-100\"\n            x-transition:leave-end=\"transform opacity-0 scale-95\"\n            class=\"absolute z-50 mt-2 w-full rounded-md shadow-lg origin-top\"\n            style=\"display: none;\"\n            @click=\"open = false\">\n        <div class=\"rounded-md shadow-xs max-h-96 overflow-y-auto py-1 bg-white\">\n            <a class=\"block px-4 py-2 text-sm leading-5 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out\" href=\"https://dujaw.com/switch/<EMAIL>\"><EMAIL></a>\n        </div>\n    </div>\n</div>\n                <div class=\"pointer-events-none absolute inset-y-0 right-0 flex items-center px-3 text-white\">\n                    <svg class=\"fill-current h-4 w-4\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\"><path d=\"M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z\"/></svg>\n                </div>\n            </div>\n        </form>\n        <div class=\"divider mt-5\"></div>\n        <div class=\"grid grid-cols-4 lg:grid-cols-2 gap-2 lg:gap-6 lg:max-w-72 lg:mx-auto\">\n            <div class=\"btn_copy bg-white bg-opacity-10 text-white rounded-md py-5 lg:py-10 text-center hover:bg-opacity-25 cursor-pointer\">\n                <div class=\"text-xl lg:text-3xl mx-auto\">\n                    <i class=\"far fa-copy\"></i>\n                </div>\n                <div class=\"text-xs lg:text-base pt-5\">Copy</div>\n            </div>\n            <div onclick=\"document.getElementById('refresh').classList.remove('pause-spinner')\" wire:click=\"$emit('fetchMessages')\" class=\"bg-white bg-opacity-10 text-white rounded-md py-5 lg:py-10 text-center hover:bg-opacity-25 cursor-pointer\">\n                <div class=\"text-xl lg:text-3xl  mx-auto\">\n                    <i id=\"refresh\" class=\"fas fa-sync-alt fa-spin\"></i>\n                </div>\n                <div class=\"text-xs lg:text-base pt-5\">Refresh</div>\n            </div>\n            <div x-on:click=\"in_app = true\" class=\"bg-white bg-opacity-10 text-white rounded-md py-5 lg:py-10 text-center hover:bg-opacity-25 cursor-pointer\">\n                <div class=\"text-xl lg:text-3xl  mx-auto\">\n                    <i class=\"far fa-plus-square\"></i>\n                </div>\n                <div class=\"text-xs lg:text-base pt-5\">New</div>\n            </div>\n            <div wire:click=\"deleteEmail\" class=\"bg-white bg-opacity-10 text-white rounded-md py-5 lg:py-10 text-center hover:bg-opacity-25 cursor-pointer\">\n                <div class=\"text-xl lg:text-3xl  mx-auto\">\n                    <i class=\"far fa-trash-alt\"></i>\n                </div>\n                <div class=\"text-xs lg:text-base pt-5\">Delete</div>\n            </div>\n        </div>\n    </div>\n    </div>\n<!-- Livewire Component wire-end:8hgMapOIueF0GsRQfDhh -->                            </div>\n            <div class=\"w-full lg:w-3/4\">\n                <nav wire:id=\"MzuZI6wMSrhe3a50KD3e\" wire:initial-data=\"{&quot;fingerprint&quot;:{&quot;id&quot;:&quot;MzuZI6wMSrhe3a50KD3e&quot;,&quot;name&quot;:&quot;frontend.nav&quot;,&quot;locale&quot;:&quot;en&quot;,&quot;path&quot;:&quot;mailbox&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;v&quot;:&quot;acj&quot;},&quot;effects&quot;:{&quot;listeners&quot;:[]},&quot;serverMemo&quot;:{&quot;children&quot;:[],&quot;errors&quot;:[],&quot;htmlHash&quot;:&quot;d2c7a7bd&quot;,&quot;data&quot;:{&quot;menus&quot;:[],&quot;current_route&quot;:null},&quot;dataMeta&quot;:{&quot;modelCollections&quot;:{&quot;menus&quot;:{&quot;class&quot;:null,&quot;id&quot;:[],&quot;relations&quot;:[],&quot;connection&quot;:null}}},&quot;checksum&quot;:&quot;1ec1001271846014ee56e69b6654adfdd484ff3f5c4d147ee02fbd9dd1bcb3c6&quot;}}\">\n    <div class=\"bg-gray-100 px-5 hidden lg:flex sticky top-0 z-40 h-24\">\n        <div class=\"w-full my-auto\">\n            <div class=\"flex items-center justify-between h-16\">\n                <div class=\"flex items-center\">\n                    <div class=\"flex items-baseline space-x-4\">\n                                                                    </div>\n                </div>\n                <div class=\"flex items-center\">\n                    <div>\n                                            </div>\n                    <div class=\"ml-4 flex items-center md:ml-6\">\n                        <div class=\"relative\">\n                            <form action=\"https://dujaw.com/locale\" id=\"locale-form\" method=\"post\">\n                                <input type=\"hidden\" name=\"_token\" value=\"6qAYEvONeT48h15gbYEUnphrGGhVo0VHqUqXluHD\">                                <select class=\"block appearance-none bg-gray-200 cursor-pointer text-gray-800 py-1 rounded-md focus:outline-none\" name=\"locale\" id=\"locale\">\n                                                                        <option >ar</option>\n                                                                        <option >de</option>\n                                                                        <option selected>en</option>\n                                                                        <option >fr</option>\n                                                                        <option >hi</option>\n                                                                        <option >pl</option>\n                                                                        <option >ru</option>\n                                                                        <option >es</option>\n                                                                        <option >vi</option>\n                                                                        <option >tr</option>\n                                                                        <option >no</option>\n                                                                        <option >id</option>\n                                                                        <option >it</option>\n                                                                    </select>\n                            </form>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n    <div x-data=\"{ open: false }\">\n        <div @click=\"open = true\" class=\"absolute top-12 right-6 w-8 text-white\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 6h16M4 12h16m-7 6h7\" />\n            </svg>\n        </div>\n        <div x-show=\"open\" x-transition:enter=\"transition ease-out duration-200\" x-transition:enter-start=\"transform opacity-0 scale-95\" x-transition:enter-end=\"transform opacity-100 scale-100\" x-transition:leave=\"transition ease-in duration-75\" x-transition:leave-start=\"transform opacity-100 scale-100\" x-transition:leave-end=\"transform opacity-0 scale-95\" @click.away=\"open = false\" class=\"flex-col lg:hidden fixed top-0 left-0 min-h-screen w-full bg-black bg-opacity-75\">\n            <div @click=\"open = false\" class=\"absolute top-6 right-6 w-8 text-white\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n                </svg>\n            </div>\n            <div class=\"w-full mx-auto mt-20\">\n                <div class=\"flex flex-col items-center justify-between\">\n                    <div class=\"flex flex-col items-center space-y-2\">\n                                                                    </div>\n                    <div class=\"flex flex-col items-center space-y-2 mt-10\">\n                        <div class=\"text-white space-x-2\">\n                                                    </div>\n                        <div class=\"flex items-center mt-4\">\n                            <div class=\"relative\">\n                                <form action=\"https://dujaw.com/locale\" id=\"locale-form-mobile\" method=\"post\">\n                                    <input type=\"hidden\" name=\"_token\" value=\"6qAYEvONeT48h15gbYEUnphrGGhVo0VHqUqXluHD\">                                    <select class=\"block appearance-none bg-gray-200 cursor-pointer text-gray-800 py-1 rounded-md focus:outline-none\" name=\"locale\" id=\"locale-mobile\">\n                                                                                <option >ar</option>\n                                                                                <option >de</option>\n                                                                                <option selected>en</option>\n                                                                                <option >fr</option>\n                                                                                <option >hi</option>\n                                                                                <option >pl</option>\n                                                                                <option >ru</option>\n                                                                                <option >es</option>\n                                                                                <option >vi</option>\n                                                                                <option >tr</option>\n                                                                                <option >no</option>\n                                                                                <option >id</option>\n                                                                                <option >it</option>\n                                                                            </select>\n                                </form>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n</nav>\n<!-- Livewire Component wire-end:MzuZI6wMSrhe3a50KD3e -->                <div class=\"flex flex-col lg:min-h-tm-default\">\n                                         \n                        <main wire:id=\"Ig2ezH90MlLW5AffZCEt\" wire:initial-data=\"{&quot;fingerprint&quot;:{&quot;id&quot;:&quot;Ig2ezH90MlLW5AffZCEt&quot;,&quot;name&quot;:&quot;frontend.app&quot;,&quot;locale&quot;:&quot;en&quot;,&quot;path&quot;:&quot;mailbox&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;v&quot;:&quot;acj&quot;},&quot;effects&quot;:{&quot;listeners&quot;:[&quot;fetchMessages&quot;,&quot;syncEmail&quot;]},&quot;serverMemo&quot;:{&quot;children&quot;:[],&quot;errors&quot;:[],&quot;htmlHash&quot;:&quot;36d69e5b&quot;,&quot;data&quot;:{&quot;messages&quot;:[],&quot;deleted&quot;:[],&quot;error&quot;:&quot;&quot;,&quot;email&quot;:&quot;<EMAIL>&quot;,&quot;initial&quot;:false,&quot;overflow&quot;:false},&quot;dataMeta&quot;:[],&quot;checksum&quot;:&quot;3f395a4eeea3a8cebcebe7255f29010f27c58c7c3c954b48a51ccc6ddaee4aac&quot;}}\" x-data=\"{ id: 0 }\" class=\"flex-1 lg:flex\">\n            <div class=\"w-full lg:w-1/3 bg-white flex flex-col min-h-tm-mobile\">\n                <div class=\"flex-1 flex justify-center items-center h-40 text-gray-400 text-2xl\">\n            Fetching...\n        </div>\n            </div>\n    <div class=\"message-content w-full lg:w-2/3 bg-white border-1 border-l border-gray-200 flex flex-col\">\n        <div x-show=\"id === 0\" class=\"flex-1 hidden lg:flex\">\n            <div class=\"w-2/3 m-auto\">\n                <img class=\"m-auto max-w-full\" src=\"https://dujaw.com/images/sample.jpg\" alt=\"mails\">\n            </div>\n        </div>\n            </div>\n</main>\n<!-- Livewire Component wire-end:Ig2ezH90MlLW5AffZCEt -->                                                        </div>\n            </div>\n        </div>\n    </div>\n    \n    <!--- Helper Text for Language Translation -->\n    <div class=\"hidden language-helper\">\n        <div class=\"error\">Error</div>\n        <div class=\"success\">Success</div>\n        <div class=\"copy_text\">Email ID Copied to Clipboard</div>\n    </div>\n\n    <script src=\"/livewire/livewire.js?id=90730a3b0e7144480175\" data-turbo-eval=\"false\" data-turbolinks-eval=\"false\" ></script><script data-turbo-eval=\"false\" data-turbolinks-eval=\"false\" >window.livewire = new Livewire();window.Livewire = window.livewire;window.livewire_app_url = '';window.livewire_token = '6qAYEvONeT48h15gbYEUnphrGGhVo0VHqUqXluHD';window.deferLoadingAlpine = function (callback) {window.addEventListener('livewire:load', function () {callback();});};let started = false;window.addEventListener('alpine:initializing', function () {if (! started) {window.livewire.start();started = true;}});document.addEventListener(\"DOMContentLoaded\", function () {if (! started) {window.livewire.start();started = true;}});</script>\n        <script>\n        document.addEventListener('DOMContentLoaded', () => {\n            const email = '<EMAIL>';\n            const add_mail_in_title = \"yes\"\n            if(add_mail_in_title === 'yes') {\n                document.title += ` - ${email}`;\n            }\n            Livewire.emit('syncEmail', email);\n            Livewire.emit('fetchMessages');\n        });\n    </script>\n        <script>\n        document.addEventListener('stopLoader', () => {\n            document.getElementById('refresh').classList.add('pause-spinner');\n        });\n        let counter = parseInt(20);\n        setInterval(() => {\n            if (counter === 0 && document.getElementById('imap-error') === null && !document.hidden) {\n                document.getElementById('refresh').classList.remove('pause-spinner');\n                Livewire.emit('fetchMessages');\n                counter = parseInt(20);\n            }\n            counter--;\n            if(document.hidden) {\n                counter = 1;\n            }\n        }, 1000);\n    </script>\n    \n    \n        <script src=\"https://dujaw.com/storage/js/mnpw3.js\" defer></script>\n    <script defer>\n    setTimeout(() => {\n        const enable_ad_block_detector = \"0\"\n        if(!document.getElementById('Q8CvrZzY9fphm6gG') && enable_ad_block_detector == \"1\") {\n            document.querySelector('.default-theme').remove()\n            document.querySelector('body > div').insertAdjacentHTML('beforebegin', `\n                <div class=\"fixed w-screen h-screen bg-red-800 flex flex-col justify-center items-center gap-5 z-50 text-white\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-40 w-40\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                        <path fill-rule=\"evenodd\" d=\"M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z\" clip-rule=\"evenodd\" />\n                    </svg>\n                    <h1 class=\"text-4xl font-bold\">Ad Blocker Detected</h1>\n                    <h2>Disable the Ad Blocker to use Dujaw Store</h2>\n                </div>\n            `)\n        }\n    }, 500);\n    </script>\n    </body>\n</html>", "correos_encontrados": [{"source": "livewire_main", "wire_id": "nqcQRkBWsrAGWesadWsq", "data": {"effects": {"html": null, "dirty": []}, "serverMemo": {"checksum": "2b0759bb1ae141dc0834b22452c2cffb9af707fbbbb9da51399ba49617aa1648"}}}, {"source": "livewire_main", "wire_id": "hfJzsYZUoRnBT5IzmGfB", "data": {"effects": {"html": "<main wire:id=\"hfJzsYZUoRnBT5IzmGfB\" x-data=\"{ id: 0 }\" class=\"flex-1 lg:flex\">\n            <div class=\"w-full lg:w-1/3 bg-white flex flex-col min-h-tm-mobile\">\n                <div class=\"messages flex flex-col divide-y divide-gray-200\">\n                                                <div x-on:click=\"id = 7292; document.querySelector('.message-content').scrollIntoView({behavior: 'smooth'});\" class=\"w-full p-5 cursor-pointer hover:bg-gray-50\" data-id=\"7292\">\n                <div class=\"flex justify-between\">\n                    <div>\n                        <div class=\"text-gray-800\">Pokémon Customer Service</div>\n                        <div class=\"text-xs text-gray-400\"><EMAIL></div>\n                    </div>\n                    <div>\n                        <div class=\"text-gray-800 text-sm\">1 hour ago</div>\n                    </div>\n                </div>\n                <div class=\"text-gray-600 mt-5 text-sm truncate\">You have requested to change your email address</div>\n            </div>\n                                </div>\n            </div>\n    <div class=\"message-content w-full lg:w-2/3 bg-white border-1 border-l border-gray-200 flex flex-col\">\n        <div x-show=\"id === 0\" class=\"flex-1 hidden lg:flex\">\n            <div class=\"w-2/3 m-auto\">\n                <img class=\"m-auto max-w-full\" src=\"https://dujaw.com/images/sample.jpg\" alt=\"mails\">\n            </div>\n        </div>\n                <div x-show=\"id === 7292\" id=\"message-7292\" class=\"flex-1 lg:flex flex-col\">\n            <textarea class=\"hidden\">To: <EMAIL>&#13;From: \"Pokémon Customer Service\" <<EMAIL>>&#13;Subject: You have requested to change your email address&#13;Date: 24 Jun 2025 07:09 AM&#13;Content-Type: text/html&#13;&#13;\r\n\r\n\r\n\r\n\r\n&lt;html&gt;&lt;head&gt;&lt;meta charset=&quot;utf-8&quot; /&gt;&lt;/head&gt;&lt;body bgcolor=&quot;#FFFFFF&quot;&gt;\r\n&lt;style&gt;\r\n.ExternalClass * {line-height: 100%!important}\r\n\r\n.button {\r\nbackground: #53b662;\r\nborder-radius: 5px;\r\nborder: none;\r\ncolor: #FFFFFF;\r\ncursor: pointer;\r\nfont-size: 14px;\r\nfloat: left;\r\nline-height: 125%;\r\nmargin: 8px 0 0 0;\r\npadding: 0.75em 1.25em;\r\nvertical-align: middle;\r\ntext-align: center;\r\ntext-decoration: none;\r\nfont-family: verdana, arial, sans-serif;\r\n}\r\n\r\n&lt;/style&gt;\r\n\r\n    &lt;table width=&quot;600&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; align=&quot;center&quot; style=&quot;min-width: 600px;&quot;&gt;\r\n\r\n        &lt;tbody&gt;\r\n        &lt;tr background=&quot;https://assets.pokemon.com/static2/_ui/img/mail/header-bg.gif&quot;&gt;\r\n            &lt;td align=&quot;left&quot;&gt;\r\n                &lt;table width=&quot;600&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; align=&quot;center&quot;&gt;\r\n                    &lt;tbody&gt;&lt;tr&gt;\r\n                        &lt;td style=&quot;font-size: 86px; line-height: 86px;&quot;&gt;\r\n                            &lt;a target=&quot;blank&quot; href=&quot;http://pokemon.com&quot;&gt;&lt;img src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/logo.gif&quot;\r\n                                                              style=&quot;display: block; margin: 0; padding: 0; height:86px;&quot;\r\n                                                              margin=&quot;0&quot; padding=&quot;0&quot; width=&quot;auto&quot; height=&quot;86&quot;\r\n                                                              alt=&quot;Pokémon Logo&quot; border=&quot;0&quot;&gt;&lt;/a&gt;\r\n                        &lt;/td&gt;\r\n                        &lt;td align=&quot;right&quot; style=&quot;font-size: 86px; line-height: 86px;&quot;&gt;\r\n                            &lt;img src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/pikachu-1.gif&quot;\r\n                                 style=&quot;display: block; margin: 0; padding: 0; height:86px;&quot;\r\n                                 margin=&quot;0&quot; padding=&quot;0&quot; width=&quot;auto&quot; height=&quot;86&quot; alt=&quot;Pikachu&quot; border=&quot;0&quot;&gt;\r\n                        &lt;/td&gt;\r\n                    &lt;/tr&gt;\r\n                &lt;/tbody&gt;&lt;/table&gt;\r\n\r\n            &lt;/td&gt;\r\n        &lt;/tr&gt;\r\n\r\n        &lt;tr bgcolor=&quot;#eaeaea&quot;&gt;\r\n            &lt;td align=&quot;left&quot; style=&quot;font-size: 3px; line-height: 4px;&quot;&gt;\r\n                &lt;table width=&quot;600&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; align=&quot;center&quot;&gt;\r\n                     &lt;tbody&gt;&lt;tr&gt;\r\n                        &lt;td align=&quot;right&quot; style=&quot;font-size: 3px; line-height: 4px;&quot;&gt;\r\n                            &lt;img src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/pikachu-2.gif&quot;\r\n                                 style=&quot;display: block; margin: 0; padding: 0; height:4px !important; line-height: 4px !important; font-size: 4px !important;&quot;\r\n                                 margin=&quot;0&quot; padding=&quot;0&quot; width=&quot;auto&quot; height=&quot;4&quot; alt=&quot;Pikachu&quot; border=&quot;0&quot;&gt;\r\n                        &lt;/td&gt;\r\n                    &lt;/tr&gt;\r\n                &lt;/tbody&gt;&lt;/table&gt;\r\n            &lt;/td&gt;\r\n        &lt;/tr&gt;\r\n\r\n        &lt;tr bgcolor=&quot;#313131&quot;&gt;\r\n            &lt;td align=&quot;left&quot;&gt;\r\n                &lt;table width=&quot;600&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; align=&quot;center&quot;&gt;\r\n                    &lt;tbody&gt;&lt;tr&gt;\r\n                        &lt;td width=&quot;448&quot; style=&quot;font-size:23px !important; font-weight:400; padding-left: 23px;&quot;&gt;\r\n                            &lt;font color=&quot;#FFFFFF&quot; face=&quot;Arial,Helvetica,sans-serif&quot;&gt;\r\n                                Hello from Pokemon.com\r\n                            &lt;/font&gt;\r\n                        &lt;/td&gt;\r\n                        &lt;td align=&quot;right&quot;&gt;\r\n                            &lt;img src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/pikachu-3.gif&quot;\r\n                                 style=&quot;display: block; margin: 0; padding: 0; height:60px; line-height: 60px; font-size: 60px;&quot;\r\n                                 margin=&quot;0&quot; padding=&quot;0&quot; width=&quot;auto&quot; height=&quot;60&quot; alt=&quot;Pikachu&quot; border=&quot;0&quot;&gt;\r\n                        &lt;/td&gt;\r\n                    &lt;/tr&gt;\r\n                &lt;/tbody&gt;&lt;/table&gt;\r\n            &lt;/td&gt;\r\n        &lt;/tr&gt;\r\n\r\n        &lt;tr style=&quot;line-height: 9px!important font-size: 9px&quot;&gt;\r\n           &lt;td align=&quot;left&quot;&gt;\r\n                &lt;table width=&quot;100%&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; align=&quot;center&quot;&gt;\r\n                    &lt;tbody&gt;&lt;tr&gt;\r\n                        &lt;td width=&quot;600&quot; align=&quot;center&quot;&gt;\r\n                            &lt;table width=&quot;600&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; align=&quot;center&quot;&gt;\r\n                                &lt;tbody&gt;&lt;tr bgcolor=&quot;#ffffff&quot;&gt;\r\n                                    &lt;td align=&quot;left&quot; height=&quot;9&quot; style=&quot;line-height: 9px; font-size: 9px;&quot;&gt;\r\n                                        &lt;img src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/notch.gif&quot;\r\n                                             style=&quot;display: block; margin: 0; padding: 0; height:9px; line-height: 9px; font-size: 9px;&quot;\r\n                                             margin=&quot;0&quot; padding=&quot;0&quot; width=&quot;auto&quot; height=&quot;9&quot; alt=&quot;&quot; border=&quot;0&quot;&gt;\r\n                                    &lt;/td&gt;\r\n                                    &lt;td align=&quot;right&quot; height=&quot;9&quot; style=&quot;line-height: 9px; font-size: 9px;&quot;&gt;\r\n                                        &lt;img src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/pikachu-4.gif&quot;\r\n                                             style=&quot;display: block; margin: 0; padding: 0; height:9px; line-height: 9px; font-size: 9px;&quot;\r\n                                             margin=&quot;0&quot; padding=&quot;0&quot; width=&quot;auto&quot; height=&quot;9&quot; alt=&quot;Pikachu&quot; border=&quot;0&quot;&gt;\r\n                                    &lt;/td&gt;\r\n                                &lt;/tr&gt;\r\n                            &lt;/tbody&gt;&lt;/table&gt;\r\n\r\n                        &lt;/td&gt;\r\n                    &lt;/tr&gt;\r\n                &lt;/tbody&gt;&lt;/table&gt;\r\n            &lt;/td&gt;\r\n        &lt;/tr&gt;\r\n\r\n        &lt;tr&gt;\r\n            &lt;td height=&quot;35&quot;&gt;&lt;/td&gt;\r\n        &lt;/tr&gt;\r\n\r\n        &lt;tr&gt;\r\n            &lt;td align=&quot;left&quot;&gt;\r\n                &lt;table width=&quot;600&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; align=&quot;center&quot;&gt;\r\n                    &lt;tbody&gt;&lt;tr&gt;\r\n                        &lt;td width=&quot;11&quot;&gt;&lt;img src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/space.gif&quot; width=&quot;1&quot; height=&quot;1&quot; alt=&quot;&quot; border=&quot;0&quot;&gt;&lt;/td&gt;\r\n                        &lt;td width=&quot;250&quot;&gt;\r\n                            &lt;font color=&quot;#4f4f4f&quot; face=&quot;Arial,Helvetica,sans-serif&quot; style=&quot;font-size:16px; line-height:28px;&quot;&gt;\r\n                                \r\n\r\n\r\n&lt;p&gt;Dear Pokémon Trainer Club member,&lt;/p&gt;\r\n\r\n&lt;p&gt;It appears that you have recently changed the email address for your Pokémon Trainer Club account from this email address to a new email address. This email has been sent to you so you can verify this requested change.&lt;/p&gt;\r\n\r\n&lt;p&gt;To approve or reject this email address change, click the button below and follow the instructions. This link will expire in 24 hours. If it no longer works, please change your email address on Pokemon.com again to receive a new link.&lt;/p&gt;\r\n\r\n&lt;p&gt;If you did not request this change, do not click the Approve Email Address Change button below. Instead, go to www.pokemon.com to log in to your account and change your password. Please use your Pokémon Trainer Club account only to access sites operated by The Pokémon Company International, Pokémon Trading Card Game Live, Pokémon UNITE, Pokémon GO, and other official Pokémon apps that support Pokémon Trainer Club account log-in. You should not use your Pokémon Trainer Club password anywhere else. If you think you might have used your password for other sites or apps, we recommend you change your password immediately.&lt;/p&gt;\r\n\r\n&lt;p&gt;Visiting suspicious-looking websites that promise hacks or cheats for online games such as Pokémon Trading Card Game Live or Pokémon GO is not supported and is a violation of the games&#039; Terms of Use.&lt;/p&gt;\r\n\r\n&lt;p&gt;If you have additional questions, please contact us at &lt;a target=&quot;blank&quot; href=&quot;http://support.pokemon.com&quot; data-home=&quot;http://club.pokemon.com&quot;&gt;support.pokemon.com&lt;/a&gt;.&lt;/p&gt;\r\n\r\n\r\n                            &lt;/font&gt;\r\n                        &lt;/td&gt;\r\n                        &lt;td width=&quot;30&quot;&gt;&lt;img src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/space.gif&quot; width=&quot;1&quot; height=&quot;1&quot; alt=&quot;&quot; border=&quot;0&quot;&gt;&lt;/td&gt;\r\n                    &lt;/tr&gt;\r\n                &lt;/tbody&gt;&lt;/table&gt;\r\n            &lt;/td&gt;\r\n        &lt;/tr&gt;\r\n\r\n      &lt;tr&gt;\r\n        &lt;td align=&quot;left&quot;&gt;\r\n          &lt;table width=&quot;600&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; align=&quot;center&quot;&gt;\r\n            &lt;tbody&gt;&lt;tr&gt;\r\n              &lt;td width=&quot;11&quot;&gt;&lt;img src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/space.gif&quot; width=&quot;1&quot; height=&quot;1&quot; alt=&quot;&quot; border=&quot;0&quot;&gt;&lt;/td&gt;\r\n              &lt;td width=&quot;250&quot;&gt;\r\n                \r\n              &lt;/td&gt;\r\n              &lt;td width=&quot;30&quot;&gt;&lt;img src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/space.gif&quot; width=&quot;1&quot; height=&quot;1&quot; alt=&quot;&quot; border=&quot;0&quot;&gt;&lt;/td&gt;\r\n            &lt;/tr&gt;&lt;/tbody&gt;\r\n          &lt;/table&gt;\r\n        &lt;/td&gt;\r\n      &lt;/tr&gt;\r\n\r\n        &lt;tr&gt;\r\n        &lt;td width=&quot;600&quot; height=&quot;30&quot; align=&quot;left&quot; valign=&quot;top&quot;&gt;\r\n            &lt;img style=&quot;display:block&quot; src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/space.gif&quot; width=&quot;1&quot; height=&quot;1&quot; alt=&quot;&quot; border=&quot;0&quot;&gt;\r\n        &lt;/td&gt;\r\n      &lt;/tr&gt;\r\n\r\n      &lt;tr&gt;\r\n            &lt;td align=&quot;left&quot;&gt;\r\n                &lt;table width=&quot;600&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; align=&quot;center&quot;&gt;\r\n                    &lt;tbody&gt;&lt;tr&gt;\r\n                        &lt;td width=&quot;11&quot;&gt;&lt;img src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/space.gif&quot; width=&quot;1&quot; height=&quot;1&quot; alt=&quot;&quot; border=&quot;0&quot;&gt;&lt;/td&gt;\r\n                        &lt;td width=&quot;250&quot;&gt;\r\n                            &lt;font color=&quot;#4f4f4f&quot; face=&quot;Arial,Helvetica,sans-serif&quot; style=&quot;font-size:16px; line-height:28px;&quot;&gt;\r\n                                \r\n                            &lt;/font&gt;\r\n                        &lt;/td&gt;\r\n                        &lt;td width=&quot;30&quot;&gt;&lt;img src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/space.gif&quot; width=&quot;1&quot; height=&quot;1&quot; alt=&quot;&quot; border=&quot;0&quot;&gt;&lt;/td&gt;\r\n                    &lt;/tr&gt;\r\n                &lt;/tbody&gt;&lt;/table&gt;\r\n            &lt;/td&gt;\r\n        &lt;/tr&gt;\r\n\r\n        &lt;tr&gt;\r\n        &lt;td width=&quot;600&quot; height=&quot;30&quot; align=&quot;left&quot; valign=&quot;top&quot;&gt;\r\n            &lt;img style=&quot;display:block&quot; src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/space.gif&quot; width=&quot;1&quot; height=&quot;1&quot; alt=&quot;&quot; border=&quot;0&quot;&gt;\r\n        &lt;/td&gt;\r\n      &lt;/tr&gt;\r\n\r\n      &lt;tr&gt;\r\n            &lt;td align=&quot;left&quot;&gt;\r\n                &lt;table width=&quot;600&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; align=&quot;center&quot;&gt;\r\n                    &lt;tbody&gt;&lt;tr&gt;\r\n                        &lt;td width=&quot;11&quot;&gt;&lt;img src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/space.gif&quot; width=&quot;1&quot; height=&quot;1&quot; alt=&quot;&quot; border=&quot;0&quot;&gt;&lt;/td&gt;\r\n                        &lt;td width=&quot;250&quot;&gt;\r\n                            &lt;font color=&quot;#4f4f4f&quot; face=&quot;Arial,Helvetica,sans-serif&quot; style=&quot;font-size:16px; line-height:28px;&quot;&gt;\r\n                                Sincerely,&lt;br/&gt;\r\n&lt;b&gt;The Pokémon Company International&lt;/b&gt;\r\n                            &lt;/font&gt;\r\n                        &lt;/td&gt;\r\n                        &lt;td width=&quot;30&quot;&gt;&lt;img src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/space.gif&quot; width=&quot;1&quot; height=&quot;1&quot; alt=&quot;&quot; border=&quot;0&quot;&gt;&lt;/td&gt;\r\n                    &lt;/tr&gt;\r\n                &lt;/tbody&gt;&lt;/table&gt;\r\n            &lt;/td&gt;\r\n        &lt;/tr&gt;\r\n\r\n        &lt;tr&gt;\r\n        &lt;td width=&quot;600&quot; height=&quot;30&quot; align=&quot;left&quot; valign=&quot;top&quot;&gt;\r\n            &lt;img style=&quot;display:block&quot; src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/space.gif&quot; width=&quot;1&quot; height=&quot;1&quot; alt=&quot;&quot; border=&quot;0&quot;&gt;\r\n        &lt;/td&gt;\r\n      &lt;/tr&gt;\r\n\r\n      &lt;tr&gt;\r\n        &lt;td align=&quot;left&quot;&gt;\r\n          &lt;table width=&quot;600&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; align=&quot;center&quot;&gt;\r\n            &lt;tbody&gt;&lt;tr&gt;\r\n              &lt;td width=&quot;11&quot;&gt;&lt;img src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/space.gif&quot; width=&quot;1&quot; height=&quot;1&quot; alt=&quot;&quot; border=&quot;0&quot;&gt;&lt;/td&gt;\r\n              &lt;td width=&quot;250&quot;&gt;\r\n                \r\n&lt;p style=&quot;margin:0;padding:0&quot;&gt;\r\n&lt;a target=&quot;blank&quot; href=&quot;https://club.pokemon.com/us/pokemon-trainer-club/email-change-approval/49d9fd87c3028b1267f9825fddf6e825&quot; class=&quot;button button-green&quot;\r\n   style=&quot;background: #53b662;border-radius: 5px;border: none;color: #FFFFFF;cursor: pointer;font-size: 14px;float: left;line-height: 125%;margin: 8px 0 0 0;padding: 0.75em 1.25em;vertical-align: middle;text-align: center;text-decoration: none;font-family: verdana, arial, sans-serif;&quot;\r\n  &gt;\r\n\r\nApprove Email Address Change\r\n&lt;/a&gt;\r\n\r\n              &lt;/td&gt;\r\n              &lt;td width=&quot;30&quot;&gt;&lt;img src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/space.gif&quot; width=&quot;1&quot; height=&quot;1&quot; alt=&quot;&quot; border=&quot;0&quot;&gt;&lt;/td&gt;\r\n            &lt;/tr&gt;&lt;/tbody&gt;\r\n          &lt;/table&gt;\r\n        &lt;/td&gt;\r\n      &lt;/tr&gt;\r\n\r\n        &lt;tr&gt;\r\n            &lt;td align=&quot;center&quot; style=&quot;padding-top: 20px&quot;&gt;\r\n                &lt;img src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/pokemon-email-footer.png&quot; alt=&quot;Pokemons&quot; width=&quot;auto&quot; height=&quot;auto&quot; border=&quot;0&quot;&gt;\r\n            &lt;/td&gt;\r\n        &lt;/tr&gt;\r\n\r\n        &lt;tr&gt;\r\n            &lt;td align=&quot;center&quot; bgcolor=&quot;#313131&quot;&gt;\r\n                &lt;table width=&quot;600&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; align=&quot;center&quot;&gt;\r\n                    &lt;tbody&gt;&lt;tr&gt;\r\n                        &lt;td align=&quot;center&quot;&gt;\r\n                            &lt;img src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/top-footer.gif&quot; alt=&quot;Pokemons&quot; width=&quot;auto&quot; height=&quot;auto&quot; border=&quot;0&quot;&gt;\r\n                        &lt;/td&gt;\r\n                    &lt;/tr&gt;\r\n                &lt;/tbody&gt;&lt;/table&gt;\r\n            &lt;/td&gt;\r\n        &lt;/tr&gt;\r\n\r\n        &lt;tr&gt;\r\n            &lt;td bgcolor=&quot;#1f1f1f&quot; height=&quot;80&quot;&gt;\r\n                &lt;table width=&quot;550&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; align=&quot;center&quot;&gt;\r\n                &lt;tbody&gt;\r\n                    &lt;tr&gt;\r\n                        &lt;td align=&quot;left&quot; bgcolor=&quot;#1f1f1f&quot;&gt;\r\n                          &lt;span style=&quot;color: #fff; font-size:18px; font-weight: bold;&quot;&gt;The Pokémon Company&lt;/span&gt;\r\n                        &lt;/td&gt;\r\n                        \r\n                            &lt;td align=&quot;left&quot; bgcolor=&quot;#1f1f1f&quot;&gt;\r\n                &lt;a target=&quot;blank&quot; href=&quot;https://corporate.pokemon.com/en-us/&quot;\r\n                                   face=&quot;Arial,Helvetica,sans-serif&quot;\r\n                                   style=&quot;color: #5f5f5f; float: left; font-size:13px; font-weight: bold; line-height:20px; margin-top: 6px; text-transform: uppercase; text-decoration: none;&quot;\r\n                                &gt;About TPCI&lt;/a&gt;\r\n                            &lt;/td&gt;\r\n                        \r\n                        &lt;td align=&quot;left&quot; bgcolor=&quot;#1f1f1f&quot;&gt;\r\n\t\t\t\t&lt;a target=&quot;blank&quot; href=&quot;http://www.pokemon.com/us/terms-of-use/&quot;\r\n                             face=&quot;Arial,Helvetica,sans-serif&quot;\r\n                             style=&quot;color: #5f5f5f; float: left; font-size:13px; font-weight: bold; line-height:20px; margin-top: 6px; text-transform: uppercase; text-decoration: none;&quot;\r\n                          &gt;Terms of use&lt;/a&gt;\r\n                        &lt;/td&gt;\r\n                        &lt;td style=&quot;text-align:left; background-color: #1f1f1f&quot;&gt;\r\n\t\t\t\t&lt;a target=&quot;blank&quot; href=&quot;http://www.pokemon.com/us/cookie-page/&quot;  id=&quot;cookieLink&quot;\r\n                             face=&quot;Arial,Helvetica,sans-serif&quot;\r\n                             style=&quot;color: #5f5f5f; float: left; font-size:13px; font-weight: bold; line-height:20px; margin-top: 6px; text-transform: uppercase; text-decoration: none;&quot;\r\n                          &gt;Cookie Page&lt;/a&gt;\r\n                        &lt;/td&gt;\r\n                        &lt;td align=&quot;left&quot; bgcolor=&quot;#1f1f1f&quot;&gt;\r\n\t\t\t\t&lt;a target=&quot;blank&quot; href=&quot;http://www.pokemon.com/us/privacy-notice/&quot;\r\n                             face=&quot;Arial,Helvetica,sans-serif&quot;\r\n                             style=&quot;color: #5f5f5f; float: left; font-size:13px; font-weight: bold; line-height:20px; margin-top: 6px; text-transform: uppercase; text-decoration: none;&quot;\r\n                          &gt;Privacy Notice&lt;/a&gt;\r\n                        &lt;/td&gt;\r\n                    &lt;/tr&gt;\r\n                    &lt;tr&gt;\r\n                     &lt;td colspan=&quot;4&quot; style=&quot;color: #fff&quot;&gt;\r\n\r\n10400 NE 4th Street, Suite 2800, Bellevue, WA 98004\r\n\r\n                     &lt;/td&gt;\r\n                    &lt;/tr&gt;\r\n                &lt;/tbody&gt;\r\n                &lt;/table&gt;\r\n            &lt;/td&gt;\r\n        &lt;/tr&gt;\r\n\r\n    &lt;/tbody&gt;&lt;/table&gt;\r\n\r\n&lt;/body&gt;&lt;/html&gt;\r\n</textarea>\n            <div class=\"flex flex-col flex-none py-5 px-6\">\n                <div class=\"flex justify-between items-center\">\n                    <div>\n                        <div class=\"text-gray-900 text-lg\">You have requested to change your email address</div>\n                        <div class=\"text-xs text-gray-400\">Pokémon Customer Service - <EMAIL></div>\n                    </div>\n                    <div>\n                        <div class=\"text-xs text-gray-400\">24 Jun 2025 07:09 AM</div>\n                    </div>\n                </div>\n                <div class=\"flex mt-5\">\n                    <a class=\"download text-xs font-semibold bg-blue-700 py-1 px-3 rounded-md text-white\" href=\"#\" data-id=\"7292\">Download</a>\n                    <span class=\"mr-2\"></span>\n                    <button x-on:click=\"id = 0; document.querySelector(`[data-id='7292']`).remove()\" wire:click=\"delete(7292)\" class=\"text-xs font-semibold bg-red-700 py-1 px-3 rounded-md text-white\">Delete</button>\n                </div>\n            </div>\n            <iframe class=\"w-full flex flex-grow px-5\" srcdoc=\"\r\n\r\n\r\n\r\n\r\n&lt;html&gt;&lt;head&gt;&lt;meta charset=&quot;utf-8&quot; /&gt;&lt;/head&gt;&lt;body bgcolor=&quot;#FFFFFF&quot;&gt;\r\n&lt;style&gt;\r\n.ExternalClass * {line-height: 100%!important}\r\n\r\n.button {\r\nbackground: #53b662;\r\nborder-radius: 5px;\r\nborder: none;\r\ncolor: #FFFFFF;\r\ncursor: pointer;\r\nfont-size: 14px;\r\nfloat: left;\r\nline-height: 125%;\r\nmargin: 8px 0 0 0;\r\npadding: 0.75em 1.25em;\r\nvertical-align: middle;\r\ntext-align: center;\r\ntext-decoration: none;\r\nfont-family: verdana, arial, sans-serif;\r\n}\r\n\r\n&lt;/style&gt;\r\n\r\n    &lt;table width=&quot;600&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; align=&quot;center&quot; style=&quot;min-width: 600px;&quot;&gt;\r\n\r\n        &lt;tbody&gt;\r\n        &lt;tr background=&quot;https://assets.pokemon.com/static2/_ui/img/mail/header-bg.gif&quot;&gt;\r\n            &lt;td align=&quot;left&quot;&gt;\r\n                &lt;table width=&quot;600&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; align=&quot;center&quot;&gt;\r\n                    &lt;tbody&gt;&lt;tr&gt;\r\n                        &lt;td style=&quot;font-size: 86px; line-height: 86px;&quot;&gt;\r\n                            &lt;a target=&quot;blank&quot; href=&quot;http://pokemon.com&quot;&gt;&lt;img src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/logo.gif&quot;\r\n                                                              style=&quot;display: block; margin: 0; padding: 0; height:86px;&quot;\r\n                                                              margin=&quot;0&quot; padding=&quot;0&quot; width=&quot;auto&quot; height=&quot;86&quot;\r\n                                                              alt=&quot;Pokémon Logo&quot; border=&quot;0&quot;&gt;&lt;/a&gt;\r\n                        &lt;/td&gt;\r\n                        &lt;td align=&quot;right&quot; style=&quot;font-size: 86px; line-height: 86px;&quot;&gt;\r\n                            &lt;img src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/pikachu-1.gif&quot;\r\n                                 style=&quot;display: block; margin: 0; padding: 0; height:86px;&quot;\r\n                                 margin=&quot;0&quot; padding=&quot;0&quot; width=&quot;auto&quot; height=&quot;86&quot; alt=&quot;Pikachu&quot; border=&quot;0&quot;&gt;\r\n                        &lt;/td&gt;\r\n                    &lt;/tr&gt;\r\n                &lt;/tbody&gt;&lt;/table&gt;\r\n\r\n            &lt;/td&gt;\r\n        &lt;/tr&gt;\r\n\r\n        &lt;tr bgcolor=&quot;#eaeaea&quot;&gt;\r\n            &lt;td align=&quot;left&quot; style=&quot;font-size: 3px; line-height: 4px;&quot;&gt;\r\n                &lt;table width=&quot;600&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; align=&quot;center&quot;&gt;\r\n                     &lt;tbody&gt;&lt;tr&gt;\r\n                        &lt;td align=&quot;right&quot; style=&quot;font-size: 3px; line-height: 4px;&quot;&gt;\r\n                            &lt;img src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/pikachu-2.gif&quot;\r\n                                 style=&quot;display: block; margin: 0; padding: 0; height:4px !important; line-height: 4px !important; font-size: 4px !important;&quot;\r\n                                 margin=&quot;0&quot; padding=&quot;0&quot; width=&quot;auto&quot; height=&quot;4&quot; alt=&quot;Pikachu&quot; border=&quot;0&quot;&gt;\r\n                        &lt;/td&gt;\r\n                    &lt;/tr&gt;\r\n                &lt;/tbody&gt;&lt;/table&gt;\r\n            &lt;/td&gt;\r\n        &lt;/tr&gt;\r\n\r\n        &lt;tr bgcolor=&quot;#313131&quot;&gt;\r\n            &lt;td align=&quot;left&quot;&gt;\r\n                &lt;table width=&quot;600&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; align=&quot;center&quot;&gt;\r\n                    &lt;tbody&gt;&lt;tr&gt;\r\n                        &lt;td width=&quot;448&quot; style=&quot;font-size:23px !important; font-weight:400; padding-left: 23px;&quot;&gt;\r\n                            &lt;font color=&quot;#FFFFFF&quot; face=&quot;Arial,Helvetica,sans-serif&quot;&gt;\r\n                                Hello from Pokemon.com\r\n                            &lt;/font&gt;\r\n                        &lt;/td&gt;\r\n                        &lt;td align=&quot;right&quot;&gt;\r\n                            &lt;img src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/pikachu-3.gif&quot;\r\n                                 style=&quot;display: block; margin: 0; padding: 0; height:60px; line-height: 60px; font-size: 60px;&quot;\r\n                                 margin=&quot;0&quot; padding=&quot;0&quot; width=&quot;auto&quot; height=&quot;60&quot; alt=&quot;Pikachu&quot; border=&quot;0&quot;&gt;\r\n                        &lt;/td&gt;\r\n                    &lt;/tr&gt;\r\n                &lt;/tbody&gt;&lt;/table&gt;\r\n            &lt;/td&gt;\r\n        &lt;/tr&gt;\r\n\r\n        &lt;tr style=&quot;line-height: 9px!important font-size: 9px&quot;&gt;\r\n           &lt;td align=&quot;left&quot;&gt;\r\n                &lt;table width=&quot;100%&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; align=&quot;center&quot;&gt;\r\n                    &lt;tbody&gt;&lt;tr&gt;\r\n                        &lt;td width=&quot;600&quot; align=&quot;center&quot;&gt;\r\n                            &lt;table width=&quot;600&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; align=&quot;center&quot;&gt;\r\n                                &lt;tbody&gt;&lt;tr bgcolor=&quot;#ffffff&quot;&gt;\r\n                                    &lt;td align=&quot;left&quot; height=&quot;9&quot; style=&quot;line-height: 9px; font-size: 9px;&quot;&gt;\r\n                                        &lt;img src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/notch.gif&quot;\r\n                                             style=&quot;display: block; margin: 0; padding: 0; height:9px; line-height: 9px; font-size: 9px;&quot;\r\n                                             margin=&quot;0&quot; padding=&quot;0&quot; width=&quot;auto&quot; height=&quot;9&quot; alt=&quot;&quot; border=&quot;0&quot;&gt;\r\n                                    &lt;/td&gt;\r\n                                    &lt;td align=&quot;right&quot; height=&quot;9&quot; style=&quot;line-height: 9px; font-size: 9px;&quot;&gt;\r\n                                        &lt;img src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/pikachu-4.gif&quot;\r\n                                             style=&quot;display: block; margin: 0; padding: 0; height:9px; line-height: 9px; font-size: 9px;&quot;\r\n                                             margin=&quot;0&quot; padding=&quot;0&quot; width=&quot;auto&quot; height=&quot;9&quot; alt=&quot;Pikachu&quot; border=&quot;0&quot;&gt;\r\n                                    &lt;/td&gt;\r\n                                &lt;/tr&gt;\r\n                            &lt;/tbody&gt;&lt;/table&gt;\r\n\r\n                        &lt;/td&gt;\r\n                    &lt;/tr&gt;\r\n                &lt;/tbody&gt;&lt;/table&gt;\r\n            &lt;/td&gt;\r\n        &lt;/tr&gt;\r\n\r\n        &lt;tr&gt;\r\n            &lt;td height=&quot;35&quot;&gt;&lt;/td&gt;\r\n        &lt;/tr&gt;\r\n\r\n        &lt;tr&gt;\r\n            &lt;td align=&quot;left&quot;&gt;\r\n                &lt;table width=&quot;600&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; align=&quot;center&quot;&gt;\r\n                    &lt;tbody&gt;&lt;tr&gt;\r\n                        &lt;td width=&quot;11&quot;&gt;&lt;img src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/space.gif&quot; width=&quot;1&quot; height=&quot;1&quot; alt=&quot;&quot; border=&quot;0&quot;&gt;&lt;/td&gt;\r\n                        &lt;td width=&quot;250&quot;&gt;\r\n                            &lt;font color=&quot;#4f4f4f&quot; face=&quot;Arial,Helvetica,sans-serif&quot; style=&quot;font-size:16px; line-height:28px;&quot;&gt;\r\n                                \r\n\r\n\r\n&lt;p&gt;Dear Pokémon Trainer Club member,&lt;/p&gt;\r\n\r\n&lt;p&gt;It appears that you have recently changed the email address for your Pokémon Trainer Club account from this email address to a new email address. This email has been sent to you so you can verify this requested change.&lt;/p&gt;\r\n\r\n&lt;p&gt;To approve or reject this email address change, click the button below and follow the instructions. This link will expire in 24 hours. If it no longer works, please change your email address on Pokemon.com again to receive a new link.&lt;/p&gt;\r\n\r\n&lt;p&gt;If you did not request this change, do not click the Approve Email Address Change button below. Instead, go to www.pokemon.com to log in to your account and change your password. Please use your Pokémon Trainer Club account only to access sites operated by The Pokémon Company International, Pokémon Trading Card Game Live, Pokémon UNITE, Pokémon GO, and other official Pokémon apps that support Pokémon Trainer Club account log-in. You should not use your Pokémon Trainer Club password anywhere else. If you think you might have used your password for other sites or apps, we recommend you change your password immediately.&lt;/p&gt;\r\n\r\n&lt;p&gt;Visiting suspicious-looking websites that promise hacks or cheats for online games such as Pokémon Trading Card Game Live or Pokémon GO is not supported and is a violation of the games&#039; Terms of Use.&lt;/p&gt;\r\n\r\n&lt;p&gt;If you have additional questions, please contact us at &lt;a target=&quot;blank&quot; href=&quot;http://support.pokemon.com&quot; data-home=&quot;http://club.pokemon.com&quot;&gt;support.pokemon.com&lt;/a&gt;.&lt;/p&gt;\r\n\r\n\r\n                            &lt;/font&gt;\r\n                        &lt;/td&gt;\r\n                        &lt;td width=&quot;30&quot;&gt;&lt;img src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/space.gif&quot; width=&quot;1&quot; height=&quot;1&quot; alt=&quot;&quot; border=&quot;0&quot;&gt;&lt;/td&gt;\r\n                    &lt;/tr&gt;\r\n                &lt;/tbody&gt;&lt;/table&gt;\r\n            &lt;/td&gt;\r\n        &lt;/tr&gt;\r\n\r\n      &lt;tr&gt;\r\n        &lt;td align=&quot;left&quot;&gt;\r\n          &lt;table width=&quot;600&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; align=&quot;center&quot;&gt;\r\n            &lt;tbody&gt;&lt;tr&gt;\r\n              &lt;td width=&quot;11&quot;&gt;&lt;img src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/space.gif&quot; width=&quot;1&quot; height=&quot;1&quot; alt=&quot;&quot; border=&quot;0&quot;&gt;&lt;/td&gt;\r\n              &lt;td width=&quot;250&quot;&gt;\r\n                \r\n              &lt;/td&gt;\r\n              &lt;td width=&quot;30&quot;&gt;&lt;img src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/space.gif&quot; width=&quot;1&quot; height=&quot;1&quot; alt=&quot;&quot; border=&quot;0&quot;&gt;&lt;/td&gt;\r\n            &lt;/tr&gt;&lt;/tbody&gt;\r\n          &lt;/table&gt;\r\n        &lt;/td&gt;\r\n      &lt;/tr&gt;\r\n\r\n        &lt;tr&gt;\r\n        &lt;td width=&quot;600&quot; height=&quot;30&quot; align=&quot;left&quot; valign=&quot;top&quot;&gt;\r\n            &lt;img style=&quot;display:block&quot; src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/space.gif&quot; width=&quot;1&quot; height=&quot;1&quot; alt=&quot;&quot; border=&quot;0&quot;&gt;\r\n        &lt;/td&gt;\r\n      &lt;/tr&gt;\r\n\r\n      &lt;tr&gt;\r\n            &lt;td align=&quot;left&quot;&gt;\r\n                &lt;table width=&quot;600&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; align=&quot;center&quot;&gt;\r\n                    &lt;tbody&gt;&lt;tr&gt;\r\n                        &lt;td width=&quot;11&quot;&gt;&lt;img src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/space.gif&quot; width=&quot;1&quot; height=&quot;1&quot; alt=&quot;&quot; border=&quot;0&quot;&gt;&lt;/td&gt;\r\n                        &lt;td width=&quot;250&quot;&gt;\r\n                            &lt;font color=&quot;#4f4f4f&quot; face=&quot;Arial,Helvetica,sans-serif&quot; style=&quot;font-size:16px; line-height:28px;&quot;&gt;\r\n                                \r\n                            &lt;/font&gt;\r\n                        &lt;/td&gt;\r\n                        &lt;td width=&quot;30&quot;&gt;&lt;img src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/space.gif&quot; width=&quot;1&quot; height=&quot;1&quot; alt=&quot;&quot; border=&quot;0&quot;&gt;&lt;/td&gt;\r\n                    &lt;/tr&gt;\r\n                &lt;/tbody&gt;&lt;/table&gt;\r\n            &lt;/td&gt;\r\n        &lt;/tr&gt;\r\n\r\n        &lt;tr&gt;\r\n        &lt;td width=&quot;600&quot; height=&quot;30&quot; align=&quot;left&quot; valign=&quot;top&quot;&gt;\r\n            &lt;img style=&quot;display:block&quot; src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/space.gif&quot; width=&quot;1&quot; height=&quot;1&quot; alt=&quot;&quot; border=&quot;0&quot;&gt;\r\n        &lt;/td&gt;\r\n      &lt;/tr&gt;\r\n\r\n      &lt;tr&gt;\r\n            &lt;td align=&quot;left&quot;&gt;\r\n                &lt;table width=&quot;600&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; align=&quot;center&quot;&gt;\r\n                    &lt;tbody&gt;&lt;tr&gt;\r\n                        &lt;td width=&quot;11&quot;&gt;&lt;img src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/space.gif&quot; width=&quot;1&quot; height=&quot;1&quot; alt=&quot;&quot; border=&quot;0&quot;&gt;&lt;/td&gt;\r\n                        &lt;td width=&quot;250&quot;&gt;\r\n                            &lt;font color=&quot;#4f4f4f&quot; face=&quot;Arial,Helvetica,sans-serif&quot; style=&quot;font-size:16px; line-height:28px;&quot;&gt;\r\n                                Sincerely,&lt;br/&gt;\r\n&lt;b&gt;The Pokémon Company International&lt;/b&gt;\r\n                            &lt;/font&gt;\r\n                        &lt;/td&gt;\r\n                        &lt;td width=&quot;30&quot;&gt;&lt;img src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/space.gif&quot; width=&quot;1&quot; height=&quot;1&quot; alt=&quot;&quot; border=&quot;0&quot;&gt;&lt;/td&gt;\r\n                    &lt;/tr&gt;\r\n                &lt;/tbody&gt;&lt;/table&gt;\r\n            &lt;/td&gt;\r\n        &lt;/tr&gt;\r\n\r\n        &lt;tr&gt;\r\n        &lt;td width=&quot;600&quot; height=&quot;30&quot; align=&quot;left&quot; valign=&quot;top&quot;&gt;\r\n            &lt;img style=&quot;display:block&quot; src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/space.gif&quot; width=&quot;1&quot; height=&quot;1&quot; alt=&quot;&quot; border=&quot;0&quot;&gt;\r\n        &lt;/td&gt;\r\n      &lt;/tr&gt;\r\n\r\n      &lt;tr&gt;\r\n        &lt;td align=&quot;left&quot;&gt;\r\n          &lt;table width=&quot;600&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; align=&quot;center&quot;&gt;\r\n            &lt;tbody&gt;&lt;tr&gt;\r\n              &lt;td width=&quot;11&quot;&gt;&lt;img src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/space.gif&quot; width=&quot;1&quot; height=&quot;1&quot; alt=&quot;&quot; border=&quot;0&quot;&gt;&lt;/td&gt;\r\n              &lt;td width=&quot;250&quot;&gt;\r\n                \r\n&lt;p style=&quot;margin:0;padding:0&quot;&gt;\r\n&lt;a target=&quot;blank&quot; href=&quot;https://club.pokemon.com/us/pokemon-trainer-club/email-change-approval/49d9fd87c3028b1267f9825fddf6e825&quot; class=&quot;button button-green&quot;\r\n   style=&quot;background: #53b662;border-radius: 5px;border: none;color: #FFFFFF;cursor: pointer;font-size: 14px;float: left;line-height: 125%;margin: 8px 0 0 0;padding: 0.75em 1.25em;vertical-align: middle;text-align: center;text-decoration: none;font-family: verdana, arial, sans-serif;&quot;\r\n  &gt;\r\n\r\nApprove Email Address Change\r\n&lt;/a&gt;\r\n\r\n              &lt;/td&gt;\r\n              &lt;td width=&quot;30&quot;&gt;&lt;img src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/space.gif&quot; width=&quot;1&quot; height=&quot;1&quot; alt=&quot;&quot; border=&quot;0&quot;&gt;&lt;/td&gt;\r\n            &lt;/tr&gt;&lt;/tbody&gt;\r\n          &lt;/table&gt;\r\n        &lt;/td&gt;\r\n      &lt;/tr&gt;\r\n\r\n        &lt;tr&gt;\r\n            &lt;td align=&quot;center&quot; style=&quot;padding-top: 20px&quot;&gt;\r\n                &lt;img src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/pokemon-email-footer.png&quot; alt=&quot;Pokemons&quot; width=&quot;auto&quot; height=&quot;auto&quot; border=&quot;0&quot;&gt;\r\n            &lt;/td&gt;\r\n        &lt;/tr&gt;\r\n\r\n        &lt;tr&gt;\r\n            &lt;td align=&quot;center&quot; bgcolor=&quot;#313131&quot;&gt;\r\n                &lt;table width=&quot;600&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; align=&quot;center&quot;&gt;\r\n                    &lt;tbody&gt;&lt;tr&gt;\r\n                        &lt;td align=&quot;center&quot;&gt;\r\n                            &lt;img src=&quot;https://assets.pokemon.com/static2/_ui/img/mail/top-footer.gif&quot; alt=&quot;Pokemons&quot; width=&quot;auto&quot; height=&quot;auto&quot; border=&quot;0&quot;&gt;\r\n                        &lt;/td&gt;\r\n                    &lt;/tr&gt;\r\n                &lt;/tbody&gt;&lt;/table&gt;\r\n            &lt;/td&gt;\r\n        &lt;/tr&gt;\r\n\r\n        &lt;tr&gt;\r\n            &lt;td bgcolor=&quot;#1f1f1f&quot; height=&quot;80&quot;&gt;\r\n                &lt;table width=&quot;550&quot; cellpadding=&quot;0&quot; cellspacing=&quot;0&quot; border=&quot;0&quot; align=&quot;center&quot;&gt;\r\n                &lt;tbody&gt;\r\n                    &lt;tr&gt;\r\n                        &lt;td align=&quot;left&quot; bgcolor=&quot;#1f1f1f&quot;&gt;\r\n                          &lt;span style=&quot;color: #fff; font-size:18px; font-weight: bold;&quot;&gt;The Pokémon Company&lt;/span&gt;\r\n                        &lt;/td&gt;\r\n                        \r\n                            &lt;td align=&quot;left&quot; bgcolor=&quot;#1f1f1f&quot;&gt;\r\n                &lt;a target=&quot;blank&quot; href=&quot;https://corporate.pokemon.com/en-us/&quot;\r\n                                   face=&quot;Arial,Helvetica,sans-serif&quot;\r\n                                   style=&quot;color: #5f5f5f; float: left; font-size:13px; font-weight: bold; line-height:20px; margin-top: 6px; text-transform: uppercase; text-decoration: none;&quot;\r\n                                &gt;About TPCI&lt;/a&gt;\r\n                            &lt;/td&gt;\r\n                        \r\n                        &lt;td align=&quot;left&quot; bgcolor=&quot;#1f1f1f&quot;&gt;\r\n\t\t\t\t&lt;a target=&quot;blank&quot; href=&quot;http://www.pokemon.com/us/terms-of-use/&quot;\r\n                             face=&quot;Arial,Helvetica,sans-serif&quot;\r\n                             style=&quot;color: #5f5f5f; float: left; font-size:13px; font-weight: bold; line-height:20px; margin-top: 6px; text-transform: uppercase; text-decoration: none;&quot;\r\n                          &gt;Terms of use&lt;/a&gt;\r\n                        &lt;/td&gt;\r\n                        &lt;td style=&quot;text-align:left; background-color: #1f1f1f&quot;&gt;\r\n\t\t\t\t&lt;a target=&quot;blank&quot; href=&quot;http://www.pokemon.com/us/cookie-page/&quot;  id=&quot;cookieLink&quot;\r\n                             face=&quot;Arial,Helvetica,sans-serif&quot;\r\n                             style=&quot;color: #5f5f5f; float: left; font-size:13px; font-weight: bold; line-height:20px; margin-top: 6px; text-transform: uppercase; text-decoration: none;&quot;\r\n                          &gt;Cookie Page&lt;/a&gt;\r\n                        &lt;/td&gt;\r\n                        &lt;td align=&quot;left&quot; bgcolor=&quot;#1f1f1f&quot;&gt;\r\n\t\t\t\t&lt;a target=&quot;blank&quot; href=&quot;http://www.pokemon.com/us/privacy-notice/&quot;\r\n                             face=&quot;Arial,Helvetica,sans-serif&quot;\r\n                             style=&quot;color: #5f5f5f; float: left; font-size:13px; font-weight: bold; line-height:20px; margin-top: 6px; text-transform: uppercase; text-decoration: none;&quot;\r\n                          &gt;Privacy Notice&lt;/a&gt;\r\n                        &lt;/td&gt;\r\n                    &lt;/tr&gt;\r\n                    &lt;tr&gt;\r\n                     &lt;td colspan=&quot;4&quot; style=&quot;color: #fff&quot;&gt;\r\n\r\n10400 NE 4th Street, Suite 2800, Bellevue, WA 98004\r\n\r\n                     &lt;/td&gt;\r\n                    &lt;/tr&gt;\r\n                &lt;/tbody&gt;\r\n                &lt;/table&gt;\r\n            &lt;/td&gt;\r\n        &lt;/tr&gt;\r\n\r\n    &lt;/tbody&gt;&lt;/table&gt;\r\n\r\n&lt;/body&gt;&lt;/html&gt;\r\n\" frameborder=\"0\"></iframe>\n                    </div>\n            </div>\n</main>", "dispatches": [{"event": "<PERSON><PERSON><PERSON><PERSON>", "data": null}, {"event": "loadDownload", "data": null}], "dirty": ["initial"]}, "serverMemo": {"htmlHash": "7a58a8e5", "data": {"messages": [{"subject": "You have requested to change your email address", "sender_name": "Pokémon Customer Service", "sender_email": "<EMAIL>", "timestamp": {"date": "2025-06-24 07:09:35.000000", "timezone_type": 1, "timezone": "+00:00"}, "date": "24 Jun 2025 07:09 AM", "datediff": "1 hour ago", "id": 7292, "content": "\r\n\r\n\r\n\r\n\r\n<html><head><meta charset=\"utf-8\" /></head><body bgcolor=\"#FFFFFF\">\r\n<style>\r\n.ExternalClass * {line-height: 100%!important}\r\n\r\n.button {\r\nbackground: #53b662;\r\nborder-radius: 5px;\r\nborder: none;\r\ncolor: #FFFFFF;\r\ncursor: pointer;\r\nfont-size: 14px;\r\nfloat: left;\r\nline-height: 125%;\r\nmargin: 8px 0 0 0;\r\npadding: 0.75em 1.25em;\r\nvertical-align: middle;\r\ntext-align: center;\r\ntext-decoration: none;\r\nfont-family: verdana, arial, sans-serif;\r\n}\r\n\r\n</style>\r\n\r\n    <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" align=\"center\" style=\"min-width: 600px;\">\r\n\r\n        <tbody>\r\n        <tr background=\"https://assets.pokemon.com/static2/_ui/img/mail/header-bg.gif\">\r\n            <td align=\"left\">\r\n                <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" align=\"center\">\r\n                    <tbody><tr>\r\n                        <td style=\"font-size: 86px; line-height: 86px;\">\r\n                            <a target=\"blank\" href=\"http://pokemon.com\"><img src=\"https://assets.pokemon.com/static2/_ui/img/mail/logo.gif\"\r\n                                                              style=\"display: block; margin: 0; padding: 0; height:86px;\"\r\n                                                              margin=\"0\" padding=\"0\" width=\"auto\" height=\"86\"\r\n                                                              alt=\"Pokémon Logo\" border=\"0\"></a>\r\n                        </td>\r\n                        <td align=\"right\" style=\"font-size: 86px; line-height: 86px;\">\r\n                            <img src=\"https://assets.pokemon.com/static2/_ui/img/mail/pikachu-1.gif\"\r\n                                 style=\"display: block; margin: 0; padding: 0; height:86px;\"\r\n                                 margin=\"0\" padding=\"0\" width=\"auto\" height=\"86\" alt=\"Pikachu\" border=\"0\">\r\n                        </td>\r\n                    </tr>\r\n                </tbody></table>\r\n\r\n            </td>\r\n        </tr>\r\n\r\n        <tr bgcolor=\"#eaeaea\">\r\n            <td align=\"left\" style=\"font-size: 3px; line-height: 4px;\">\r\n                <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" align=\"center\">\r\n                     <tbody><tr>\r\n                        <td align=\"right\" style=\"font-size: 3px; line-height: 4px;\">\r\n                            <img src=\"https://assets.pokemon.com/static2/_ui/img/mail/pikachu-2.gif\"\r\n                                 style=\"display: block; margin: 0; padding: 0; height:4px !important; line-height: 4px !important; font-size: 4px !important;\"\r\n                                 margin=\"0\" padding=\"0\" width=\"auto\" height=\"4\" alt=\"Pikachu\" border=\"0\">\r\n                        </td>\r\n                    </tr>\r\n                </tbody></table>\r\n            </td>\r\n        </tr>\r\n\r\n        <tr bgcolor=\"#313131\">\r\n            <td align=\"left\">\r\n                <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" align=\"center\">\r\n                    <tbody><tr>\r\n                        <td width=\"448\" style=\"font-size:23px !important; font-weight:400; padding-left: 23px;\">\r\n                            <font color=\"#FFFFFF\" face=\"Arial,Helvetica,sans-serif\">\r\n                                Hello from Pokemon.com\r\n                            </font>\r\n                        </td>\r\n                        <td align=\"right\">\r\n                            <img src=\"https://assets.pokemon.com/static2/_ui/img/mail/pikachu-3.gif\"\r\n                                 style=\"display: block; margin: 0; padding: 0; height:60px; line-height: 60px; font-size: 60px;\"\r\n                                 margin=\"0\" padding=\"0\" width=\"auto\" height=\"60\" alt=\"Pikachu\" border=\"0\">\r\n                        </td>\r\n                    </tr>\r\n                </tbody></table>\r\n            </td>\r\n        </tr>\r\n\r\n        <tr style=\"line-height: 9px!important font-size: 9px\">\r\n           <td align=\"left\">\r\n                <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" align=\"center\">\r\n                    <tbody><tr>\r\n                        <td width=\"600\" align=\"center\">\r\n                            <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" align=\"center\">\r\n                                <tbody><tr bgcolor=\"#ffffff\">\r\n                                    <td align=\"left\" height=\"9\" style=\"line-height: 9px; font-size: 9px;\">\r\n                                        <img src=\"https://assets.pokemon.com/static2/_ui/img/mail/notch.gif\"\r\n                                             style=\"display: block; margin: 0; padding: 0; height:9px; line-height: 9px; font-size: 9px;\"\r\n                                             margin=\"0\" padding=\"0\" width=\"auto\" height=\"9\" alt=\"\" border=\"0\">\r\n                                    </td>\r\n                                    <td align=\"right\" height=\"9\" style=\"line-height: 9px; font-size: 9px;\">\r\n                                        <img src=\"https://assets.pokemon.com/static2/_ui/img/mail/pikachu-4.gif\"\r\n                                             style=\"display: block; margin: 0; padding: 0; height:9px; line-height: 9px; font-size: 9px;\"\r\n                                             margin=\"0\" padding=\"0\" width=\"auto\" height=\"9\" alt=\"Pikachu\" border=\"0\">\r\n                                    </td>\r\n                                </tr>\r\n                            </tbody></table>\r\n\r\n                        </td>\r\n                    </tr>\r\n                </tbody></table>\r\n            </td>\r\n        </tr>\r\n\r\n        <tr>\r\n            <td height=\"35\"></td>\r\n        </tr>\r\n\r\n        <tr>\r\n            <td align=\"left\">\r\n                <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" align=\"center\">\r\n                    <tbody><tr>\r\n                        <td width=\"11\"><img src=\"https://assets.pokemon.com/static2/_ui/img/mail/space.gif\" width=\"1\" height=\"1\" alt=\"\" border=\"0\"></td>\r\n                        <td width=\"250\">\r\n                            <font color=\"#4f4f4f\" face=\"Arial,Helvetica,sans-serif\" style=\"font-size:16px; line-height:28px;\">\r\n                                \r\n\r\n\r\n<p>Dear Pokémon Trainer Club member,</p>\r\n\r\n<p>It appears that you have recently changed the email address for your Pokémon Trainer Club account from this email address to a new email address. This email has been sent to you so you can verify this requested change.</p>\r\n\r\n<p>To approve or reject this email address change, click the button below and follow the instructions. This link will expire in 24 hours. If it no longer works, please change your email address on Pokemon.com again to receive a new link.</p>\r\n\r\n<p>If you did not request this change, do not click the Approve Email Address Change button below. Instead, go to www.pokemon.com to log in to your account and change your password. Please use your Pokémon Trainer Club account only to access sites operated by The Pokémon Company International, Pokémon Trading Card Game Live, Pokémon UNITE, Pokémon GO, and other official Pokémon apps that support Pokémon Trainer Club account log-in. You should not use your Pokémon Trainer Club password anywhere else. If you think you might have used your password for other sites or apps, we recommend you change your password immediately.</p>\r\n\r\n<p>Visiting suspicious-looking websites that promise hacks or cheats for online games such as Pokémon Trading Card Game Live or Pokémon GO is not supported and is a violation of the games' Terms of Use.</p>\r\n\r\n<p>If you have additional questions, please contact us at <a target=\"blank\" href=\"http://support.pokemon.com\" data-home=\"http://club.pokemon.com\">support.pokemon.com</a>.</p>\r\n\r\n\r\n                            </font>\r\n                        </td>\r\n                        <td width=\"30\"><img src=\"https://assets.pokemon.com/static2/_ui/img/mail/space.gif\" width=\"1\" height=\"1\" alt=\"\" border=\"0\"></td>\r\n                    </tr>\r\n                </tbody></table>\r\n            </td>\r\n        </tr>\r\n\r\n      <tr>\r\n        <td align=\"left\">\r\n          <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" align=\"center\">\r\n            <tbody><tr>\r\n              <td width=\"11\"><img src=\"https://assets.pokemon.com/static2/_ui/img/mail/space.gif\" width=\"1\" height=\"1\" alt=\"\" border=\"0\"></td>\r\n              <td width=\"250\">\r\n                \r\n              </td>\r\n              <td width=\"30\"><img src=\"https://assets.pokemon.com/static2/_ui/img/mail/space.gif\" width=\"1\" height=\"1\" alt=\"\" border=\"0\"></td>\r\n            </tr></tbody>\r\n          </table>\r\n        </td>\r\n      </tr>\r\n\r\n        <tr>\r\n        <td width=\"600\" height=\"30\" align=\"left\" valign=\"top\">\r\n            <img style=\"display:block\" src=\"https://assets.pokemon.com/static2/_ui/img/mail/space.gif\" width=\"1\" height=\"1\" alt=\"\" border=\"0\">\r\n        </td>\r\n      </tr>\r\n\r\n      <tr>\r\n            <td align=\"left\">\r\n                <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" align=\"center\">\r\n                    <tbody><tr>\r\n                        <td width=\"11\"><img src=\"https://assets.pokemon.com/static2/_ui/img/mail/space.gif\" width=\"1\" height=\"1\" alt=\"\" border=\"0\"></td>\r\n                        <td width=\"250\">\r\n                            <font color=\"#4f4f4f\" face=\"Arial,Helvetica,sans-serif\" style=\"font-size:16px; line-height:28px;\">\r\n                                \r\n                            </font>\r\n                        </td>\r\n                        <td width=\"30\"><img src=\"https://assets.pokemon.com/static2/_ui/img/mail/space.gif\" width=\"1\" height=\"1\" alt=\"\" border=\"0\"></td>\r\n                    </tr>\r\n                </tbody></table>\r\n            </td>\r\n        </tr>\r\n\r\n        <tr>\r\n        <td width=\"600\" height=\"30\" align=\"left\" valign=\"top\">\r\n            <img style=\"display:block\" src=\"https://assets.pokemon.com/static2/_ui/img/mail/space.gif\" width=\"1\" height=\"1\" alt=\"\" border=\"0\">\r\n        </td>\r\n      </tr>\r\n\r\n      <tr>\r\n            <td align=\"left\">\r\n                <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" align=\"center\">\r\n                    <tbody><tr>\r\n                        <td width=\"11\"><img src=\"https://assets.pokemon.com/static2/_ui/img/mail/space.gif\" width=\"1\" height=\"1\" alt=\"\" border=\"0\"></td>\r\n                        <td width=\"250\">\r\n                            <font color=\"#4f4f4f\" face=\"Arial,Helvetica,sans-serif\" style=\"font-size:16px; line-height:28px;\">\r\n                                Sincerely,<br/>\r\n<b>The Pokémon Company International</b>\r\n                            </font>\r\n                        </td>\r\n                        <td width=\"30\"><img src=\"https://assets.pokemon.com/static2/_ui/img/mail/space.gif\" width=\"1\" height=\"1\" alt=\"\" border=\"0\"></td>\r\n                    </tr>\r\n                </tbody></table>\r\n            </td>\r\n        </tr>\r\n\r\n        <tr>\r\n        <td width=\"600\" height=\"30\" align=\"left\" valign=\"top\">\r\n            <img style=\"display:block\" src=\"https://assets.pokemon.com/static2/_ui/img/mail/space.gif\" width=\"1\" height=\"1\" alt=\"\" border=\"0\">\r\n        </td>\r\n      </tr>\r\n\r\n      <tr>\r\n        <td align=\"left\">\r\n          <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" align=\"center\">\r\n            <tbody><tr>\r\n              <td width=\"11\"><img src=\"https://assets.pokemon.com/static2/_ui/img/mail/space.gif\" width=\"1\" height=\"1\" alt=\"\" border=\"0\"></td>\r\n              <td width=\"250\">\r\n                \r\n<p style=\"margin:0;padding:0\">\r\n<a target=\"blank\" href=\"https://club.pokemon.com/us/pokemon-trainer-club/email-change-approval/49d9fd87c3028b1267f9825fddf6e825\" class=\"button button-green\"\r\n   style=\"background: #53b662;border-radius: 5px;border: none;color: #FFFFFF;cursor: pointer;font-size: 14px;float: left;line-height: 125%;margin: 8px 0 0 0;padding: 0.75em 1.25em;vertical-align: middle;text-align: center;text-decoration: none;font-family: verdana, arial, sans-serif;\"\r\n  >\r\n\r\nApprove Email Address Change\r\n</a>\r\n\r\n              </td>\r\n              <td width=\"30\"><img src=\"https://assets.pokemon.com/static2/_ui/img/mail/space.gif\" width=\"1\" height=\"1\" alt=\"\" border=\"0\"></td>\r\n            </tr></tbody>\r\n          </table>\r\n        </td>\r\n      </tr>\r\n\r\n        <tr>\r\n            <td align=\"center\" style=\"padding-top: 20px\">\r\n                <img src=\"https://assets.pokemon.com/static2/_ui/img/mail/pokemon-email-footer.png\" alt=\"Pokemons\" width=\"auto\" height=\"auto\" border=\"0\">\r\n            </td>\r\n        </tr>\r\n\r\n        <tr>\r\n            <td align=\"center\" bgcolor=\"#313131\">\r\n                <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" align=\"center\">\r\n                    <tbody><tr>\r\n                        <td align=\"center\">\r\n                            <img src=\"https://assets.pokemon.com/static2/_ui/img/mail/top-footer.gif\" alt=\"Pokemons\" width=\"auto\" height=\"auto\" border=\"0\">\r\n                        </td>\r\n                    </tr>\r\n                </tbody></table>\r\n            </td>\r\n        </tr>\r\n\r\n        <tr>\r\n            <td bgcolor=\"#1f1f1f\" height=\"80\">\r\n                <table width=\"550\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" align=\"center\">\r\n                <tbody>\r\n                    <tr>\r\n                        <td align=\"left\" bgcolor=\"#1f1f1f\">\r\n                          <span style=\"color: #fff; font-size:18px; font-weight: bold;\">The Pokémon Company</span>\r\n                        </td>\r\n                        \r\n                            <td align=\"left\" bgcolor=\"#1f1f1f\">\r\n                <a target=\"blank\" href=\"https://corporate.pokemon.com/en-us/\"\r\n                                   face=\"Arial,Helvetica,sans-serif\"\r\n                                   style=\"color: #5f5f5f; float: left; font-size:13px; font-weight: bold; line-height:20px; margin-top: 6px; text-transform: uppercase; text-decoration: none;\"\r\n                                >About TPCI</a>\r\n                            </td>\r\n                        \r\n                        <td align=\"left\" bgcolor=\"#1f1f1f\">\r\n\t\t\t\t<a target=\"blank\" href=\"http://www.pokemon.com/us/terms-of-use/\"\r\n                             face=\"Arial,Helvetica,sans-serif\"\r\n                             style=\"color: #5f5f5f; float: left; font-size:13px; font-weight: bold; line-height:20px; margin-top: 6px; text-transform: uppercase; text-decoration: none;\"\r\n                          >Terms of use</a>\r\n                        </td>\r\n                        <td style=\"text-align:left; background-color: #1f1f1f\">\r\n\t\t\t\t<a target=\"blank\" href=\"http://www.pokemon.com/us/cookie-page/\"  id=\"cookieLink\"\r\n                             face=\"Arial,Helvetica,sans-serif\"\r\n                             style=\"color: #5f5f5f; float: left; font-size:13px; font-weight: bold; line-height:20px; margin-top: 6px; text-transform: uppercase; text-decoration: none;\"\r\n                          >Cookie Page</a>\r\n                        </td>\r\n                        <td align=\"left\" bgcolor=\"#1f1f1f\">\r\n\t\t\t\t<a target=\"blank\" href=\"http://www.pokemon.com/us/privacy-notice/\"\r\n                             face=\"Arial,Helvetica,sans-serif\"\r\n                             style=\"color: #5f5f5f; float: left; font-size:13px; font-weight: bold; line-height:20px; margin-top: 6px; text-transform: uppercase; text-decoration: none;\"\r\n                          >Privacy Notice</a>\r\n                        </td>\r\n                    </tr>\r\n                    <tr>\r\n                     <td colspan=\"4\" style=\"color: #fff\">\r\n\r\n10400 NE 4th Street, Suite 2800, Bellevue, WA 98004\r\n\r\n                     </td>\r\n                    </tr>\r\n                </tbody>\r\n                </table>\r\n            </td>\r\n        </tr>\r\n\r\n    </tbody></table>\r\n\r\n</body></html>\r\n", "attachments": []}], "initial": true}, "checksum": "1a4f371f2bb3143f1602579df8e051f799a0c654a9cc2b6270e5a5dbd929b0db"}}}, {"source": "ajax", "data": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <meta http-equiv=\"X-UA-Compatible\" content=\"ie=edge\">\n        <title>Dujaw Store</title>\n        \n        <link rel=\"icon\" href=\"https://dujaw.com/storage/public/images/joystick.png\">\n        <link href=\"https://cdn.quilljs.com/1.3.6/quill.snow.css\" rel=\"preload\" as=\"style\" onload=\"this.onload=null;this.rel='stylesheet'\">\n    <link rel=\"preload\" as=\"style\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css\" integrity=\"sha512-+4zCK9k+qNFUR5X+cKL9EIR+ZOhtIloNl9GIKS57V1MyNsYpYcUrUeQc9vNfzsWfV28IaLL3i96P9sdNyeRssA==\" crossorigin=\"anonymous\" onload=\"this.onload=null;this.rel='stylesheet'\" />\n    <link rel=\"preload\" as=\"style\" href=\"https://dujaw.com/css/vendor.css\" onload=\"this.onload=null;this.rel='stylesheet'\">\n    <link rel=\"stylesheet\" href=\"https://dujaw.com/css/common.css\">\n    <script src=\"https://dujaw.com/vendor/Shortcode/Shortcode.js\"></script>\n    <script src=\"https://dujaw.com/js/app.js\" defer></script>\n    <style >[wire\\:loading], [wire\\:loading\\.delay], [wire\\:loading\\.inline-block], [wire\\:loading\\.inline], [wire\\:loading\\.block], [wire\\:loading\\.flex], [wire\\:loading\\.table], [wire\\:loading\\.grid], [wire\\:loading\\.inline-flex] {display: none;}[wire\\:loading\\.delay\\.shortest], [wire\\:loading\\.delay\\.shorter], [wire\\:loading\\.delay\\.short], [wire\\:loading\\.delay\\.long], [wire\\:loading\\.delay\\.longer], [wire\\:loading\\.delay\\.longest] {display:none;}[wire\\:offline] {display: none;}[wire\\:dirty]:not(textarea):not(input):not(select) {display: none;}input:-webkit-autofill, select:-webkit-autofill, textarea:-webkit-autofill {animation-duration: 50000s;animation-name: livewireautofill;}@keyframes livewireautofill { from {} }</style>\n    \n        \n        <meta name=\"csrf-token\" content=\"6qAYEvONeT48h15gbYEUnphrGGhVo0VHqUqXluHD\">\n<link rel=\"preconnect\" href=\"https://fonts.googleapis.com\">\n<link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossorigin>\n<link href=\"https://fonts.googleapis.com/css2?family=Kadwa:wght@400;600;700&display=swap\" rel=\"preload\" as=\"style\" onload=\"this.onload=null;this.rel='stylesheet'\">\n<link href=\"https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap\" rel=\"preload\" as=\"style\" onload=\"this.onload=null;this.rel='stylesheet'\">\n<style>\n:root {\n  --head-font: \"Kadwa\";\n  --body-font: \"Poppins\";\n  --primary: #0155b5;\n  --secondary: #2fc10a;\n  --tertiary: #d2ab3e;\n}\n</style>\n<script>\n  let captcha_name = \"off\";\n  let site_key = \"\";\n  if(captcha_name && captcha_name !== \"off\") {\n    site_key = \"\";\n  }\n  let strings = {\"Get back to MailBox\":\"Get back to MailBox\",\"Enter Username\":\"Enter Username\",\"Select Domain\":\"Select Domain\",\"Create\":\"Create\",\"Random\":\"Random\",\"Custom\":\"Custom\",\"Menu\":\"Menu\",\"Cancel\":\"Cancel\",\"Copy\":\"Copy\",\"Refresh\":\"Refresh\",\"New\":\"New\",\"Delete\":\"Delete\",\"Download\":\"Download\",\"Fetching\":\"Fetching\",\"Empty Inbox\":\"Empty Inbox\",\"Error\":\"Error\",\"Success\":\"Success\",\"Close\":\"Close\",\"Email ID Copied to Clipboard\":\"Email ID Copied to Clipboard\",\"Please enter Username\":\"Please enter Username\",\"Please Select a Domain\":\"Please Select a Domain\",\"Username not allowed\":\"Username not allowed\",\"Your Temporary Email Address\":\"Your Temporary Email Address\",\"Attachments\":\"Attachments\",\"Blocked\":\"Blocked\",\"Emails from\":\"Emails from\",\"are blocked by Admin\":\"are blocked by Admin\",\"No Messages\":\"No Messages\",\"Waiting for Incoming Messages\":\"Waiting for Incoming Messages\",\"Scan QR Code to access\":\"Scan QR Code to access\",\"Create your own Temp Mail\":\"Create your own Temp Mail\",\"Your Temprorary Email\":\"Your Temprorary Email\",\"Enter a Username and Select the Domain\":\"Enter a Username and Select the Domain\",\"Username length cannot be less than\":\"Username length cannot be less than\",\"and greator than\":\"and greator than\",\"Create a Random Email\":\"Create a Random Email\",\"Sender\":\"Sender\",\"Subject\":\"Subject\",\"Time\":\"Time\",\"Open\":\"Open\",\"Go Back to Inbox\":\"Go Back to Inbox\",\"Date\":\"Date\",\"Copyright\":\"Copyright\",\"Ad Blocker Detected\":\"Ad Blocker Detected\",\"Disable the Ad Blocker to use \":\"Disable the Ad Blocker to use \",\"Your temporary email address is ready\":\"Your temporary email address is ready\",\"You have reached daily limit of MAX \":\"You have reached daily limit of MAX \",\" temp mail\":\" temp mail\",\"Sorry! That email is already been used by someone else. Please try a different email address.\":\"Sorry! That email is already been used by someone else. Please try a different email address.\",\"Invalid Captcha. Please try again\":\"Invalid Captcha. Please try again\",\"Invalid Password\":\"Invalid Password\",\"Password\":\"Password\",\"Unlock\":\"Unlock\",\"Your Name\":\"Your Name\",\"Enter your Name\":\"Enter your Name\",\"Your Email\":\"Your Email\",\"Enter your Email\":\"Enter your Email\",\"Message\":\"Message\",\"Enter your Message\":\"Enter your Message\",\"Send Message\":\"Send Message\"}\n  const __ = (string) => {\n    if(strings[string] !== undefined) {\n      return strings[string];\n    } else {\n      return string;\n    }\n  }\n</script>\n</head>\n<body>\n    <div class=\"default-theme\">\n        <div class=\"flex flex-wrap\">\n            <div class=\"w-full lg:w-1/4 bg-blue-700 py-6 lg:min-h-screen\" style=\"background-color: #0155b5\">\n                <div class=\"flex justify-center p-3 mb-10\">\n                    <a href=\"https://dujaw.com\">\n                                                <img class=\"w-logo\" src=\"https://dujaw.com/storage/public/images/joystick.png\" alt=\"logo\">\n                                            </a>\n                </div>\n                                <div wire:id=\"8hgMapOIueF0GsRQfDhh\" wire:initial-data=\"{&quot;fingerprint&quot;:{&quot;id&quot;:&quot;8hgMapOIueF0GsRQfDhh&quot;,&quot;name&quot;:&quot;frontend.actions&quot;,&quot;locale&quot;:&quot;en&quot;,&quot;path&quot;:&quot;mailbox&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;v&quot;:&quot;acj&quot;},&quot;effects&quot;:{&quot;listeners&quot;:[&quot;syncEmail&quot;,&quot;checkReCaptcha3&quot;]},&quot;serverMemo&quot;:{&quot;children&quot;:[],&quot;errors&quot;:[],&quot;htmlHash&quot;:&quot;da08db03&quot;,&quot;data&quot;:{&quot;in_app&quot;:false,&quot;user&quot;:null,&quot;domain&quot;:null,&quot;domains&quot;:[&quot;dujaw.com&quot;,&quot;fgeta.com&quot;,&quot;dennisgls26.com&quot;,&quot;aipicz.com&quot;,&quot;gamersparky26.com&quot;,&quot;withsd.com&quot;,&quot;zzetu.com&quot;,&quot;dxgamers.com&quot;,&quot;ulnik.com&quot;,&quot;rdmail.info&quot;,&quot;ziuwi.com&quot;,&quot;tseru.com&quot;,&quot;gohuki.com&quot;,&quot;1em0nstore.win&quot;,&quot;1em0nstore.trade&quot;,&quot;1emonstore.trade&quot;],&quot;email&quot;:&quot;<EMAIL>&quot;,&quot;emails&quot;:[&quot;<EMAIL>&quot;],&quot;captcha&quot;:null},&quot;dataMeta&quot;:[],&quot;checksum&quot;:&quot;8eed15e847235b1328ee7474423b5ed6d178e7867ac931aa2f39e4b7be8d36b6&quot;}}\" x-data=\"{ in_app: false }\">\n    <div x-show.transition.in=\"in_app\" class=\"app-action mt-4 px-8\" style=\"display: none;\">\n                <form wire:submit.prevent=\"create\" class=\"lg:max-w-72 lg:mx-auto\" method=\"post\">\n                        <input class=\"block appearance-none w-full border-0 rounded-md py-4 px-5 bg-white text-white bg-opacity-10 focus:outline-none placeholder-white placeholder-opacity-50\" type=\"text\" name=\"user\" id=\"user\" wire:model.defer=\"user\" placeholder=\"Enter Username\">\n            <div class=\"divider mt-5\"></div>\n            <div class=\"relative\">\n                <div class=\"relative\" x-data=\"{ open: false }\" @click.away=\"open = false\" @close.stop=\"open = false\">\n    <div @click=\"open = ! open\">\n        <input x-ref=\"domain\" type=\"text\" class=\"block appearance-none w-full border-0 bg-white text-white py-4 px-5 pr-8 bg-opacity-10 rounded-md cursor-pointer focus:outline-none select-none placeholder-white placeholder-opacity-50\" placeholder=\"Select Domain\" name=\"domain\" id=\"domain\" wire:model=\"domain\" readonly>\n    </div>\n\n    <div x-show=\"open\"\n            x-transition:enter=\"transition ease-out duration-200\"\n            x-transition:enter-start=\"transform opacity-0 scale-95\"\n            x-transition:enter-end=\"transform opacity-100 scale-100\"\n            x-transition:leave=\"transition ease-in duration-75\"\n            x-transition:leave-start=\"transform opacity-100 scale-100\"\n            x-transition:leave-end=\"transform opacity-0 scale-95\"\n            class=\"absolute z-50 mt-2 w-full rounded-md shadow-lg origin-top-right right-0\"\n            style=\"display: none;\"\n            @click=\"open = false\">\n        <div class=\"rounded-md shadow-xs max-h-96 overflow-y-auto py-1 bg-white\">\n            <a x-on:click=\"$refs.domain.value = 'dujaw.com'; $wire.setDomain('dujaw.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>dujaw.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'fgeta.com'; $wire.setDomain('fgeta.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>fgeta.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'dennisgls26.com'; $wire.setDomain('dennisgls26.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>dennisgls26.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'aipicz.com'; $wire.setDomain('aipicz.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>aipicz.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'gamersparky26.com'; $wire.setDomain('gamersparky26.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>gamersparky26.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'withsd.com'; $wire.setDomain('withsd.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>withsd.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'zzetu.com'; $wire.setDomain('zzetu.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>zzetu.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'dxgamers.com'; $wire.setDomain('dxgamers.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>dxgamers.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'ulnik.com'; $wire.setDomain('ulnik.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>ulnik.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'rdmail.info'; $wire.setDomain('rdmail.info')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>rdmail.info</a>\n                                                <a x-on:click=\"$refs.domain.value = 'ziuwi.com'; $wire.setDomain('ziuwi.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>ziuwi.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'tseru.com'; $wire.setDomain('tseru.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>tseru.com</a>\n                                                <a x-on:click=\"$refs.domain.value = 'gohuki.com'; $wire.setDomain('gohuki.com')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>gohuki.com</a>\n                                                <a x-on:click=\"$refs.domain.value = '1em0nstore.win'; $wire.setDomain('1em0nstore.win')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>1em0nstore.win</a>\n                                                <a x-on:click=\"$refs.domain.value = '1em0nstore.trade'; $wire.setDomain('1em0nstore.trade')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>1em0nstore.trade</a>\n                                                <a x-on:click=\"$refs.domain.value = '1emonstore.trade'; $wire.setDomain('1emonstore.trade')\" class='block px-4 py-2 text-sm leading-5 text-gray-700 cursor-pointer hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out'>1emonstore.trade</a>\n        </div>\n    </div>\n</div>\n                <div class=\"pointer-events-none absolute inset-y-0 right-0 flex items-center px-5 text-white\">\n                    <svg class=\"fill-current h-4 w-4\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\"><path d=\"M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z\"/></svg>\n                </div>\n            </div>\n            <div class=\"divider mt-5\"></div>\n            <input id=\"create\" class=\"block appearance-none w-full rounded-md py-4 px-5 bg-teal-500 text-white cursor-pointer focus:outline-none\" style=\"background-color: #2fc10a\" type=\"submit\" value=\"Create\">\n            <div class=\"divider my-8 flex justify-center\">\n                <div class=\"border-t-2 w-2/3 border-white border-opacity-25\"></div>\n            </div>\n        </form>\n        <form wire:submit.prevent=\"random\" class=\"lg:max-w-72 lg:mx-auto\" method=\"post\">\n            <input id=\"random\" class=\"block appearance-none w-full rounded-md py-4 px-5 bg-yellow-500 text-white cursor-pointer focus:outline-none\" style=\"background-color: #d2ab3e\" type=\"submit\" value=\"Random\">\n        </form>\n                <div class=\"lg:max-w-72 lg:mx-auto\">\n            <button x-on:click=\"in_app = false\" class=\"block appearance-none w-full rounded-md my-5 py-2 px-5 bg-white bg-opacity-10 text-white text-sm cursor-pointer focus:outline-none\">Cancel</button>\n        </div>\n            </div>\n    <div x-show.transition.in=\"!in_app\" class=\"in-app-actions mt-4 px-8\" style=\"display: none;\">\n        <form class=\"lg:max-w-72 lg:mx-auto\" action=\"#\" method=\"post\">\n            <div class=\"relative\">\n                <div class=\"relative\" x-data=\"{ open: false }\" @click.away=\"open = false\" @close.stop=\"open = false\">\n    <div @click=\"open = ! open\">\n        <div class=\"block appearance-none w-full bg-white text-white py-4 px-5 pr-8 bg-opacity-10 rounded-md cursor-pointer focus:outline-none select-none\" id=\"email_id\"><EMAIL></div>\n    </div>\n\n    <div x-show=\"open\"\n            x-transition:enter=\"transition ease-out duration-200\"\n            x-transition:enter-start=\"transform opacity-0 scale-95\"\n            x-transition:enter-end=\"transform opacity-100 scale-100\"\n            x-transition:leave=\"transition ease-in duration-75\"\n            x-transition:leave-start=\"transform opacity-100 scale-100\"\n            x-transition:leave-end=\"transform opacity-0 scale-95\"\n            class=\"absolute z-50 mt-2 w-full rounded-md shadow-lg origin-top\"\n            style=\"display: none;\"\n            @click=\"open = false\">\n        <div class=\"rounded-md shadow-xs max-h-96 overflow-y-auto py-1 bg-white\">\n            <a class=\"block px-4 py-2 text-sm leading-5 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out\" href=\"https://dujaw.com/switch/<EMAIL>\"><EMAIL></a>\n        </div>\n    </div>\n</div>\n                <div class=\"pointer-events-none absolute inset-y-0 right-0 flex items-center px-3 text-white\">\n                    <svg class=\"fill-current h-4 w-4\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\"><path d=\"M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z\"/></svg>\n                </div>\n            </div>\n        </form>\n        <div class=\"divider mt-5\"></div>\n        <div class=\"grid grid-cols-4 lg:grid-cols-2 gap-2 lg:gap-6 lg:max-w-72 lg:mx-auto\">\n            <div class=\"btn_copy bg-white bg-opacity-10 text-white rounded-md py-5 lg:py-10 text-center hover:bg-opacity-25 cursor-pointer\">\n                <div class=\"text-xl lg:text-3xl mx-auto\">\n                    <i class=\"far fa-copy\"></i>\n                </div>\n                <div class=\"text-xs lg:text-base pt-5\">Copy</div>\n            </div>\n            <div onclick=\"document.getElementById('refresh').classList.remove('pause-spinner')\" wire:click=\"$emit('fetchMessages')\" class=\"bg-white bg-opacity-10 text-white rounded-md py-5 lg:py-10 text-center hover:bg-opacity-25 cursor-pointer\">\n                <div class=\"text-xl lg:text-3xl  mx-auto\">\n                    <i id=\"refresh\" class=\"fas fa-sync-alt fa-spin\"></i>\n                </div>\n                <div class=\"text-xs lg:text-base pt-5\">Refresh</div>\n            </div>\n            <div x-on:click=\"in_app = true\" class=\"bg-white bg-opacity-10 text-white rounded-md py-5 lg:py-10 text-center hover:bg-opacity-25 cursor-pointer\">\n                <div class=\"text-xl lg:text-3xl  mx-auto\">\n                    <i class=\"far fa-plus-square\"></i>\n                </div>\n                <div class=\"text-xs lg:text-base pt-5\">New</div>\n            </div>\n            <div wire:click=\"deleteEmail\" class=\"bg-white bg-opacity-10 text-white rounded-md py-5 lg:py-10 text-center hover:bg-opacity-25 cursor-pointer\">\n                <div class=\"text-xl lg:text-3xl  mx-auto\">\n                    <i class=\"far fa-trash-alt\"></i>\n                </div>\n                <div class=\"text-xs lg:text-base pt-5\">Delete</div>\n            </div>\n        </div>\n    </div>\n    </div>\n<!-- Livewire Component wire-end:8hgMapOIueF0GsRQfDhh -->                            </div>\n            <div class=\"w-full lg:w-3/4\">\n                <nav wire:id=\"MzuZI6wMSrhe3a50KD3e\" wire:initial-data=\"{&quot;fingerprint&quot;:{&quot;id&quot;:&quot;MzuZI6wMSrhe3a50KD3e&quot;,&quot;name&quot;:&quot;frontend.nav&quot;,&quot;locale&quot;:&quot;en&quot;,&quot;path&quot;:&quot;mailbox&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;v&quot;:&quot;acj&quot;},&quot;effects&quot;:{&quot;listeners&quot;:[]},&quot;serverMemo&quot;:{&quot;children&quot;:[],&quot;errors&quot;:[],&quot;htmlHash&quot;:&quot;d2c7a7bd&quot;,&quot;data&quot;:{&quot;menus&quot;:[],&quot;current_route&quot;:null},&quot;dataMeta&quot;:{&quot;modelCollections&quot;:{&quot;menus&quot;:{&quot;class&quot;:null,&quot;id&quot;:[],&quot;relations&quot;:[],&quot;connection&quot;:null}}},&quot;checksum&quot;:&quot;1ec1001271846014ee56e69b6654adfdd484ff3f5c4d147ee02fbd9dd1bcb3c6&quot;}}\">\n    <div class=\"bg-gray-100 px-5 hidden lg:flex sticky top-0 z-40 h-24\">\n        <div class=\"w-full my-auto\">\n            <div class=\"flex items-center justify-between h-16\">\n                <div class=\"flex items-center\">\n                    <div class=\"flex items-baseline space-x-4\">\n                                                                    </div>\n                </div>\n                <div class=\"flex items-center\">\n                    <div>\n                                            </div>\n                    <div class=\"ml-4 flex items-center md:ml-6\">\n                        <div class=\"relative\">\n                            <form action=\"https://dujaw.com/locale\" id=\"locale-form\" method=\"post\">\n                                <input type=\"hidden\" name=\"_token\" value=\"6qAYEvONeT48h15gbYEUnphrGGhVo0VHqUqXluHD\">                                <select class=\"block appearance-none bg-gray-200 cursor-pointer text-gray-800 py-1 rounded-md focus:outline-none\" name=\"locale\" id=\"locale\">\n                                                                        <option >ar</option>\n                                                                        <option >de</option>\n                                                                        <option selected>en</option>\n                                                                        <option >fr</option>\n                                                                        <option >hi</option>\n                                                                        <option >pl</option>\n                                                                        <option >ru</option>\n                                                                        <option >es</option>\n                                                                        <option >vi</option>\n                                                                        <option >tr</option>\n                                                                        <option >no</option>\n                                                                        <option >id</option>\n                                                                        <option >it</option>\n                                                                    </select>\n                            </form>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n    <div x-data=\"{ open: false }\">\n        <div @click=\"open = true\" class=\"absolute top-12 right-6 w-8 text-white\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 6h16M4 12h16m-7 6h7\" />\n            </svg>\n        </div>\n        <div x-show=\"open\" x-transition:enter=\"transition ease-out duration-200\" x-transition:enter-start=\"transform opacity-0 scale-95\" x-transition:enter-end=\"transform opacity-100 scale-100\" x-transition:leave=\"transition ease-in duration-75\" x-transition:leave-start=\"transform opacity-100 scale-100\" x-transition:leave-end=\"transform opacity-0 scale-95\" @click.away=\"open = false\" class=\"flex-col lg:hidden fixed top-0 left-0 min-h-screen w-full bg-black bg-opacity-75\">\n            <div @click=\"open = false\" class=\"absolute top-6 right-6 w-8 text-white\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n                </svg>\n            </div>\n            <div class=\"w-full mx-auto mt-20\">\n                <div class=\"flex flex-col items-center justify-between\">\n                    <div class=\"flex flex-col items-center space-y-2\">\n                                                                    </div>\n                    <div class=\"flex flex-col items-center space-y-2 mt-10\">\n                        <div class=\"text-white space-x-2\">\n                                                    </div>\n                        <div class=\"flex items-center mt-4\">\n                            <div class=\"relative\">\n                                <form action=\"https://dujaw.com/locale\" id=\"locale-form-mobile\" method=\"post\">\n                                    <input type=\"hidden\" name=\"_token\" value=\"6qAYEvONeT48h15gbYEUnphrGGhVo0VHqUqXluHD\">                                    <select class=\"block appearance-none bg-gray-200 cursor-pointer text-gray-800 py-1 rounded-md focus:outline-none\" name=\"locale\" id=\"locale-mobile\">\n                                                                                <option >ar</option>\n                                                                                <option >de</option>\n                                                                                <option selected>en</option>\n                                                                                <option >fr</option>\n                                                                                <option >hi</option>\n                                                                                <option >pl</option>\n                                                                                <option >ru</option>\n                                                                                <option >es</option>\n                                                                                <option >vi</option>\n                                                                                <option >tr</option>\n                                                                                <option >no</option>\n                                                                                <option >id</option>\n                                                                                <option >it</option>\n                                                                            </select>\n                                </form>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n</nav>\n<!-- Livewire Component wire-end:MzuZI6wMSrhe3a50KD3e -->                <div class=\"flex flex-col lg:min-h-tm-default\">\n                                         \n                        <main wire:id=\"Ig2ezH90MlLW5AffZCEt\" wire:initial-data=\"{&quot;fingerprint&quot;:{&quot;id&quot;:&quot;Ig2ezH90MlLW5AffZCEt&quot;,&quot;name&quot;:&quot;frontend.app&quot;,&quot;locale&quot;:&quot;en&quot;,&quot;path&quot;:&quot;mailbox&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;v&quot;:&quot;acj&quot;},&quot;effects&quot;:{&quot;listeners&quot;:[&quot;fetchMessages&quot;,&quot;syncEmail&quot;]},&quot;serverMemo&quot;:{&quot;children&quot;:[],&quot;errors&quot;:[],&quot;htmlHash&quot;:&quot;36d69e5b&quot;,&quot;data&quot;:{&quot;messages&quot;:[],&quot;deleted&quot;:[],&quot;error&quot;:&quot;&quot;,&quot;email&quot;:&quot;<EMAIL>&quot;,&quot;initial&quot;:false,&quot;overflow&quot;:false},&quot;dataMeta&quot;:[],&quot;checksum&quot;:&quot;3f395a4eeea3a8cebcebe7255f29010f27c58c7c3c954b48a51ccc6ddaee4aac&quot;}}\" x-data=\"{ id: 0 }\" class=\"flex-1 lg:flex\">\n            <div class=\"w-full lg:w-1/3 bg-white flex flex-col min-h-tm-mobile\">\n                <div class=\"flex-1 flex justify-center items-center h-40 text-gray-400 text-2xl\">\n            Fetching...\n        </div>\n            </div>\n    <div class=\"message-content w-full lg:w-2/3 bg-white border-1 border-l border-gray-200 flex flex-col\">\n        <div x-show=\"id === 0\" class=\"flex-1 hidden lg:flex\">\n            <div class=\"w-2/3 m-auto\">\n                <img class=\"m-auto max-w-full\" src=\"https://dujaw.com/images/sample.jpg\" alt=\"mails\">\n            </div>\n        </div>\n            </div>\n</main>\n<!-- Livewire Component wire-end:Ig2ezH90MlLW5AffZCEt -->                                                        </div>\n            </div>\n        </div>\n    </div>\n    \n    <!--- Helper Text for Language Translation -->\n    <div class=\"hidden language-helper\">\n        <div class=\"error\">Error</div>\n        <div class=\"success\">Success</div>\n        <div class=\"copy_text\">Email ID Copied to Clipboard</div>\n    </div>\n\n    <script src=\"/livewire/livewire.js?id=90730a3b0e7144480175\" data-turbo-eval=\"false\" data-turbolinks-eval=\"false\" ></script><script data-turbo-eval=\"false\" data-turbolinks-eval=\"false\" >window.livewire = new Livewire();window.Livewire = window.livewire;window.livewire_app_url = '';window.livewire_token = '6qAYEvONeT48h15gbYEUnphrGGhVo0VHqUqXluHD';window.deferLoadingAlpine = function (callback) {window.addEventListener('livewire:load', function () {callback();});};let started = false;window.addEventListener('alpine:initializing', function () {if (! started) {window.livewire.start();started = true;}});document.addEventListener(\"DOMContentLoaded\", function () {if (! started) {window.livewire.start();started = true;}});</script>\n        <script>\n        document.addEventListener('DOMContentLoaded', () => {\n            const email = '<EMAIL>';\n            const add_mail_in_title = \"yes\"\n            if(add_mail_in_title === 'yes') {\n                document.title += ` - ${email}`;\n            }\n            Livewire.emit('syncEmail', email);\n            Livewire.emit('fetchMessages');\n        });\n    </script>\n        <script>\n        document.addEventListener('stopLoader', () => {\n            document.getElementById('refresh').classList.add('pause-spinner');\n        });\n        let counter = parseInt(20);\n        setInterval(() => {\n            if (counter === 0 && document.getElementById('imap-error') === null && !document.hidden) {\n                document.getElementById('refresh').classList.remove('pause-spinner');\n                Livewire.emit('fetchMessages');\n                counter = parseInt(20);\n            }\n            counter--;\n            if(document.hidden) {\n                counter = 1;\n            }\n        }, 1000);\n    </script>\n    \n    \n        <script src=\"https://dujaw.com/storage/js/mnpw3.js\" defer></script>\n    <script defer>\n    setTimeout(() => {\n        const enable_ad_block_detector = \"0\"\n        if(!document.getElementById('Q8CvrZzY9fphm6gG') && enable_ad_block_detector == \"1\") {\n            document.querySelector('.default-theme').remove()\n            document.querySelector('body > div').insertAdjacentHTML('beforebegin', `\n                <div class=\"fixed w-screen h-screen bg-red-800 flex flex-col justify-center items-center gap-5 z-50 text-white\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-40 w-40\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                        <path fill-rule=\"evenodd\" d=\"M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z\" clip-rule=\"evenodd\" />\n                    </svg>\n                    <h1 class=\"text-4xl font-bold\">Ad Blocker Detected</h1>\n                    <h2>Disable the Ad Blocker to use Dujaw Store</h2>\n                </div>\n            `)\n        }\n    }, 500);\n    </script>\n    </body>\n</html>"}]}