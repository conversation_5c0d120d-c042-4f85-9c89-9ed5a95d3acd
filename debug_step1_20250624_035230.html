<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Dujaw Store</title>
    
        <link rel="icon" href="https://dujaw.com/storage/public/images/joystick.png">
        <link rel="preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css" integrity="sha512-+4zCK9k+qNFUR5X+cKL9EIR+ZOhtIloNl9GIKS57V1MyNsYpYcUrUeQc9vNfzsWfV28IaLL3i96P9sdNyeRssA==" crossorigin="anonymous" onload="this.onload=null;this.rel='stylesheet'" />
    <link rel="preload" as="style" href="https://dujaw.com/css/vendor.css" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="stylesheet" href="https://dujaw.com/css/common.css">
    <script src="https://dujaw.com/js/app.js" defer></script>
    <style >[wire\:loading], [wire\:loading\.delay], [wire\:loading\.inline-block], [wire\:loading\.inline], [wire\:loading\.block], [wire\:loading\.flex], [wire\:loading\.table], [wire\:loading\.grid], [wire\:loading\.inline-flex] {display: none;}[wire\:loading\.delay\.shortest], [wire\:loading\.delay\.shorter], [wire\:loading\.delay\.short], [wire\:loading\.delay\.long], [wire\:loading\.delay\.longer], [wire\:loading\.delay\.longest] {display:none;}[wire\:offline] {display: none;}[wire\:dirty]:not(textarea):not(input):not(select) {display: none;}input:-webkit-autofill, select:-webkit-autofill, textarea:-webkit-autofill {animation-duration: 50000s;animation-name: livewireautofill;}@keyframes livewireautofill { from {} }</style>
    
        
        <meta name="csrf-token" content="lzEWjB0XSRUmpLeWp5jAuKbfr71GQ8UTPO96l2ow">
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Kadwa:wght@400;600;700&display=swap" rel="preload" as="style" onload="this.onload=null;this.rel='stylesheet'">
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="preload" as="style" onload="this.onload=null;this.rel='stylesheet'">
<style>
:root {
  --head-font: "Kadwa";
  --body-font: "Poppins";
  --primary: #0155b5;
  --secondary: #2fc10a;
  --tertiary: #d2ab3e;
}
</style>
<script>
  let captcha_name = "off";
  let site_key = "";
  if(captcha_name && captcha_name !== "off") {
    site_key = "";
  }
  let strings = {"Get back to MailBox":"Get back to MailBox","Enter Username":"Enter Username","Select Domain":"Select Domain","Create":"Create","Random":"Random","Custom":"Custom","Menu":"Menu","Cancel":"Cancel","Copy":"Copy","Refresh":"Refresh","New":"New","Delete":"Delete","Download":"Download","Fetching":"Fetching","Empty Inbox":"Empty Inbox","Error":"Error","Success":"Success","Close":"Close","Email ID Copied to Clipboard":"Email ID Copied to Clipboard","Please enter Username":"Please enter Username","Please Select a Domain":"Please Select a Domain","Username not allowed":"Username not allowed","Your Temporary Email Address":"Your Temporary Email Address","Attachments":"Attachments","Blocked":"Blocked","Emails from":"Emails from","are blocked by Admin":"are blocked by Admin","No Messages":"No Messages","Waiting for Incoming Messages":"Waiting for Incoming Messages","Scan QR Code to access":"Scan QR Code to access","Create your own Temp Mail":"Create your own Temp Mail","Your Temprorary Email":"Your Temprorary Email","Enter a Username and Select the Domain":"Enter a Username and Select the Domain","Username length cannot be less than":"Username length cannot be less than","and greator than":"and greator than","Create a Random Email":"Create a Random Email","Sender":"Sender","Subject":"Subject","Time":"Time","Open":"Open","Go Back to Inbox":"Go Back to Inbox","Date":"Date","Copyright":"Copyright","Ad Blocker Detected":"Ad Blocker Detected","Disable the Ad Blocker to use ":"Disable the Ad Blocker to use ","Your temporary email address is ready":"Your temporary email address is ready","You have reached daily limit of MAX ":"You have reached daily limit of MAX "," temp mail":" temp mail","Sorry! That email is already been used by someone else. Please try a different email address.":"Sorry! That email is already been used by someone else. Please try a different email address.","Invalid Captcha. Please try again":"Invalid Captcha. Please try again","Invalid Password":"Invalid Password","Password":"Password","Unlock":"Unlock","Your Name":"Your Name","Enter your Name":"Enter your Name","Your Email":"Your Email","Enter your Email":"Enter your Email","Message":"Message","Enter your Message":"Enter your Message","Send Message":"Send Message"}
  const __ = (string) => {
    if(strings[string] !== undefined) {
      return strings[string];
    } else {
      return string;
    }
  }
</script>
    <style>
        input:focus {
            box-shadow: none !important;
            border-color: #8c8c8c !important;
        }
    </style>
</head>
<body style="background-color: #0155b5">
    <div class="container mx-auto">
        <div class="flex h-screen">
            <div class="m-auto">
                <div class="flex justify-center my-10">
                                        <img class="w-logo" src="https://dujaw.com/storage/public/images/joystick.png" alt="logo">
                                    </div>
                <div class="bg-white rounded-lg p-10">
                                        
                    <form action="https://dujaw.com/unlock" class="flex justify-center items-center gap-2" method="post">
                        <input type="hidden" name="_token" value="lzEWjB0XSRUmpLeWp5jAuKbfr71GQ8UTPO96l2ow">                        <input type="password" name="password" id="password" class="flex-1 w-full rounded-md px-4 py-2 text-sm outline-none border-1 border-gray-200 focus:border-gray-200 focus:shadow-none" placeholder="Password">
                        <button type="submit" class="rounded-md px-4 py-2 text-sm border-1 text-white" style="border-color: #2fc10a; background-color: #2fc10a">Unlock</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</body>