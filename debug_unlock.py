#!/usr/bin/env python3
"""
Script de debug para el proceso de unlock
Analiza paso a paso qué está pasando con las credenciales
"""

import requests
import json
from datetime import datetime
from bs4 import BeautifulSoup

def debug_unlock_process():
    """Debug completo del proceso de unlock"""
    
    # Credenciales
    EMAIL = "<EMAIL>"
    PASSWORD = "unlockgs2024"  # Password de la aplicación Dujaw
    CONFIRMATION_PASSWORD = "EMVaB#6G3"  # Password de confirmación para links
    
    print("🐛 DEBUG DEL PROCESO DE UNLOCK")
    print("=" * 50)
    print(f"📧 Email: {EMAIL}")
    print(f"🔐 Password Dujaw: {PASSWORD}")
    print(f"🔑 Password Confirmación: {CONFIRMATION_PASSWORD}")
    
    # Crear sesión
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHT<PERSON>, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
    })
    
    base_url = "https://dujaw.com"
    mailbox_url = f"https://dujaw.com/mailbox/{EMAIL}"
    unlock_url = "https://dujaw.com/unlock"
    
    try:
        # Paso 1: Acceder al mailbox inicial
        print(f"\n1️⃣ ACCEDIENDO AL MAILBOX INICIAL")
        print(f"URL: {mailbox_url}")
        
        response1 = session.get(mailbox_url)
        print(f"Status: {response1.status_code}")
        print(f"URL final: {response1.url}")
        print(f"Tamaño respuesta: {len(response1.text)} caracteres")
        
        # Guardar respuesta inicial
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        with open(f"debug_step1_{timestamp}.html", 'w', encoding='utf-8') as f:
            f.write(response1.text)
        
        # Analizar si hay formulario de unlock
        soup1 = BeautifulSoup(response1.text, 'html.parser')
        unlock_form = soup1.find('form', action=lambda x: x and 'unlock' in x)
        password_input = soup1.find('input', {'name': 'password'})
        csrf_input = soup1.find('input', {'name': '_token'})
        
        print(f"Formulario unlock encontrado: {'✅' if unlock_form else '❌'}")
        print(f"Input password encontrado: {'✅' if password_input else '❌'}")
        print(f"Token CSRF encontrado: {'✅' if csrf_input else '❌'}")
        
        if not unlock_form or not password_input or not csrf_input:
            print("❌ No se encontró el formulario de unlock completo")
            return
        
        csrf_token = csrf_input.get('value')
        print(f"Token CSRF: {csrf_token[:20]}...")
        
        # Paso 2: Enviar formulario de unlock
        print(f"\n2️⃣ ENVIANDO FORMULARIO DE UNLOCK")
        
        form_data = {
            '_token': csrf_token,
            'password': PASSWORD
        }
        
        print(f"Datos del formulario:")
        print(f"  _token: {csrf_token[:20]}...")
        print(f"  password: {PASSWORD}")
        
        # Enviar POST sin seguir redirects
        response2 = session.post(unlock_url, data=form_data, allow_redirects=False)
        print(f"Status: {response2.status_code}")
        print(f"Headers de respuesta:")
        for header, value in response2.headers.items():
            print(f"  {header}: {value}")
        
        # Guardar respuesta del POST
        with open(f"debug_step2_{timestamp}.html", 'w', encoding='utf-8') as f:
            f.write(response2.text)
        
        # Paso 3: Manejar redirect si existe
        if response2.status_code == 302:
            print(f"\n3️⃣ MANEJANDO REDIRECT")
            redirect_url = response2.headers.get('location', '')
            print(f"Redirect URL: {redirect_url}")
            
            if redirect_url:
                if redirect_url.startswith('/'):
                    redirect_url = base_url + redirect_url
                
                print(f"Siguiendo redirect a: {redirect_url}")
                response3 = session.get(redirect_url)
                print(f"Status: {response3.status_code}")
                print(f"URL final: {response3.url}")
                print(f"Tamaño respuesta: {len(response3.text)} caracteres")
                
                # Guardar respuesta del redirect
                with open(f"debug_step3_{timestamp}.html", 'w', encoding='utf-8') as f:
                    f.write(response3.text)
                
                # Analizar si el unlock fue exitoso
                soup3 = BeautifulSoup(response3.text, 'html.parser')
                unlock_form_after = soup3.find('form', action=lambda x: x and 'unlock' in x)
                password_input_after = soup3.find('input', {'name': 'password'})
                
                print(f"¿Aún hay formulario unlock?: {'❌ SÍ' if unlock_form_after else '✅ NO'}")
                print(f"¿Aún hay input password?: {'❌ SÍ' if password_input_after else '✅ NO'}")
                
                if not unlock_form_after and not password_input_after:
                    print("✅ UNLOCK EXITOSO!")
                    
                    # Buscar contenido del mailbox
                    print(f"\n4️⃣ ANALIZANDO CONTENIDO DEL MAILBOX")
                    
                    # Buscar elementos que indiquen correos
                    email_indicators = soup3.find_all(string=lambda text: text and '@' in str(text))
                    message_elements = soup3.find_all(['div', 'tr', 'li'], class_=lambda x: x and any(
                        keyword in ' '.join(x).lower() for keyword in ['message', 'mail', 'email', 'inbox']
                    ))
                    
                    print(f"Elementos con '@': {len(email_indicators)}")
                    print(f"Elementos de mensaje: {len(message_elements)}")
                    
                    if email_indicators:
                        print("📧 Contenido con '@' encontrado:")
                        for i, indicator in enumerate(email_indicators[:5], 1):
                            print(f"  {i}. {str(indicator).strip()[:80]}...")
                    
                    if message_elements:
                        print("📬 Elementos de mensaje encontrados:")
                        for i, element in enumerate(message_elements[:3], 1):
                            text = element.get_text(strip=True)
                            print(f"  {i}. {text[:80]}...")
                    
                    # Buscar scripts con datos
                    scripts = soup3.find_all('script')
                    relevant_scripts = []
                    for script in scripts:
                        script_content = script.string or ""
                        if any(keyword in script_content.lower() for keyword in ['message', 'email', 'mail', 'data']):
                            relevant_scripts.append(script_content[:200])
                    
                    print(f"Scripts relevantes: {len(relevant_scripts)}")
                    
                    if not email_indicators and not message_elements and not relevant_scripts:
                        print("📭 El mailbox parece estar vacío o los correos se cargan dinámicamente")
                    
                else:
                    print("❌ UNLOCK FALLÓ - Aún se pide password")
                    
                    # Analizar por qué falló
                    print(f"\n🔍 ANÁLISIS DEL FALLO:")
                    
                    # Buscar mensajes de error
                    error_elements = soup3.find_all(['div', 'span', 'p'], class_=lambda x: x and any(
                        keyword in ' '.join(x).lower() for keyword in ['error', 'invalid', 'wrong', 'incorrect']
                    ))
                    
                    if error_elements:
                        print("❌ Mensajes de error encontrados:")
                        for error in error_elements:
                            text = error.get_text(strip=True)
                            if text:
                                print(f"  - {text}")
                    else:
                        print("❓ No se encontraron mensajes de error específicos")
                    
                    # Verificar si el password es correcto
                    print(f"\n🔐 VERIFICACIÓN DE CREDENCIALES:")
                    print(f"Password usado: '{PASSWORD}'")
                    print(f"¿Contiene caracteres especiales?: {'✅' if any(c in PASSWORD for c in '#@$%&*') else '❌'}")
                    print(f"Longitud: {len(PASSWORD)} caracteres")
                    
        elif response2.status_code == 200:
            print(f"\n3️⃣ RESPUESTA DIRECTA (SIN REDIRECT)")
            print("Analizando respuesta directa...")
            
            soup2 = BeautifulSoup(response2.text, 'html.parser')
            unlock_form_after = soup2.find('form', action=lambda x: x and 'unlock' in x)
            
            if not unlock_form_after:
                print("✅ UNLOCK EXITOSO!")
            else:
                print("❌ UNLOCK FALLÓ")
        else:
            print(f"❌ ERROR EN POST: {response2.status_code}")
            print(f"Respuesta: {response2.text[:200]}...")
        
        # Resumen final
        print(f"\n📊 RESUMEN DEL DEBUG:")
        print(f"  📄 Archivos generados:")
        print(f"    - debug_step1_{timestamp}.html (página inicial)")
        print(f"    - debug_step2_{timestamp}.html (respuesta POST)")
        if response2.status_code == 302:
            print(f"    - debug_step3_{timestamp}.html (después del redirect)")
        
    except Exception as e:
        print(f"❌ Error durante debug: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Función principal"""
    debug_unlock_process()

if __name__ == "__main__":
    main()
