version: '3.8'

services:
  # Main Dujaw API Web Interface
  dujaw-web:
    build: .
    container_name: dujaw-web
    ports:
      - "8000:8000"
    volumes:
      - ./logs:/app/logs
      - ./config.json:/app/config.json
    environment:
      - PYTHONPATH=/app
      - LOG_LEVEL=INFO
    restart: unless-stopped
    networks:
      - dujaw-network
    depends_on:
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Webhook Server for testing notifications
  webhook-server:
    build:
      context: .
      dockerfile: Dockerfile.webhook
    container_name: dujaw-webhook
    ports:
      - "5000:5000"
    volumes:
      - ./webhook_logs:/app/logs
    environment:
      - FLASK_ENV=production
      - LOG_LEVEL=INFO
    restart: unless-stopped
    networks:
      - dujaw-network

  # Redis for caching and session storage
  redis:
    image: redis:7-alpine
    container_name: dujaw-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped
    networks:
      - dujaw-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx reverse proxy (optional)
  nginx:
    image: nginx:alpine
    container_name: dujaw-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - dujaw-web
      - webhook-server
    restart: unless-stopped
    networks:
      - dujaw-network

  # Monitoring with Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: dujaw-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - dujaw-network

  # Grafana for visualization (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: dujaw-grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./grafana/datasources:/etc/grafana/provisioning/datasources:ro
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    restart: unless-stopped
    networks:
      - dujaw-network
    depends_on:
      - prometheus

networks:
  dujaw-network:
    driver: bridge

volumes:
  redis_data:
  prometheus_data:
  grafana_data:
