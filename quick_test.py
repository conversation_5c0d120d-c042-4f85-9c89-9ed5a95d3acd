#!/usr/bin/env python3
"""
Script rápido para probar Dujaw con credenciales específicas
Uso: python quick_test.py
"""

import sys
import os

# Agregar el directorio actual al path para importar módulos
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_dujaw_credentials():
    """Prueba rápida con las credenciales proporcionadas"""
    
    # Credenciales proporcionadas
    EMAIL = "<EMAIL>"
    PASSWORD = "EMVaB#6G3"
    
    print("🎯 PRUEBA RÁPIDA DUJAW API")
    print("=" * 40)
    print(f"📧 Email: {EMAIL}")
    print(f"🔐 Password: {PASSWORD}")
    print()
    
    try:
        # Importar la clase parametrizada
        from dujaw_interactive import DujawAPIParametrized
        
        # Crear instancia con credenciales específicas
        print("🔧 Inicializando API...")
        api = DujawAPIParametrized(EMAIL, PASSWORD)
        
        # Probar unlock
        print("\n🔓 Probando unlock...")
        if api.unlock_mailbox():
            print("✅ ¡Unlock exitoso!")
            
            # Probar acceso al mailbox
            print("\n📧 Probando acceso al mailbox...")
            mailbox_info = api.access_mailbox()
            
            if mailbox_info and 'error' not in mailbox_info:
                print("✅ ¡Acceso al mailbox exitoso!")
                
                # Mostrar información básica
                messages = mailbox_info.get('messages', [])
                actions = mailbox_info.get('actions', [])
                
                print(f"\n📊 RESUMEN:")
                print(f"   📧 Email: {mailbox_info.get('email_address', EMAIL)}")
                print(f"   📬 Mensajes: {len(messages)}")
                print(f"   🔧 Acciones: {len(actions)}")
                print(f"   ⏰ Timestamp: {mailbox_info.get('timestamp', 'N/A')}")
                
                # Mostrar algunos mensajes si existen
                if messages:
                    print(f"\n📬 MENSAJES ENCONTRADOS:")
                    for i, msg in enumerate(messages[:3], 1):
                        content = msg.get('content', 'Sin contenido')[:80]
                        print(f"   {i}. {content}...")
                else:
                    print(f"\n📭 No hay mensajes en el mailbox")
                
                # Mostrar algunas acciones si existen
                if actions:
                    print(f"\n🔧 ACCIONES DISPONIBLES:")
                    for i, action in enumerate(actions[:3], 1):
                        text = action.get('text', 'Sin texto')
                        action_type = action.get('type', 'unknown')
                        print(f"   {i}. {text} ({action_type})")
                
                # Obtener estado
                print(f"\n📊 ESTADO DEL SISTEMA:")
                status = api.get_status()
                print(f"   🔐 Autenticado: {status.get('authenticated', False)}")
                print(f"   📊 Estado: {status.get('status', 'unknown')}")
                print(f"   📬 Total mensajes: {status.get('message_count', 0)}")
                print(f"   🔧 Total acciones: {status.get('available_actions', 0)}")
                
                print(f"\n🎉 ¡TODAS LAS PRUEBAS EXITOSAS!")
                print(f"   Las credenciales funcionan correctamente")
                print(f"   El mailbox está accesible")
                print(f"   La API está funcionando")
                
            else:
                print("❌ Error accediendo al mailbox")
                if mailbox_info and 'error' in mailbox_info:
                    print(f"   Error: {mailbox_info['error']}")
                return False
                
        else:
            print("❌ Error en unlock")
            print("   Verifica las credenciales o la conectividad")
            return False
            
        # Cerrar sesión
        print(f"\n🚪 Cerrando sesión...")
        api.logout()
        
        return True
        
    except ImportError as e:
        print(f"❌ Error importando módulos: {e}")
        print("   Asegúrate de que beautifulsoup4 y requests estén instalados:")
        print("   pip install beautifulsoup4 requests")
        return False
        
    except Exception as e:
        print(f"❌ Error inesperado: {e}")
        return False

def main():
    """Función principal"""
    print("🚀 Iniciando prueba rápida...")
    print()
    
    success = test_dujaw_credentials()
    
    print()
    print("=" * 40)
    
    if success:
        print("✅ PRUEBA COMPLETADA EXITOSAMENTE")
        print()
        print("💡 Próximos pasos:")
        print("   1. Usar el script interactivo: python dujaw_interactive.py")
        print("   2. Iniciar la interfaz web: uvicorn app:app --host 0.0.0.0 --port 8000")
        print("   3. Probar funciones avanzadas con dujaw_api_enhanced.py")
    else:
        print("❌ PRUEBA FALLÓ")
        print()
        print("🔧 Soluciones posibles:")
        print("   1. Verificar conectividad a internet")
        print("   2. Confirmar que las credenciales son correctas")
        print("   3. Instalar dependencias: pip install beautifulsoup4 requests")
        print("   4. Revisar si dujaw.com está accesible")
    
    print()

if __name__ == "__main__":
    main()
