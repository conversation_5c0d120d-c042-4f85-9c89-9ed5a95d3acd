#!/usr/bin/env python3
"""
Simple webhook server for testing Dujaw API notifications
Receives and logs webhook notifications from the enhanced API
"""

from flask import Flask, request, jsonify
import json
import logging
from datetime import datetime
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('webhook_server.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Store received webhooks
webhook_history = []

@app.route('/webhook', methods=['POST'])
def receive_webhook():
    """Receive webhook notifications"""
    try:
        # Get the JSON payload
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No JSON data received'}), 400
        
        # Add timestamp and source info
        webhook_data = {
            'received_at': datetime.now().isoformat(),
            'source_ip': request.remote_addr,
            'headers': dict(request.headers),
            'payload': data
        }
        
        # Store in history
        webhook_history.append(webhook_data)
        
        # Keep only last 100 webhooks
        if len(webhook_history) > 100:
            webhook_history.pop(0)
        
        # Log the webhook
        logger.info(f"Webhook received: {data.get('event', 'unknown')} - {data.get('message', {}).get('subject', 'No subject')}")
        
        # Pretty print the webhook data
        print("\n" + "="*50)
        print("🔔 NEW WEBHOOK RECEIVED")
        print("="*50)
        print(f"Event: {data.get('event', 'unknown')}")
        print(f"Timestamp: {data.get('timestamp', 'unknown')}")
        
        if 'message' in data:
            message = data['message']
            print(f"Message ID: {message.get('id', 'unknown')}")
            print(f"From: {message.get('sender', 'unknown')}")
            print(f"Subject: {message.get('subject', 'unknown')}")
            print(f"Priority: {message.get('priority', 'unknown')}")
            print(f"Content: {message.get('content', 'unknown')[:100]}...")
        
        print("="*50)
        
        # Save to file
        save_webhook_to_file(webhook_data)
        
        return jsonify({'status': 'success', 'message': 'Webhook received'}), 200
        
    except Exception as e:
        logger.error(f"Error processing webhook: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/webhooks', methods=['GET'])
def get_webhooks():
    """Get all received webhooks"""
    return jsonify({
        'total': len(webhook_history),
        'webhooks': webhook_history
    })

@app.route('/webhooks/latest', methods=['GET'])
def get_latest_webhook():
    """Get the latest webhook"""
    if webhook_history:
        return jsonify(webhook_history[-1])
    else:
        return jsonify({'message': 'No webhooks received yet'}), 404

@app.route('/webhooks/clear', methods=['POST'])
def clear_webhooks():
    """Clear webhook history"""
    global webhook_history
    count = len(webhook_history)
    webhook_history.clear()
    logger.info(f"Cleared {count} webhooks from history")
    return jsonify({'message': f'Cleared {count} webhooks'})

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'webhooks_received': len(webhook_history)
    })

@app.route('/', methods=['GET'])
def index():
    """Simple index page"""
    return f"""
    <html>
    <head>
        <title>Dujaw Webhook Server</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 40px; }}
            .container {{ max-width: 800px; margin: 0 auto; }}
            .webhook {{ background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }}
            .header {{ background: #007bff; color: white; padding: 20px; border-radius: 5px; }}
            .stats {{ background: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0; }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🔔 Dujaw Webhook Server</h1>
                <p>Receiving and logging webhook notifications from Dujaw API</p>
            </div>
            
            <div class="stats">
                <h3>📊 Statistics</h3>
                <p><strong>Total Webhooks Received:</strong> {len(webhook_history)}</p>
                <p><strong>Server Status:</strong> Running</p>
                <p><strong>Last Updated:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
            
            <h3>🔗 Available Endpoints</h3>
            <ul>
                <li><code>POST /webhook</code> - Receive webhook notifications</li>
                <li><code>GET /webhooks</code> - Get all received webhooks</li>
                <li><code>GET /webhooks/latest</code> - Get latest webhook</li>
                <li><code>POST /webhooks/clear</code> - Clear webhook history</li>
                <li><code>GET /health</code> - Health check</li>
            </ul>
            
            <h3>📝 Recent Webhooks</h3>
            <div id="webhooks">
                {"".join([f'<div class="webhook"><strong>{w["payload"].get("event", "unknown")}</strong> - {w["received_at"]}</div>' for w in webhook_history[-5:]])}
            </div>
            
            <script>
                // Auto-refresh every 10 seconds
                setTimeout(() => location.reload(), 10000);
            </script>
        </div>
    </body>
    </html>
    """

def save_webhook_to_file(webhook_data):
    """Save webhook to file for persistence"""
    try:
        filename = f"webhooks_{datetime.now().strftime('%Y%m%d')}.json"
        
        # Load existing data
        existing_data = []
        if os.path.exists(filename):
            with open(filename, 'r') as f:
                existing_data = json.load(f)
        
        # Add new webhook
        existing_data.append(webhook_data)
        
        # Save back to file
        with open(filename, 'w') as f:
            json.dump(existing_data, f, indent=2, default=str)
            
    except Exception as e:
        logger.error(f"Error saving webhook to file: {e}")

if __name__ == '__main__':
    print("🚀 Starting Dujaw Webhook Server")
    print("📡 Webhook endpoint: http://localhost:5000/webhook")
    print("🌐 Web interface: http://localhost:5000")
    print("📊 Health check: http://localhost:5000/health")
    print("\n⚠️  Press Ctrl+C to stop the server\n")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
