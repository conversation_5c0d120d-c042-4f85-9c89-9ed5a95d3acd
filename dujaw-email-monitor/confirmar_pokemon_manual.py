#!/usr/bin/env python3
"""
Script para confirmar manualmente el cambio de email de Pokémon
Maneja la confirmación paso a paso con validación detallada
"""

import requests
import json
import sys
import os
from bs4 import BeautifulSoup
from datetime import datetime

# Agregar src al path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

import config
from src.utils import setup_logging, save_text
from src.dujaw_client import DujawClient
from src.email_extractor import EmailExtractor


class PokemonEmailConfirmer:
    """Confirmador manual de email de Pokémon"""
    
    def __init__(self):
        """Inicializa el confirmador"""
        self.logger = setup_logging('INFO')
        
        # Configurar sesión HTTP
        self.session = requests.Session()
        self.session.headers.update(config.DEFAULT_HEADERS)
        
        # Credenciales
        self.email = "<EMAIL>"
        self.dujaw_password = "unlockgs2024"
        self.confirmation_password = "EMVaB#6G3"
        
        # Link de confirmación (extraído del correo)
        self.pokemon_link = "https://club.pokemon.com/us/pokemon-trainer-club/email-change-approval/49d9fd87c3028b1267f9825fddf6e825"
        
        self.logger.info("🔐 Confirmador de Email Pokémon inicializado")
        self.logger.info(f"📧 Email: {self.email}")
        self.logger.info(f"🔗 Link: {self.pokemon_link}")
    
    def obtener_link_fresco(self):
        """Obtiene el link más reciente del buzón"""
        self.logger.info("📧 Obteniendo link fresco del buzón...")
        
        try:
            with DujawClient(self.email, self.dujaw_password) as client:
                emails = client.get_emails(force_refresh=True)
                
                if not emails:
                    self.logger.warning("No se encontraron correos")
                    return None
                
                extractor = EmailExtractor()
                all_links = extractor.extract_links_from_emails(emails)
                confirmation_links = extractor.get_best_confirmation_links(all_links)
                
                if confirmation_links:
                    best_link = confirmation_links[0]['link']
                    self.logger.info(f"✅ Link fresco obtenido: {best_link}")
                    return best_link
                else:
                    self.logger.warning("No se encontraron links de confirmación")
                    return None
                    
        except Exception as e:
            self.logger.error(f"Error obteniendo link fresco: {e}")
            return None
    
    def acceder_pagina_confirmacion(self, link=None):
        """Accede a la página de confirmación"""
        if link is None:
            link = self.pokemon_link
        
        self.logger.info(f"🌐 Accediendo a página de confirmación...")
        self.logger.info(f"🔗 Link: {link}")
        
        try:
            response = self.session.get(link, allow_redirects=True)
            
            self.logger.info(f"📥 Status: {response.status_code}")
            self.logger.info(f"🌐 URL final: {response.url}")
            self.logger.info(f"📄 Tamaño: {len(response.text)} caracteres")
            
            if response.status_code != 200:
                self.logger.error(f"Error HTTP: {response.status_code}")
                return None
            
            # Guardar respuesta para análisis
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"pokemon_confirmacion_{timestamp}.html"
            filepath = os.path.join("output", filename)
            
            save_text(response.text, filepath)
            self.logger.info(f"💾 Página guardada: {filename}")
            
            return response
            
        except Exception as e:
            self.logger.error(f"Error accediendo a página: {e}")
            return None
    
    def analizar_pagina_confirmacion(self, response):
        """Analiza la página de confirmación"""
        self.logger.info("🔍 Analizando página de confirmación...")
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Verificar si es página de confirmación
        title = soup.find('title')
        if title:
            self.logger.info(f"📄 Título: {title.get_text(strip=True)}")
        
        # Buscar texto de confirmación
        confirmation_text = soup.find(string=lambda text: text and 'approve your email change' in text.lower())
        if confirmation_text:
            self.logger.info("✅ Página de confirmación detectada")
        else:
            self.logger.warning("⚠️ No se detectó página de confirmación")
        
        # Buscar formularios
        forms = soup.find_all('form')
        self.logger.info(f"📋 Formularios encontrados: {len(forms)}")
        
        confirmation_form = None
        for i, form in enumerate(forms):
            action = form.get('action', '')
            method = form.get('method', 'GET')
            
            self.logger.info(f"   Formulario {i+1}: {method} -> {action}")
            
            # Buscar inputs del formulario
            inputs = form.find_all(['input', 'select', 'textarea'])
            self.logger.info(f"      Inputs: {len(inputs)}")
            
            has_password = False
            has_email = False
            
            for input_elem in inputs:
                input_type = input_elem.get('type', input_elem.name)
                input_name = input_elem.get('name', '')
                input_value = input_elem.get('value', '')
                
                self.logger.info(f"         {input_type}: {input_name} = {input_value[:50]}")
                
                if input_type == 'password':
                    has_password = True
                elif input_type == 'email' or 'email' in input_name.lower():
                    has_email = True
            
            # Identificar formulario de confirmación
            if has_password and (has_email or 'approve' in action.lower()):
                confirmation_form = form
                self.logger.info(f"🎯 Formulario de confirmación identificado: Formulario {i+1}")
        
        return confirmation_form
    
    def completar_confirmacion(self, form, base_url):
        """Completa la confirmación del formulario"""
        self.logger.info("🔐 Completando confirmación...")
        
        if not form:
            self.logger.error("❌ No se proporcionó formulario")
            return False
        
        # Extraer datos del formulario
        form_data = {}
        action = form.get('action', '')
        method = form.get('method', 'POST').upper()
        
        self.logger.info(f"📋 Método: {method}")
        self.logger.info(f"📋 Action: {action}")
        
        # Procesar inputs
        for input_elem in form.find_all(['input', 'select', 'textarea']):
            input_type = input_elem.get('type', input_elem.name)
            input_name = input_elem.get('name', '')
            input_value = input_elem.get('value', '')
            
            if not input_name:
                continue
            
            if input_type == 'password':
                form_data[input_name] = self.confirmation_password
                self.logger.info(f"🔑 Password field: {input_name} = {self.confirmation_password}")
            elif input_type == 'hidden':
                form_data[input_name] = input_value
                self.logger.info(f"🔒 Hidden field: {input_name} = {input_value}")
            elif input_type in ['text', 'email']:
                if 'email' in input_name.lower():
                    # Usar el email actual si está vacío
                    if not input_value:
                        form_data[input_name] = self.email
                        self.logger.info(f"📧 Email field: {input_name} = {self.email}")
                    else:
                        form_data[input_name] = input_value
                        self.logger.info(f"📧 Email field: {input_name} = {input_value}")
                else:
                    form_data[input_name] = input_value
                    self.logger.info(f"📝 Text field: {input_name} = {input_value}")
            elif input_type == 'submit':
                if input_value:
                    form_data[input_name] = input_value
                    self.logger.info(f"🔘 Submit field: {input_name} = {input_value}")
        
        # Determinar URL de envío
        if action.startswith('http'):
            submit_url = action
        elif action.startswith('/'):
            submit_url = f"https://club.pokemon.com{action}"
        else:
            submit_url = f"{base_url.rstrip('/')}/{action}"
        
        self.logger.info(f"📤 URL de envío: {submit_url}")
        self.logger.info(f"📊 Datos del formulario: {len(form_data)} campos")
        
        try:
            # Enviar formulario
            if method == 'POST':
                response = self.session.post(submit_url, data=form_data, allow_redirects=True)
            else:
                response = self.session.get(submit_url, params=form_data, allow_redirects=True)
            
            self.logger.info(f"📥 Respuesta: {response.status_code}")
            self.logger.info(f"🌐 URL final: {response.url}")
            
            # Guardar respuesta
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"pokemon_confirmacion_resultado_{timestamp}.html"
            filepath = os.path.join("output", filename)
            
            save_text(response.text, filepath)
            self.logger.info(f"💾 Resultado guardado: {filename}")
            
            # Analizar resultado
            return self.analizar_resultado_confirmacion(response)
            
        except Exception as e:
            self.logger.error(f"❌ Error enviando confirmación: {e}")
            return False
    
    def analizar_resultado_confirmacion(self, response):
        """Analiza el resultado de la confirmación"""
        self.logger.info("🔍 Analizando resultado de confirmación...")
        
        html_lower = response.text.lower()
        
        # Indicadores de éxito
        success_indicators = [
            'successfully confirmed',
            'email has been verified',
            'confirmation successful',
            'email change approved',
            'change has been approved',
            'email updated',
            'successfully updated'
        ]
        
        # Indicadores de error
        error_indicators = [
            'invalid password',
            'incorrect password',
            'password is incorrect',
            'error occurred',
            'confirmation failed',
            'invalid request',
            'expired',
            'not found'
        ]
        
        # Verificar éxito
        for indicator in success_indicators:
            if indicator in html_lower:
                self.logger.info(f"🎉 ¡CONFIRMACIÓN EXITOSA! Indicador: {indicator}")
                return True
        
        # Verificar errores
        for indicator in error_indicators:
            if indicator in html_lower:
                self.logger.warning(f"❌ Error detectado: {indicator}")
                return False
        
        # Verificar redirección a página de éxito
        if 'pokemon.com' in response.url and response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Buscar mensajes de confirmación en la página
            success_elements = soup.find_all(string=lambda text: text and any(
                word in text.lower() for word in ['success', 'confirmed', 'approved', 'updated']
            ))
            
            if success_elements:
                self.logger.info(f"✅ Elementos de éxito encontrados: {len(success_elements)}")
                for element in success_elements[:3]:  # Mostrar primeros 3
                    self.logger.info(f"   📝 {element.strip()[:100]}")
                return True
        
        self.logger.warning("⚠️ Resultado incierto - revisar archivo guardado")
        return False
    
    def proceso_completo(self):
        """Ejecuta el proceso completo de confirmación"""
        self.logger.info("🚀 INICIANDO PROCESO COMPLETO DE CONFIRMACIÓN")
        self.logger.info("=" * 60)
        
        try:
            # 1. Obtener link fresco del buzón
            self.logger.info("1️⃣ Obteniendo link fresco...")
            fresh_link = self.obtener_link_fresco()
            
            if fresh_link:
                self.pokemon_link = fresh_link
                self.logger.info(f"✅ Usando link fresco: {fresh_link}")
            else:
                self.logger.info(f"⚠️ Usando link original: {self.pokemon_link}")
            
            # 2. Acceder a página de confirmación
            self.logger.info("\n2️⃣ Accediendo a página de confirmación...")
            response = self.acceder_pagina_confirmacion(self.pokemon_link)
            
            if not response:
                self.logger.error("❌ No se pudo acceder a la página")
                return False
            
            # 3. Analizar página
            self.logger.info("\n3️⃣ Analizando página...")
            confirmation_form = self.analizar_pagina_confirmacion(response)
            
            if not confirmation_form:
                self.logger.error("❌ No se encontró formulario de confirmación")
                return False
            
            # 4. Completar confirmación
            self.logger.info("\n4️⃣ Completando confirmación...")
            success = self.completar_confirmacion(confirmation_form, response.url)
            
            if success:
                self.logger.info("\n🎉 ¡CONFIRMACIÓN COMPLETADA EXITOSAMENTE!")
                return True
            else:
                self.logger.error("\n❌ La confirmación no fue exitosa")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Error en proceso completo: {e}")
            return False
        
        finally:
            self.session.close()


def main():
    """Función principal"""
    print("🔐 CONFIRMADOR MANUAL DE EMAIL POKÉMON")
    print("=" * 50)
    
    confirmer = PokemonEmailConfirmer()
    success = confirmer.proceso_completo()
    
    if success:
        print("\n✅ PROCESO COMPLETADO EXITOSAMENTE")
        print("🎉 El cambio de email debería estar confirmado")
    else:
        print("\n❌ PROCESO NO COMPLETADO")
        print("💡 Revisar archivos guardados en output/ para más detalles")
    
    print("\n🏁 Proceso finalizado")


if __name__ == "__main__":
    main()
