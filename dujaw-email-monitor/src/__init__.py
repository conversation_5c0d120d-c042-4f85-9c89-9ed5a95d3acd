"""
Dujaw Email Monitor - Sistema de monitoreo de correos temporales

Este paquete proporciona herramientas para:
- Monitorear buzones temporales en Dujaw.com
- Extraer correos y links de confirmación
- Seguir links automáticamente
- Procesar formularios de verificación

Módulos principales:
- dujaw_client: Cliente para interactuar con la API de Dujaw
- email_extractor: Extractor de correos y links
- link_follower: Seguidor automático de links
- utils: Utilidades comunes

Autor: Dujaw Email Monitor Team
Versión: 1.0.0
"""

__version__ = "1.0.0"
__author__ = "Dujaw Email Monitor Team"
__email__ = "<EMAIL>"

# Importaciones principales para facilitar el uso
from .dujaw_client import DujawClient
from .email_extractor import EmailExtractor
from .link_follower import LinkFollower
from .utils import setup_logging, validate_email, sanitize_filename

__all__ = [
    'DujawClient',
    'EmailExtractor', 
    'LinkFollower',
    'setup_logging',
    'validate_email',
    'sanitize_filename'
]
