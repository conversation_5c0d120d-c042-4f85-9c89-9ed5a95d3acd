#!/usr/bin/env python3
"""
Utilidades comunes para el sistema Dujaw Email Monitor
"""

import os
import re
import json
import logging
import colorlog
from datetime import datetime
from typing import Optional, Dict, Any, List
from email_validator import validate_email as validate_email_lib, EmailNotValidError

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import config


def setup_logging(log_level: str = None, log_file: str = None) -> logging.Logger:
    """
    Configura el sistema de logging con colores y formato personalizado
    
    Args:
        log_level: Nivel de logging (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Archivo de log (opcional)
    
    Returns:
        Logger configurado
    """
    if log_level is None:
        log_level = config.DEFAULT_LOG_LEVEL
    
    # Crear logger principal
    logger = logging.getLogger('dujaw_monitor')
    logger.setLevel(config.LOG_LEVELS.get(log_level.upper(), logging.INFO))
    
    # Limpiar handlers existentes
    logger.handlers.clear()
    
    # Handler para consola con colores
    console_handler = colorlog.StreamHandler()
    console_formatter = colorlog.ColoredFormatter(
        '%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt=config.LOG_DATE_FORMAT,
        log_colors=config.LOG_COLORS
    )
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)
    
    # Handler para archivo si se especifica
    if log_file:
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_formatter = logging.Formatter(
            config.LOG_FORMAT,
            datefmt=config.LOG_DATE_FORMAT
        )
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
    
    return logger


def validate_email(email: str) -> bool:
    """
    Valida si un email tiene formato correcto
    
    Args:
        email: Email a validar
    
    Returns:
        True si el email es válido, False en caso contrario
    """
    try:
        # Validación básica con regex
        if not re.match(config.EMAIL_REGEX, email):
            return False
        
        # Validación avanzada con email-validator
        validate_email_lib(email)
        return True
        
    except (EmailNotValidError, Exception):
        return False


def sanitize_filename(filename: str) -> str:
    """
    Sanitiza un nombre de archivo removiendo caracteres no válidos
    
    Args:
        filename: Nombre de archivo a sanitizar
    
    Returns:
        Nombre de archivo sanitizado
    """
    # Remover caracteres no válidos
    sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # Remover espacios múltiples y reemplazar por guión bajo
    sanitized = re.sub(r'\s+', '_', sanitized)
    
    # Limitar longitud
    if len(sanitized) > 200:
        sanitized = sanitized[:200]
    
    return sanitized


def generate_timestamp() -> str:
    """
    Genera un timestamp en formato YYYYMMDD_HHMMSS
    
    Returns:
        Timestamp como string
    """
    return datetime.now().strftime("%Y%m%d_%H%M%S")


def save_json(data: Dict[Any, Any], filepath: str, indent: int = 2) -> bool:
    """
    Guarda datos en formato JSON
    
    Args:
        data: Datos a guardar
        filepath: Ruta del archivo
        indent: Indentación del JSON
    
    Returns:
        True si se guardó exitosamente, False en caso contrario
    """
    try:
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=indent, ensure_ascii=False)
        
        return True
        
    except Exception as e:
        logging.getLogger('dujaw_monitor').error(f"Error guardando JSON: {e}")
        return False


def load_json(filepath: str) -> Optional[Dict[Any, Any]]:
    """
    Carga datos desde un archivo JSON
    
    Args:
        filepath: Ruta del archivo
    
    Returns:
        Datos cargados o None si hay error
    """
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
            
    except Exception as e:
        logging.getLogger('dujaw_monitor').error(f"Error cargando JSON: {e}")
        return None


def save_text(text: str, filepath: str) -> bool:
    """
    Guarda texto en un archivo
    
    Args:
        text: Texto a guardar
        filepath: Ruta del archivo
    
    Returns:
        True si se guardó exitosamente, False en caso contrario
    """
    try:
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(text)
        
        return True
        
    except Exception as e:
        logging.getLogger('dujaw_monitor').error(f"Error guardando texto: {e}")
        return False


def mask_sensitive_data(data: str, sensitive_fields: List[str] = None) -> str:
    """
    Enmascara datos sensibles en strings para logging seguro
    
    Args:
        data: String que puede contener datos sensibles
        sensitive_fields: Lista de campos sensibles a enmascarar
    
    Returns:
        String con datos sensibles enmascarados
    """
    if sensitive_fields is None:
        sensitive_fields = config.SENSITIVE_FIELDS
    
    masked_data = data
    
    for field in sensitive_fields:
        # Patrón para encontrar field="value" o field: "value"
        patterns = [
            rf'({field}["\']?\s*[:=]\s*["\']?)([^"\'&\s]+)(["\']?)',
            rf'({field}["\']?\s*[:=]\s*)([^&\s]+)',
        ]
        
        for pattern in patterns:
            masked_data = re.sub(
                pattern, 
                rf'\1{config.SENSITIVE_MASK}\3', 
                masked_data, 
                flags=re.IGNORECASE
            )
    
    return masked_data


def validate_url(url: str) -> bool:
    """
    Valida si una URL tiene formato correcto
    
    Args:
        url: URL a validar
    
    Returns:
        True si la URL es válida, False en caso contrario
    """
    return bool(re.match(config.URL_REGEX, url))


def validate_hex_token(token: str, min_length: int = None) -> bool:
    """
    Valida si un token es hexadecimal válido
    
    Args:
        token: Token a validar
        min_length: Longitud mínima del token
    
    Returns:
        True si el token es válido, False en caso contrario
    """
    if min_length is None:
        min_length = config.HEX_TOKEN_MIN_LENGTH
    
    if len(token) < min_length:
        return False
    
    return bool(re.match(config.HEX_TOKEN_REGEX, token))


def extract_domain(url: str) -> Optional[str]:
    """
    Extrae el dominio de una URL
    
    Args:
        url: URL de la cual extraer el dominio
    
    Returns:
        Dominio extraído o None si hay error
    """
    try:
        # Patrón para extraer dominio
        pattern = r'https?://([^/]+)'
        match = re.search(pattern, url)
        
        if match:
            return match.group(1).lower()
        
        return None
        
    except Exception:
        return None


def is_domain_of_interest(url: str) -> bool:
    """
    Verifica si una URL pertenece a un dominio de interés
    
    Args:
        url: URL a verificar
    
    Returns:
        True si es de interés, False en caso contrario
    """
    domain = extract_domain(url)
    
    if not domain:
        return False
    
    for interest_domain in config.DOMAINS_OF_INTEREST:
        if interest_domain.lower() in domain:
            return True
    
    return False


def contains_keywords(text: str, keywords: List[str] = None) -> bool:
    """
    Verifica si un texto contiene palabras clave de interés
    
    Args:
        text: Texto a verificar
        keywords: Lista de palabras clave (opcional)
    
    Returns:
        True si contiene palabras clave, False en caso contrario
    """
    if keywords is None:
        keywords = config.EMAIL_KEYWORDS
    
    text_lower = text.lower()
    
    return any(keyword.lower() in text_lower for keyword in keywords)


def create_output_filename(prefix: str, extension: str, timestamp: str = None) -> str:
    """
    Crea un nombre de archivo de salida con timestamp
    
    Args:
        prefix: Prefijo del archivo
        extension: Extensión del archivo
        timestamp: Timestamp (opcional, se genera automáticamente)
    
    Returns:
        Nombre de archivo completo
    """
    if timestamp is None:
        timestamp = generate_timestamp()
    
    return f"{prefix}{timestamp}.{extension}"


def ensure_directory_exists(directory: str) -> bool:
    """
    Asegura que un directorio existe, creándolo si es necesario
    
    Args:
        directory: Ruta del directorio
    
    Returns:
        True si el directorio existe o se creó exitosamente
    """
    try:
        os.makedirs(directory, exist_ok=True)
        return True
        
    except Exception as e:
        logging.getLogger('dujaw_monitor').error(f"Error creando directorio {directory}: {e}")
        return False
