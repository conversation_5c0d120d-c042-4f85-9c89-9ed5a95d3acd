#!/usr/bin/env python3
"""
Cliente para interactuar con la API de Dujaw.com
Maneja autenticación, sesiones y comunicación con Livewire
"""

import requests
import json
import re
import time
import logging
from typing import Optional, Dict, Any, List
from bs4 import BeautifulSoup

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import config
from .utils import mask_sensitive_data, validate_email


class DujawClient:
    """Cliente para interactuar con Dujaw.com"""
    
    def __init__(self, email: str, password: str = None):
        """
        Inicializa el cliente Dujaw
        
        Args:
            email: Email del buzón temporal
            password: Password del buzón (opcional)
        """
        self.logger = logging.getLogger('dujaw_monitor.client')
        
        # Validar email
        if not validate_email(email):
            raise ValueError(f"Email inválido: {email}")
        
        self.email = email
        self.password = password or config.DEFAULT_PASSWORD
        self.base_url = config.DUJAW_BASE_URL
        self.mailbox_url = config.DUJAW_MAILBOX_URL_TEMPLATE.format(email=email)
        self.unlock_url = config.DUJAW_UNLOCK_URL
        
        # Configurar sesión HTTP
        self.session = requests.Session()
        self.session.headers.update(config.DEFAULT_HEADERS)
        
        # Estado interno
        self.csrf_token = None
        self.is_unlocked = False
        self.livewire_components = []
        
        self.logger.info(f"Cliente Dujaw inicializado para: {email}")
    
    def unlock_mailbox(self) -> bool:
        """
        Desbloquea el buzón temporal
        
        Returns:
            True si se desbloqueó exitosamente, False en caso contrario
        """
        try:
            self.logger.info("Iniciando unlock del mailbox...")
            
            # Acceder al mailbox para obtener el formulario
            response = self.session.get(self.mailbox_url, timeout=config.HTTP_TIMEOUT)
            
            if response.status_code != 200:
                self.logger.error(f"Error accediendo al mailbox: {response.status_code}")
                return False
            
            # Verificar si ya está desbloqueado
            soup = BeautifulSoup(response.text, 'html.parser')
            password_input = soup.find('input', {'type': 'password'})
            
            if not password_input:
                self.logger.info("Mailbox ya está desbloqueado")
                self.is_unlocked = True
                self._extract_livewire_components(response.text)
                return True
            
            # Obtener token CSRF
            csrf_input = soup.find('input', {'name': '_token'})
            if not csrf_input:
                self.logger.error("No se encontró token CSRF")
                return False
            
            self.csrf_token = csrf_input.get('value')
            self.logger.debug(f"Token CSRF obtenido: {self.csrf_token[:20]}...")
            
            # Hacer unlock
            form_data = {
                '_token': self.csrf_token,
                'password': self.password
            }
            
            self.logger.debug("Enviando formulario de unlock...")
            response = self.session.post(
                self.unlock_url, 
                data=form_data, 
                allow_redirects=True,
                timeout=config.HTTP_TIMEOUT
            )
            
            if response.status_code == 200:
                self.logger.info("Unlock exitoso")
                self.is_unlocked = True
                self._extract_livewire_components(response.text)
                return True
            else:
                self.logger.error(f"Error en unlock: {response.status_code}")
                return False
                
        except Exception as e:
            self.logger.error(f"Excepción durante unlock: {e}")
            return False
    
    def _extract_livewire_components(self, html_content: str) -> None:
        """
        Extrae componentes Livewire del HTML
        
        Args:
            html_content: Contenido HTML de la página
        """
        try:
            pattern = r'wire:id="([^"]+)"\s+wire:initial-data="([^"]+)"'
            matches = re.findall(pattern, html_content)
            
            self.livewire_components = []
            
            for wire_id, wire_initial_data_encoded in matches:
                try:
                    import html
                    wire_initial_data = html.unescape(wire_initial_data_encoded)
                    initial_data = json.loads(wire_initial_data)
                    
                    component_name = initial_data.get('fingerprint', {}).get('name', 'unknown')
                    
                    self.livewire_components.append({
                        'wire_id': wire_id,
                        'component_name': component_name,
                        'initial_data': initial_data
                    })
                    
                    self.logger.debug(f"Componente Livewire encontrado: {component_name}")
                    
                except Exception as e:
                    self.logger.warning(f"Error procesando componente {wire_id}: {e}")
                    continue
            
            self.logger.info(f"Extraídos {len(self.livewire_components)} componentes Livewire")
            
        except Exception as e:
            self.logger.error(f"Error extrayendo componentes Livewire: {e}")
    
    def get_emails(self, force_refresh: bool = False) -> List[Dict[str, Any]]:
        """
        Obtiene correos del buzón
        
        Args:
            force_refresh: Forzar refresh con Livewire si no hay mensajes
        
        Returns:
            Lista de correos encontrados
        """
        if not self.is_unlocked:
            self.logger.warning("Mailbox no está desbloqueado")
            if not self.unlock_mailbox():
                return []
        
        try:
            self.logger.info("Obteniendo correos del buzón...")
            
            # Acceder al mailbox
            response = self.session.get(self.mailbox_url, timeout=config.HTTP_TIMEOUT)
            
            if response.status_code != 200:
                self.logger.error(f"Error accediendo mailbox: {response.status_code}")
                return []
            
            # Extraer componentes Livewire actualizados
            self._extract_livewire_components(response.text)
            
            # Buscar correos en componentes
            emails = []
            
            for component in self.livewire_components:
                if component['component_name'] == 'frontend.app':
                    initial_data = component['initial_data']
                    
                    if 'serverMemo' in initial_data and 'data' in initial_data['serverMemo']:
                        server_data = initial_data['serverMemo']['data']
                        
                        if 'messages' in server_data:
                            messages = server_data['messages']
                            self.logger.info(f"Mensajes iniciales encontrados: {len(messages)}")
                            emails.extend(messages)
            
            # Si no hay correos y se solicita refresh, intentar con Livewire
            if not emails and force_refresh:
                self.logger.info("No hay correos iniciales, intentando refresh...")
                refresh_emails = self._refresh_emails_livewire()
                emails.extend(refresh_emails)
            
            self.logger.info(f"Total de correos obtenidos: {len(emails)}")
            return emails
            
        except Exception as e:
            self.logger.error(f"Error obteniendo correos: {e}")
            return []
    
    def _refresh_emails_livewire(self) -> List[Dict[str, Any]]:
        """
        Hace refresh de correos usando Livewire
        
        Returns:
            Lista de correos obtenidos del refresh
        """
        emails = []
        
        try:
            for component in self.livewire_components:
                if component['component_name'] == 'frontend.app':
                    self.logger.debug(f"Haciendo refresh en {component['component_name']}")
                    
                    initial_data = component['initial_data']
                    fingerprint = initial_data.get('fingerprint', {})
                    server_memo = initial_data.get('serverMemo', {})
                    
                    # Headers para Livewire
                    headers = config.LIVEWIRE_HEADERS.copy()
                    headers['X-CSRF-TOKEN'] = self.csrf_token
                    headers['Referer'] = self.mailbox_url
                    
                    # Request Livewire
                    livewire_request = {
                        'fingerprint': fingerprint,
                        'serverMemo': server_memo,
                        'updates': [
                            {
                                'type': 'fireEvent',
                                'payload': {
                                    'id': component['wire_id'],
                                    'event': 'fetchMessages',
                                    'params': []
                                }
                            }
                        ]
                    }
                    
                    # Hacer llamada
                    livewire_url = f"{self.base_url}/livewire/message/{component['component_name']}"
                    
                    response = self.session.post(
                        livewire_url, 
                        json=livewire_request, 
                        headers=headers,
                        timeout=config.HTTP_TIMEOUT
                    )
                    
                    self.logger.debug(f"Respuesta Livewire: {response.status_code}")
                    
                    if response.status_code == 200:
                        data = response.json()
                        
                        # Buscar mensajes en serverMemo actualizado
                        if 'serverMemo' in data and 'data' in data['serverMemo']:
                            server_data = data['serverMemo']['data']
                            if 'messages' in server_data:
                                messages = server_data['messages']
                                self.logger.info(f"Mensajes después de refresh: {len(messages)}")
                                emails.extend(messages)
                        
                        # También buscar en effects/html
                        if 'effects' in data and 'html' in data['effects']:
                            html_content = data['effects']['html']
                            emails.append({
                                'source': 'livewire_refresh',
                                'content': html_content
                            })
                            self.logger.debug("HTML actualizado obtenido de Livewire")
                    
        except Exception as e:
            self.logger.error(f"Error en refresh Livewire: {e}")
        
        return emails
    
    def close(self) -> None:
        """Cierra la sesión HTTP"""
        try:
            self.session.close()
            self.logger.info("Sesión HTTP cerrada")
        except Exception as e:
            self.logger.warning(f"Error cerrando sesión: {e}")
    
    def __enter__(self):
        """Context manager entry"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.close()
