#!/usr/bin/env python3
"""
Extractor de correos y links de confirmación
Procesa contenido HTML y extrae información relevante
"""

import re
import logging
from typing import List, Dict, Any, Optional, Tuple
from bs4 import BeautifulSoup

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import config
from .utils import validate_url, validate_hex_token, extract_domain, is_domain_of_interest, contains_keywords


class EmailExtractor:
    """Extractor de correos y links de confirmación"""
    
    def __init__(self):
        """Inicializa el extractor"""
        self.logger = logging.getLogger('dujaw_monitor.extractor')
        self.logger.info("Email Extractor inicializado")
    
    def extract_links_from_emails(self, emails: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Extrae links de una lista de correos
        
        Args:
            emails: Lista de correos a procesar
        
        Returns:
            Lista de links encontrados con metadatos
        """
        all_links = []
        
        self.logger.info(f"Extrayendo links de {len(emails)} correos...")
        
        for i, email in enumerate(emails):
            self.logger.debug(f"Procesando correo {i+1}/{len(emails)}")
            
            # Obtener contenido HTML del correo
            html_content = self._get_email_content(email)
            
            if not html_content:
                self.logger.debug(f"Correo {i+1} sin contenido HTML")
                continue
            
            # Extraer metadatos del correo
            email_metadata = self._extract_email_metadata(email)
            
            # Buscar links en el contenido
            links = self._extract_links_from_html(html_content)
            
            # Agregar metadatos a cada link
            for link in links:
                link_data = {
                    'link': link,
                    'email_index': i,
                    'email_metadata': email_metadata,
                    'validation': self._validate_link(link),
                    'classification': self._classify_link(link)
                }
                all_links.append(link_data)
        
        self.logger.info(f"Total de links extraídos: {len(all_links)}")
        return all_links
    
    def _get_email_content(self, email: Dict[str, Any]) -> Optional[str]:
        """
        Obtiene el contenido HTML de un correo
        
        Args:
            email: Datos del correo
        
        Returns:
            Contenido HTML o None si no se encuentra
        """
        # Buscar en diferentes campos posibles
        content_fields = ['content', 'html', 'body', 'message']
        
        for field in content_fields:
            if field in email and email[field]:
                return str(email[field])
        
        # Si es un correo de Livewire refresh
        if email.get('source') == 'livewire_refresh':
            return email.get('content', '')
        
        return None
    
    def _extract_email_metadata(self, email: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extrae metadatos de un correo
        
        Args:
            email: Datos del correo
        
        Returns:
            Diccionario con metadatos
        """
        metadata = {
            'sender_name': email.get('sender_name', 'N/A'),
            'sender_email': email.get('sender_email', 'N/A'),
            'subject': email.get('subject', 'N/A'),
            'date': email.get('date', 'N/A'),
            'message_id': email.get('id', 'N/A'),
            'source': email.get('source', 'mailbox')
        }
        
        return metadata
    
    def _extract_links_from_html(self, html_content: str) -> List[str]:
        """
        Extrae todos los links de contenido HTML
        
        Args:
            html_content: Contenido HTML
        
        Returns:
            Lista de links únicos encontrados
        """
        links = []
        
        try:
            # Método 1: BeautifulSoup para links en tags <a>
            soup = BeautifulSoup(html_content, 'html.parser')
            
            for a_tag in soup.find_all('a', href=True):
                href = a_tag['href']
                if href.startswith('http'):
                    links.append(href)
            
            # Método 2: Regex para URLs en texto
            url_patterns = [
                r'https?://[^\s<>"\']+(?:[^\s<>"\'.,;!?])',  # URLs generales
                r'href="(https://[^"]+)"',  # URLs en atributos href
                r'href=\'(https://[^\']+)\'',  # URLs en atributos href con comillas simples
            ]
            
            for pattern in url_patterns:
                matches = re.findall(pattern, html_content)
                for match in matches:
                    # Si el match es una tupla (de grupos de captura), tomar el primer elemento
                    if isinstance(match, tuple):
                        match = match[0]
                    
                    if match.startswith('http') and match not in links:
                        links.append(match)
            
            # Método 3: Patrones específicos de config
            for pattern_name, pattern in config.LINK_PATTERNS.items():
                matches = re.findall(pattern, html_content)
                for match in matches:
                    if match not in links:
                        links.append(match)
            
            # Limpiar links (remover caracteres HTML extra)
            cleaned_links = []
            for link in links:
                cleaned_link = self._clean_link(link)
                if cleaned_link and cleaned_link not in cleaned_links:
                    cleaned_links.append(cleaned_link)
            
            return cleaned_links
            
        except Exception as e:
            self.logger.error(f"Error extrayendo links: {e}")
            return []
    
    def _clean_link(self, link: str) -> Optional[str]:
        """
        Limpia un link removiendo caracteres HTML extra
        
        Args:
            link: Link a limpiar
        
        Returns:
            Link limpio o None si es inválido
        """
        try:
            # Remover caracteres HTML comunes al final
            cleaned = re.sub(r'[&"\'<>]+$', '', link)
            
            # Remover fragmentos y parámetros de tracking comunes
            cleaned = re.sub(r'[?&]utm_[^&]*', '', cleaned)
            cleaned = re.sub(r'[?&]ref=[^&]*', '', cleaned)
            
            # Validar que sigue siendo una URL válida
            if validate_url(cleaned):
                return cleaned
            
            return None
            
        except Exception:
            return None
    
    def _validate_link(self, link: str) -> Dict[str, Any]:
        """
        Valida un link y extrae información de validación
        
        Args:
            link: Link a validar
        
        Returns:
            Diccionario con información de validación
        """
        validation = {
            'is_valid_url': validate_url(link),
            'domain': extract_domain(link),
            'is_https': link.startswith('https://'),
            'has_token': False,
            'token_valid': False,
            'token_length': 0
        }
        
        # Verificar si tiene token (para links de confirmación - múltiples idiomas)
        confirmation_keywords = [
            'email-change-approval',  # Inglés
            'aprovacao-de-alteracao-de-endereco-de-email',  # Portugués brasileño
            'aprobacion-de-cambio-de-email',  # Español
            'approbation-changement-email',  # Francés
            'email-aenderung-genehmigung',  # Alemán
            'approvazione-cambio-email',  # Italiano
            'confirm', 'verify', 'activate'
        ]

        if any(keyword in link.lower() for keyword in confirmation_keywords):
            # Extraer posible token del final de la URL
            parts = link.split('/')
            if parts:
                potential_token = parts[-1]
                
                # Remover parámetros de query si los hay
                potential_token = potential_token.split('?')[0]
                
                if potential_token:
                    validation['has_token'] = True
                    validation['token_length'] = len(potential_token)
                    validation['token_valid'] = validate_hex_token(potential_token)
        
        return validation
    
    def _classify_link(self, link: str) -> Dict[str, Any]:
        """
        Clasifica un link según su tipo e importancia
        
        Args:
            link: Link a clasificar
        
        Returns:
            Diccionario con clasificación del link
        """
        classification = {
            'type': 'unknown',
            'priority': 'low',
            'is_confirmation': False,
            'is_domain_of_interest': is_domain_of_interest(link),
            'keywords_found': []
        }
        
        link_lower = link.lower()
        
        # Clasificar por tipo (múltiples idiomas)
        email_change_keywords = [
            'email-change-approval',  # Inglés
            'aprovacao-de-alteracao-de-endereco-de-email',  # Portugués brasileño
            'aprobacion-de-cambio-de-email',  # Español
            'approbation-changement-email',  # Francés
            'email-aenderung-genehmigung',  # Alemán
            'approvazione-cambio-email'  # Italiano
        ]

        if any(keyword in link_lower for keyword in email_change_keywords):
            classification['type'] = 'email_change_confirmation'
            classification['priority'] = 'high'
            classification['is_confirmation'] = True
        elif any(keyword in link_lower for keyword in ['confirm', 'verify', 'activate']):
            classification['type'] = 'confirmation'
            classification['priority'] = 'high'
            classification['is_confirmation'] = True
        elif any(keyword in link_lower for keyword in ['unsubscribe', 'opt-out']):
            classification['type'] = 'unsubscribe'
            classification['priority'] = 'low'
        elif any(keyword in link_lower for keyword in ['login', 'signin', 'auth']):
            classification['type'] = 'authentication'
            classification['priority'] = 'medium'
        elif any(keyword in link_lower for keyword in ['support', 'help', 'contact']):
            classification['type'] = 'support'
            classification['priority'] = 'low'
        
        # Buscar palabras clave
        for keyword in config.EMAIL_KEYWORDS:
            if keyword.lower() in link_lower:
                classification['keywords_found'].append(keyword)
        
        # Ajustar prioridad basada en dominio de interés
        if classification['is_domain_of_interest']:
            if classification['priority'] == 'low':
                classification['priority'] = 'medium'
            elif classification['priority'] == 'medium':
                classification['priority'] = 'high'
        
        return classification
    
    def filter_links(self, links: List[Dict[str, Any]], 
                    min_priority: str = 'medium',
                    only_confirmations: bool = False,
                    only_valid: bool = True) -> List[Dict[str, Any]]:
        """
        Filtra links según criterios específicos
        
        Args:
            links: Lista de links a filtrar
            min_priority: Prioridad mínima ('low', 'medium', 'high')
            only_confirmations: Solo links de confirmación
            only_valid: Solo links válidos
        
        Returns:
            Lista de links filtrados
        """
        priority_order = {'low': 0, 'medium': 1, 'high': 2}
        min_priority_value = priority_order.get(min_priority, 1)
        
        filtered_links = []
        
        for link_data in links:
            # Filtrar por validez
            if only_valid and not link_data['validation']['is_valid_url']:
                continue
            
            # Filtrar por confirmaciones
            if only_confirmations and not link_data['classification']['is_confirmation']:
                continue
            
            # Filtrar por prioridad
            link_priority = link_data['classification']['priority']
            link_priority_value = priority_order.get(link_priority, 0)
            
            if link_priority_value >= min_priority_value:
                filtered_links.append(link_data)
        
        self.logger.info(f"Links filtrados: {len(filtered_links)} de {len(links)}")
        return filtered_links
    
    def get_best_confirmation_links(self, links: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Obtiene los mejores links de confirmación
        
        Args:
            links: Lista de links a evaluar
        
        Returns:
            Lista de mejores links de confirmación ordenados por calidad
        """
        # Filtrar solo links de confirmación válidos
        confirmation_links = self.filter_links(
            links, 
            min_priority='medium',
            only_confirmations=True,
            only_valid=True
        )
        
        # Ordenar por calidad (prioridad, validación de token, dominio de interés)
        def link_quality_score(link_data):
            score = 0
            
            # Puntos por prioridad
            priority_scores = {'low': 1, 'medium': 2, 'high': 3}
            score += priority_scores.get(link_data['classification']['priority'], 0)
            
            # Puntos por validación de token
            if link_data['validation']['token_valid']:
                score += 3
            elif link_data['validation']['has_token']:
                score += 1
            
            # Puntos por dominio de interés
            if link_data['classification']['is_domain_of_interest']:
                score += 2
            
            # Puntos por HTTPS
            if link_data['validation']['is_https']:
                score += 1
            
            return score
        
        confirmation_links.sort(key=link_quality_score, reverse=True)
        
        self.logger.info(f"Mejores links de confirmación: {len(confirmation_links)}")
        return confirmation_links
