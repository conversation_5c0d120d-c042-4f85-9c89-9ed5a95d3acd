# 📧 Dujaw Email Monitor

Sistema automatizado para monitorear correos temporales en Dujaw.com y procesar links de confirmación automáticamente.

## 🚀 Características

- ✅ **Monitoreo automático** de buzones temporales
- ✅ **Extracción inteligente** de links de confirmación
- ✅ **Seguimiento automático** de links
- ✅ **Validación completa** de formularios
- ✅ **Logging detallado** de todas las operaciones
- ✅ **Configuración flexible** por línea de comandos

## 📁 Estructura del Proyecto

```
dujaw-email-monitor/
├── README.md                 # Este archivo
├── requirements.txt          # Dependencias Python
├── config.py                # Configuración del proyecto
├── main.py                  # Punto de entrada principal
├── src/                     # Código fuente
│   ├── __init__.py
│   ├── dujaw_client.py      # Cliente para API de Dujaw
│   ├── email_extractor.py   # Extractor de correos y links
│   ├── link_follower.py     # Seguidor de links
│   └── utils.py             # Utilidades comunes
├── logs/                    # Archivos de log
├── output/                  # Archivos de salida
└── tests/                   # Tests unitarios
    └── __init__.py
```

## 🛠️ Instalación

1. **Clonar/Descargar** el proyecto
2. **Instalar dependencias:**
   ```bash
   pip install -r requirements.txt
   ```

## 🎯 Uso

### Comando Básico
```bash
python main.py --email <EMAIL>
```

### Opciones Avanzadas
```bash
python main.py --email <EMAIL> \
               --password tu_password \
               --monitor \
               --follow-links \
               --output-dir ./resultados
```

### Parámetros Disponibles

| Parámetro | Descripción | Requerido | Ejemplo |
|-----------|-------------|-----------|---------|
| `--email` | Email del buzón temporal | ✅ | `<EMAIL>` |
| `--password` | Password del buzón (opcional) | ❌ | `unlockgs2024` |
| `--monitor` | Modo monitoreo continuo | ❌ | `--monitor` |
| `--follow-links` | Seguir links automáticamente | ❌ | `--follow-links` |
| `--output-dir` | Directorio de salida | ❌ | `./output` |
| `--log-level` | Nivel de logging | ❌ | `DEBUG` |
| `--interval` | Intervalo de monitoreo (seg) | ❌ | `30` |

## 📋 Ejemplos de Uso

### 1. Buscar correos una vez
```bash
python main.py --email <EMAIL>
```

### 2. Monitoreo continuo
```bash
python main.py --email <EMAIL> --monitor --interval 60
```

### 3. Buscar y seguir links automáticamente
```bash
python main.py --email <EMAIL> --follow-links
```

### 4. Configuración completa
```bash
python main.py \
  --email <EMAIL> \
  --password unlockgs2024 \
  --monitor \
  --follow-links \
  --output-dir ./mis_resultados \
  --log-level DEBUG \
  --interval 30
```

## 📊 Salida

El sistema genera:

- **📄 Logs detallados** en `logs/dujaw_monitor_YYYYMMDD.log`
- **📧 Correos extraídos** en `output/emails_YYYYMMDD_HHMMSS.json`
- **🔗 Links encontrados** en `output/links_YYYYMMDD_HHMMSS.txt`
- **🌐 Páginas seguidas** en `output/pages_YYYYMMDD_HHMMSS.html`

## 🔧 Configuración

Edita `config.py` para personalizar:

- URLs base de Dujaw
- Timeouts y reintentos
- Patrones de extracción
- Configuración de logging

## 🧪 Testing

```bash
python -m pytest tests/
```

## 📝 Logs

Los logs incluyen:
- ✅ Operaciones exitosas
- ❌ Errores y excepciones  
- 🔍 Detalles de extracción
- 🌐 Respuestas HTTP
- ⏱️ Timestamps precisos

## 🚨 Notas Importantes

- **Buzones temporales:** Los emails pueden expirar automáticamente
- **Rate limiting:** El sistema respeta los límites de Dujaw
- **Privacidad:** No se almacenan passwords en logs
- **Conectividad:** Requiere conexión a internet estable

## 🆘 Solución de Problemas

### Error: "No se pudo acceder al buzón"
- Verificar que el email existe en Dujaw
- Comprobar conectividad a internet
- Revisar si el buzón requiere password

### Error: "No se encontraron correos"
- El buzón puede estar vacío
- Los correos pueden haber expirado
- Usar `--monitor` para esperar nuevos correos

### Error: "Link inválido o expirado"
- Los links de confirmación tienen tiempo limitado
- Ejecutar el sistema más frecuentemente
- Verificar que el link se extrajo correctamente

## 📞 Soporte

Para reportar bugs o solicitar características, crear un issue con:
- Comando ejecutado
- Logs relevantes
- Comportamiento esperado vs actual

---

**🎉 ¡Disfruta automatizando tus correos temporales!**
