#!/usr/bin/env python3
"""
Configuración del sistema Dujaw Email Monitor
"""

import os
from datetime import timedelta

# =============================================================================
# CONFIGURACIÓN PRINCIPAL
# =============================================================================

# URLs base
DUJAW_BASE_URL = "https://dujaw.com"
DUJAW_MAILBOX_URL_TEMPLATE = "https://dujaw.com/mailbox/{email}"
DUJAW_UNLOCK_URL = "https://dujaw.com/unlock"

# Credenciales por defecto (pueden ser sobrescritas por argumentos)
DEFAULT_PASSWORD = "unlockgs2024"
DEFAULT_CONFIRMATION_PASSWORD = "EMVaB#6G3"

# =============================================================================
# CONFIGURACIÓN DE TIMEOUTS Y REINTENTOS
# =============================================================================

# Timeouts HTTP (en segundos)
HTTP_TIMEOUT = 30
HTTP_CONNECT_TIMEOUT = 10

# Reintentos
MAX_RETRIES = 3
RETRY_DELAY = 2  # segundos entre reintentos

# Monitoreo
DEFAULT_MONITOR_INTERVAL = 60  # segundos
MAX_MONITOR_DURATION = 3600   # 1 hora máximo de monitoreo continuo

# =============================================================================
# CONFIGURACIÓN DE HEADERS HTTP
# =============================================================================

DEFAULT_HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'Accept-Language': 'en-US,en;q=0.9',
    'Accept-Encoding': 'gzip, deflate, br',
    'DNT': '1',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'none',
    'Sec-Fetch-User': '?1',
    'Cache-Control': 'max-age=0'
}

LIVEWIRE_HEADERS = {
    'X-Livewire': 'true',
    'Content-Type': 'application/json',
    'Accept': 'text/html, application/xhtml+xml',
    'X-Requested-With': 'XMLHttpRequest'
}

# =============================================================================
# CONFIGURACIÓN DE EXTRACCIÓN
# =============================================================================

# Patrones de links a buscar
LINK_PATTERNS = {
    'pokemon': r'https://club\.pokemon\.com/[^\s<>"\']+email-change-approval/[a-f0-9]+',
    'general_confirmation': r'https?://[^\s<>"\']+(?:confirm|verify|approve|activate)[^\s<>"\']*',
    'all_https': r'https://[^\s<>"\']+(?:[^\s<>"\'.,;!?])'
}

# Dominios de interés para extracción automática
DOMAINS_OF_INTEREST = [
    'club.pokemon.com',
    'accounts.google.com',
    'login.microsoftonline.com',
    'github.com',
    'gitlab.com',
    'discord.com',
    'twitter.com',
    'facebook.com'
]

# Palabras clave para identificar correos importantes
EMAIL_KEYWORDS = [
    'confirm', 'verify', 'activate', 'approve', 'validation',
    'registration', 'signup', 'login', 'password', 'security',
    'account', 'email change', 'verification code'
]

# =============================================================================
# CONFIGURACIÓN DE DIRECTORIOS
# =============================================================================

# Directorios base
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
LOGS_DIR = os.path.join(BASE_DIR, 'logs')
OUTPUT_DIR = os.path.join(BASE_DIR, 'output')
TEMP_DIR = os.path.join(BASE_DIR, 'temp')

# Crear directorios si no existen
for directory in [LOGS_DIR, OUTPUT_DIR, TEMP_DIR]:
    os.makedirs(directory, exist_ok=True)

# =============================================================================
# CONFIGURACIÓN DE LOGGING
# =============================================================================

# Niveles de logging
LOG_LEVELS = {
    'DEBUG': 10,
    'INFO': 20,
    'WARNING': 30,
    'ERROR': 40,
    'CRITICAL': 50
}

DEFAULT_LOG_LEVEL = 'INFO'

# Formato de logs
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
LOG_DATE_FORMAT = '%Y-%m-%d %H:%M:%S'

# Colores para logging en consola
LOG_COLORS = {
    'DEBUG': 'cyan',
    'INFO': 'green',
    'WARNING': 'yellow',
    'ERROR': 'red',
    'CRITICAL': 'red,bg_white',
}

# Rotación de logs
MAX_LOG_SIZE = 10 * 1024 * 1024  # 10MB
MAX_LOG_FILES = 5

# =============================================================================
# CONFIGURACIÓN DE VALIDACIÓN
# =============================================================================

# Validación de emails
EMAIL_REGEX = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'

# Validación de tokens hexadecimales
HEX_TOKEN_MIN_LENGTH = 16
HEX_TOKEN_REGEX = r'^[a-f0-9]+$'

# Validación de URLs
URL_REGEX = r'^https?://[^\s<>"\']+$'

# =============================================================================
# CONFIGURACIÓN DE SALIDA
# =============================================================================

# Formatos de archivo de salida
OUTPUT_FORMATS = {
    'emails': 'json',
    'links': 'txt',
    'pages': 'html',
    'logs': 'log'
}

# Prefijos de archivos de salida
OUTPUT_PREFIXES = {
    'emails': 'emails_',
    'links': 'links_',
    'pages': 'pages_',
    'reports': 'report_'
}

# =============================================================================
# CONFIGURACIÓN DE COMPONENTES LIVEWIRE
# =============================================================================

# Componentes Livewire conocidos
LIVEWIRE_COMPONENTS = {
    'frontend.app': {
        'methods': ['fetchMessages', 'syncEmail', 'getMessages'],
        'events': ['fetchMessages', 'syncEmail']
    },
    'frontend.actions': {
        'methods': ['syncEmail', 'fetchMessages'],
        'events': ['syncEmail']
    },
    'frontend.nav': {
        'methods': ['refresh'],
        'events': ['refresh']
    }
}

# URLs de Livewire
LIVEWIRE_URLS = {
    'message': '/livewire/message/{component}',
    'update': '/livewire/update',
    'component': '/livewire/component/{component}'
}

# =============================================================================
# CONFIGURACIÓN DE DESARROLLO Y DEBUG
# =============================================================================

# Modo debug
DEBUG_MODE = os.getenv('DUJAW_DEBUG', 'False').lower() == 'true'

# Guardar respuestas HTTP para debug
SAVE_HTTP_RESPONSES = DEBUG_MODE

# Mostrar headers HTTP en logs
SHOW_HTTP_HEADERS = DEBUG_MODE

# Tiempo máximo de ejecución para operaciones individuales
MAX_OPERATION_TIME = timedelta(minutes=5)

# =============================================================================
# CONFIGURACIÓN DE SEGURIDAD
# =============================================================================

# No loggear passwords
SENSITIVE_FIELDS = ['password', 'passwd', 'pwd', 'secret', 'token', 'key']

# Máscara para campos sensibles en logs
SENSITIVE_MASK = '***HIDDEN***'

# Verificar SSL por defecto
VERIFY_SSL = True

# =============================================================================
# CONFIGURACIÓN ESPECÍFICA POR ENTORNO
# =============================================================================

# Configuración para desarrollo
if DEBUG_MODE:
    HTTP_TIMEOUT = 60
    MAX_RETRIES = 1
    DEFAULT_LOG_LEVEL = 'DEBUG'
    SAVE_HTTP_RESPONSES = True

# Configuración desde variables de entorno
DUJAW_BASE_URL = os.getenv('DUJAW_BASE_URL', DUJAW_BASE_URL)
DEFAULT_PASSWORD = os.getenv('DUJAW_PASSWORD', DEFAULT_PASSWORD)
DEFAULT_LOG_LEVEL = os.getenv('DUJAW_LOG_LEVEL', DEFAULT_LOG_LEVEL)
