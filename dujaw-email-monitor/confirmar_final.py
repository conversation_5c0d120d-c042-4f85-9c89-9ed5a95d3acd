#!/usr/bin/env python3
"""
Script final para confirmar el cambio de email de Pokémon
Completa específicamente el formulario visible en la imagen
"""

import requests
import time
from bs4 import BeautifulSoup
from datetime import datetime

def confirmar_pokemon_final():
    """Confirma el cambio de email usando el formulario específico"""
    print("🔐 CONFIRMACIÓN FINAL DE EMAIL POKÉMON")
    print("=" * 50)
    
    # Configurar sesión
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
    })
    
    # URL del formulario
    pokemon_url = "https://club.pokemon.com/us/pokemon-trainer-club/email-change-approval/49d9fd87c3028b1267f9825fddf6e825"
    password = "EMVaB#6G3"
    
    print(f"🌐 URL: {pokemon_url}")
    print(f"🔑 Password: {password}")
    
    try:
        # 1. Acceder a la página
        print(f"\n1️⃣ Accediendo a la página...")
        response = session.get(pokemon_url)
        
        print(f"📥 Status: {response.status_code}")
        print(f"🌐 URL final: {response.url}")
        
        if response.status_code != 200:
            print(f"❌ Error HTTP: {response.status_code}")
            return False
        
        # 2. Parsear HTML
        print(f"\n2️⃣ Analizando formulario...")
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Buscar el formulario de confirmación específico
        form = None
        for f in soup.find_all('form'):
            # Buscar formulario que tenga campo de password y botón confirm
            password_field = f.find('input', {'type': 'password'})
            confirm_button = f.find('input', {'value': 'Confirm'}) or f.find('button', string='Confirm')
            
            if password_field and confirm_button:
                form = f
                break
        
        if not form:
            print(f"❌ No se encontró formulario de confirmación")
            return False
        
        print(f"✅ Formulario encontrado")
        
        # 3. Extraer datos del formulario
        print(f"\n3️⃣ Extrayendo datos del formulario...")
        form_data = {}
        
        for input_elem in form.find_all('input'):
            name = input_elem.get('name')
            value = input_elem.get('value', '')
            input_type = input_elem.get('type', 'text')
            
            if not name:
                continue
            
            if input_type == 'password':
                form_data[name] = password
                print(f"🔑 Password: {name} = {password}")
            elif input_type == 'hidden':
                form_data[name] = value
                print(f"🔒 Hidden: {name} = {value[:50]}...")
            elif input_type in ['email', 'text']:
                form_data[name] = value
                print(f"📧 Field: {name} = {value}")
            elif input_type == 'submit' and value:
                form_data[name] = value
                print(f"🔘 Submit: {name} = {value}")
        
        # 4. Determinar URL de envío
        action = form.get('action', '')
        method = form.get('method', 'POST').upper()
        
        if action:
            if action.startswith('http'):
                submit_url = action
            elif action.startswith('/'):
                submit_url = f"https://club.pokemon.com{action}"
            else:
                submit_url = f"{pokemon_url.rstrip('/')}/{action}"
        else:
            # Si no hay action, usar la misma URL
            submit_url = pokemon_url
        
        print(f"\n4️⃣ Enviando confirmación...")
        print(f"📤 URL: {submit_url}")
        print(f"📋 Método: {method}")
        print(f"📊 Campos: {len(form_data)}")
        
        # 5. Enviar formulario
        if method == 'POST':
            confirm_response = session.post(submit_url, data=form_data, allow_redirects=True)
        else:
            confirm_response = session.get(submit_url, params=form_data, allow_redirects=True)
        
        print(f"\n5️⃣ Analizando respuesta...")
        print(f"📥 Status: {confirm_response.status_code}")
        print(f"🌐 URL final: {confirm_response.url}")
        print(f"📄 Tamaño: {len(confirm_response.text)} caracteres")
        
        # 6. Guardar respuesta
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"confirmacion_final_{timestamp}.html"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"<!-- Confirmación final - {timestamp} -->\n")
            f.write(f"<!-- URL original: {pokemon_url} -->\n")
            f.write(f"<!-- URL envío: {submit_url} -->\n")
            f.write(f"<!-- Status: {confirm_response.status_code} -->\n\n")
            f.write(confirm_response.text)
        
        print(f"💾 Respuesta guardada: {filename}")
        
        # 7. Analizar resultado
        print(f"\n6️⃣ Analizando resultado...")
        
        html_lower = confirm_response.text.lower()
        
        # Buscar indicadores de éxito
        success_indicators = [
            'successfully',
            'confirmed',
            'approved',
            'email has been changed',
            'email updated',
            'change complete',
            'verification successful'
        ]
        
        # Buscar indicadores de error
        error_indicators = [
            'error',
            'invalid password',
            'incorrect password',
            'failed',
            'expired',
            'not found'
        ]
        
        success_found = []
        error_found = []
        
        for indicator in success_indicators:
            if indicator in html_lower:
                success_found.append(indicator)
        
        for indicator in error_indicators:
            if indicator in html_lower:
                error_found.append(indicator)
        
        # Verificar cambio en la URL (redirección a página de éxito)
        url_changed = confirm_response.url != pokemon_url
        
        print(f"🔍 Indicadores de éxito: {success_found}")
        print(f"⚠️ Indicadores de error: {error_found}")
        print(f"🌐 URL cambió: {url_changed}")
        
        # Determinar resultado
        if success_found and not error_found:
            print(f"\n🎉 ¡CONFIRMACIÓN EXITOSA!")
            print(f"✅ Indicadores de éxito: {', '.join(success_found)}")
            return True
        elif error_found:
            print(f"\n❌ Error en confirmación:")
            print(f"⚠️ Errores detectados: {', '.join(error_found)}")
            return False
        elif url_changed and confirm_response.status_code == 200:
            print(f"\n✅ Posible éxito (redirección detectada)")
            print(f"🌐 Nueva URL: {confirm_response.url}")
            return True
        else:
            print(f"\n⚠️ Resultado incierto")
            print(f"💡 Revisar archivo: {filename}")
            
            # Buscar en el HTML cualquier mensaje relevante
            soup_result = BeautifulSoup(confirm_response.text, 'html.parser')
            
            # Buscar mensajes en elementos comunes
            message_elements = soup_result.find_all(['div', 'p', 'span'], 
                                                  class_=lambda x: x and any(word in ' '.join(x).lower() 
                                                                           for word in ['message', 'alert', 'notice', 'success', 'error']))
            
            if message_elements:
                print(f"📝 Mensajes encontrados:")
                for elem in message_elements[:3]:
                    text = elem.get_text(strip=True)
                    if text and len(text) > 10:
                        print(f"   • {text[:100]}")
            
            return False
    
    except Exception as e:
        print(f"\n❌ Error durante confirmación: {e}")
        return False
    
    finally:
        session.close()

def main():
    """Función principal"""
    print("🚀 CONFIRMACIÓN FINAL DE EMAIL POKÉMON")
    print("Basado en la página visible en la imagen")
    print("=" * 60)
    
    success = confirmar_pokemon_final()
    
    if success:
        print(f"\n🎉 ¡PROCESO COMPLETADO EXITOSAMENTE!")
        print(f"✅ El cambio de email debería estar confirmado")
        print(f"📧 Email cambiado de: <EMAIL>")
        print(f"📧 Email cambiado a: <EMAIL>")
    else:
        print(f"\n❌ PROCESO NO COMPLETADO")
        print(f"💡 Revisar archivo de respuesta para más detalles")
        print(f"🔍 Verificar manualmente en la página web")
    
    print(f"\n🏁 Proceso finalizado")

if __name__ == "__main__":
    main()
