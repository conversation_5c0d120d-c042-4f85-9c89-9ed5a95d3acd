#!/bin/bash
# Ejemplo de uso del Dujaw Email Monitor
# Sistema automatizado para monitorear correos temporales

echo "🚀 DUJAW EMAIL MONITOR - EJEMPLOS DE USO"
echo "=" * 50

echo ""
echo "📧 1. VERIFICACIÓN ÚNICA BÁSICA"
echo "Busca correos una sola vez en el buzón especificado"
echo "Comando:"
echo "python main.py --email <EMAIL>"
echo ""

echo "📧 2. VERIFICACIÓN CON SEGUIMIENTO DE LINKS"
echo "Busca correos y sigue automáticamente los links de confirmación"
echo "Comando:"
echo "python main.py --email <EMAIL> --follow-links"
echo ""

echo "📧 3. CONFIRMACIÓN AUTOMÁTICA COMPLETA"
echo "Busca correos, sigue links Y completa confirmaciones automáticamente"
echo "Comando:"
echo "python main.py --email <EMAIL> \\"
echo "               --follow-links \\"
echo "               --auto-confirm \\"
echo "               --confirmation-password 'EMVaB#6G3'"
echo ""

echo "📧 4. M<PERSON><PERSON><PERSON><PERSON><PERSON> CONTINUO"
echo "Monitorea el buzón cada 60 segundos buscando nuevos correos"
echo "Comando:"
echo "python main.py --email <EMAIL> \\"
echo "               --monitor \\"
echo "               --interval 60 \\"
echo "               --follow-links"
echo ""

echo "📧 5. CONFIGURACIÓN COMPLETA CON LOGGING DEBUG"
echo "Configuración completa con máximo detalle de logging"
echo "Comando:"
echo "python main.py --email <EMAIL> \\"
echo "               --password unlockgs2024 \\"
echo "               --monitor \\"
echo "               --follow-links \\"
echo "               --auto-confirm \\"
echo "               --confirmation-password 'EMVaB#6G3' \\"
echo "               --output-dir ./mis_resultados \\"
echo "               --log-level DEBUG \\"
echo "               --interval 30"
echo ""

echo "📧 6. EJEMPLO REAL CON EL EMAIL DE PRUEBA"
echo "Comando listo para usar con el email que hemos estado probando:"
echo "python main.py --email <EMAIL> \\"
echo "               --follow-links \\"
echo "               --auto-confirm \\"
echo "               --confirmation-password 'EMVaB#6G3' \\"
echo "               --log-level INFO"
echo ""

echo "📁 ARCHIVOS DE SALIDA GENERADOS:"
echo "- output/results_YYYYMMDD_HHMMSS.json    # Resultados completos"
echo "- output/links_YYYYMMDD_HHMMSS.txt       # Links encontrados"
echo "- output/summary_YYYYMMDD_HHMMSS.txt     # Resumen de ejecución"
echo "- logs/dujaw_monitor_YYYYMMDD.log        # Logs detallados"
echo ""

echo "🎯 CASOS DE USO TÍPICOS:"
echo ""
echo "✅ Verificación de email de Pokémon:"
echo "   python main.py --email <EMAIL> --follow-links --auto-confirm"
echo ""
echo "✅ Monitoreo de registros automáticos:"
echo "   python main.py --email <EMAIL> --monitor --follow-links"
echo ""
echo "✅ Procesamiento batch de múltiples confirmaciones:"
echo "   python main.py --email <EMAIL> --follow-links --auto-confirm --continuous"
echo ""

echo "🔧 INSTALACIÓN:"
echo "1. pip install -r requirements.txt"
echo "2. python main.py --email <EMAIL>"
echo ""

echo "📞 SOPORTE:"
echo "- Revisar logs en ./logs/ para debugging"
echo "- Verificar archivos de salida en ./output/"
echo "- Usar --log-level DEBUG para máximo detalle"
echo ""

echo "🎉 ¡Sistema listo para automatizar tus correos temporales!"
