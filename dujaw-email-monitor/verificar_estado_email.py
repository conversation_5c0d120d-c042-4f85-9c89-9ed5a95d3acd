#!/usr/bin/env python3
"""
Script para verificar el estado actual del email y obtener un link fresco
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.dujaw_client import DujawClient
from src.email_extractor import EmailExtractor
from src.utils import setup_logging

def verificar_estado_email():
    """Verifica el estado actual del buzón y obtiene links frescos"""
    logger = setup_logging('INFO')
    
    print("📧 VERIFICADOR DE ESTADO DE EMAIL")
    print("=" * 50)
    
    email = "<EMAIL>"
    password = "unlockgs2024"
    
    print(f"📧 Email: {email}")
    
    try:
        with DujawClient(email, password) as client:
            print(f"\n1️⃣ Accediendo al buzón...")
            
            # Obtener correos frescos
            emails = client.get_emails(force_refresh=True)
            
            if not emails:
                print(f"❌ No se encontraron correos en el buzón")
                print(f"💡 Esto podría significar que:")
                print(f"   • Los correos expiraron")
                print(f"   • El buzón está vacío")
                print(f"   • Ya se procesaron las confirmaciones")
                return None
            
            print(f"✅ Encontrados {len(emails)} correos")
            
            # Extraer y analizar links
            print(f"\n2️⃣ Analizando correos...")
            extractor = EmailExtractor()
            all_links = extractor.extract_links_from_emails(emails)
            
            print(f"🔗 Total de links encontrados: {len(all_links)}")
            
            # Filtrar links de confirmación
            confirmation_links = extractor.get_best_confirmation_links(all_links)
            
            if not confirmation_links:
                print(f"❌ No se encontraron links de confirmación")
                print(f"💡 Esto podría significar que:")
                print(f"   • Los links expiraron")
                print(f"   • Ya se confirmó el cambio")
                print(f"   • Los correos no contienen links válidos")
                return None
            
            print(f"🎯 Links de confirmación encontrados: {len(confirmation_links)}")
            
            # Mostrar detalles de los links
            print(f"\n3️⃣ Detalles de links de confirmación:")
            
            for i, link_data in enumerate(confirmation_links):
                link = link_data['link']
                validation = link_data['validation']
                classification = link_data['classification']
                
                print(f"\n   Link {i+1}:")
                print(f"   🔗 URL: {link}")
                print(f"   ✅ Válido: {validation['is_valid_url']}")
                print(f"   🔐 Token válido: {validation['token_valid']}")
                print(f"   📏 Longitud token: {validation['token_length']}")
                print(f"   🎯 Tipo: {classification['type']}")
                print(f"   ⭐ Prioridad: {classification['priority']}")
                
                # Verificar si el link está activo
                print(f"   🌐 Verificando accesibilidad...")
                
                import requests
                try:
                    response = requests.head(link, timeout=10, allow_redirects=True)
                    print(f"   📥 Status: {response.status_code}")
                    
                    if response.status_code == 200:
                        print(f"   ✅ Link activo y accesible")
                    elif response.status_code == 404:
                        print(f"   ❌ Link no encontrado (404)")
                    elif response.status_code == 403:
                        print(f"   ⚠️ Acceso denegado (403)")
                    else:
                        print(f"   ⚠️ Status inusual: {response.status_code}")
                        
                except Exception as e:
                    print(f"   ❌ Error verificando link: {e}")
            
            # Retornar el mejor link
            if confirmation_links:
                best_link = confirmation_links[0]['link']
                print(f"\n🏆 Mejor link identificado:")
                print(f"🔗 {best_link}")
                return best_link
            
            return None
            
    except Exception as e:
        logger.error(f"Error verificando estado: {e}")
        return None

def verificar_cambio_completado():
    """Verifica si el cambio de email ya se completó"""
    print(f"\n4️⃣ Verificando si el cambio ya se completó...")
    
    # Intentar acceder a la cuenta con el nuevo email
    nuevo_email = "<EMAIL>"
    
    print(f"📧 Verificando acceso con nuevo email: {nuevo_email}")
    
    try:
        with DujawClient(nuevo_email, "unlockgs2024") as client:
            emails = client.get_emails()
            
            if emails is not None:  # Si puede acceder
                print(f"✅ ¡El cambio de email YA SE COMPLETÓ!")
                print(f"🎉 Nuevo email activo: {nuevo_email}")
                return True
            else:
                print(f"❌ El nuevo email aún no está activo")
                return False
                
    except Exception as e:
        print(f"❌ No se puede acceder con el nuevo email: {e}")
        return False

def main():
    """Función principal"""
    print("🔍 VERIFICACIÓN COMPLETA DE ESTADO DE EMAIL")
    print("=" * 60)
    
    # Verificar estado actual
    fresh_link = verificar_estado_email()
    
    # Verificar si ya se completó
    ya_completado = verificar_cambio_completado()
    
    print(f"\n📊 RESUMEN:")
    print(f"=" * 30)
    
    if ya_completado:
        print(f"🎉 ¡CAMBIO DE EMAIL COMPLETADO!")
        print(f"✅ El email ya cambió exitosamente")
        print(f"📧 Nuevo email activo: <EMAIL>")
    elif fresh_link:
        print(f"⏳ Cambio pendiente de confirmación")
        print(f"🔗 Link fresco disponible: {fresh_link}")
        print(f"💡 Usar este link para confirmar manualmente")
    else:
        print(f"❓ Estado incierto")
        print(f"⚠️ No se encontraron links de confirmación")
        print(f"💡 Posibles causas:")
        print(f"   • Los links expiraron")
        print(f"   • El cambio ya se procesó")
        print(f"   • Los correos fueron eliminados")
    
    print(f"\n🏁 Verificación completada")

if __name__ == "__main__":
    main()
