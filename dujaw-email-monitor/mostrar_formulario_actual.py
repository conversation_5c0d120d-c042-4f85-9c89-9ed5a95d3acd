#!/usr/bin/env python3
"""
Script para mostrar el estado actual del formulario de confirmación de Pokémon
"""

import requests
import sys
import os
from bs4 import BeautifulSoup
from datetime import datetime

sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.dujaw_client import DujawClient
from src.email_extractor import EmailExtractor
from src.utils import setup_logging, save_text

def obtener_link_fresco():
    """Obtiene el link más fresco del buzón"""
    print("📧 Obteniendo link fresco del buzón...")
    
    email = "<EMAIL>"
    password = "unlockgs2024"
    
    try:
        with DujawClient(email, password) as client:
            emails = client.get_emails(force_refresh=True)
            
            if not emails:
                print("❌ No se encontraron correos")
                return None
            
            extractor = EmailExtractor()
            all_links = extractor.extract_links_from_emails(emails)
            confirmation_links = extractor.get_best_confirmation_links(all_links)
            
            if confirmation_links:
                best_link = confirmation_links[0]['link']
                print(f"✅ Link fresco obtenido: {best_link}")
                return best_link
            else:
                print("❌ No se encontraron links de confirmación")
                return None
                
    except Exception as e:
        print(f"❌ Error obteniendo link: {e}")
        return None

def acceder_y_mostrar_formulario(link):
    """Accede al link y muestra el formulario actual"""
    print(f"🌐 Accediendo al formulario de confirmación...")
    print(f"🔗 Link: {link}")
    
    # Configurar sesión
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
    })
    
    try:
        # Acceder al link
        response = session.get(link, allow_redirects=True)
        
        print(f"📥 Status: {response.status_code}")
        print(f"🌐 URL final: {response.url}")
        print(f"📄 Tamaño: {len(response.text)} caracteres")
        
        if response.status_code != 200:
            print(f"❌ Error HTTP: {response.status_code}")
            return None
        
        # Guardar HTML completo
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"formulario_actual_{timestamp}.html"
        
        save_text(response.text, filename)
        print(f"💾 HTML completo guardado: {filename}")
        
        # Parsear HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Mostrar información básica de la página
        print(f"\n📄 INFORMACIÓN DE LA PÁGINA:")
        print("=" * 40)
        
        title = soup.find('title')
        if title:
            print(f"📄 Título: {title.get_text(strip=True)}")
        
        # Buscar encabezados principales
        main_headings = soup.find_all(['h1', 'h2', 'h3'])
        if main_headings:
            print(f"\n📝 ENCABEZADOS PRINCIPALES:")
            for i, heading in enumerate(main_headings[:5], 1):
                text = heading.get_text(strip=True)
                if text:
                    print(f"   {i}. {heading.name.upper()}: {text}")
        
        # Analizar formularios
        print(f"\n📋 FORMULARIOS ENCONTRADOS:")
        print("=" * 40)
        
        forms = soup.find_all('form')
        print(f"Total de formularios: {len(forms)}")
        
        for i, form in enumerate(forms, 1):
            print(f"\n🔸 FORMULARIO {i}:")
            
            action = form.get('action', '')
            method = form.get('method', 'GET')
            form_id = form.get('id', '')
            form_class = form.get('class', [])
            
            print(f"   📋 Método: {method.upper()}")
            print(f"   📤 Action: {action}")
            print(f"   🆔 ID: {form_id}")
            print(f"   🏷️ Class: {' '.join(form_class) if form_class else 'N/A'}")
            
            # Analizar inputs del formulario
            inputs = form.find_all(['input', 'select', 'textarea', 'button'])
            print(f"   📊 Campos: {len(inputs)}")
            
            if inputs:
                print(f"   📝 CAMPOS DEL FORMULARIO:")
                
                for j, input_elem in enumerate(inputs, 1):
                    input_type = input_elem.get('type', input_elem.name)
                    input_name = input_elem.get('name', '')
                    input_value = input_elem.get('value', '')
                    input_placeholder = input_elem.get('placeholder', '')
                    input_id = input_elem.get('id', '')
                    input_required = 'required' if input_elem.has_attr('required') else ''
                    input_readonly = 'readonly' if input_elem.has_attr('readonly') else ''
                    
                    # Mostrar información del campo
                    field_info = f"      {j}. {input_type.upper()}"
                    
                    if input_name:
                        field_info += f" | name='{input_name}'"
                    
                    if input_id:
                        field_info += f" | id='{input_id}'"
                    
                    if input_value:
                        # Mostrar valor truncado para campos sensibles
                        if input_type == 'password':
                            field_info += f" | value='***'"
                        elif 'token' in input_name.lower():
                            field_info += f" | value='{input_value[:20]}...'"
                        else:
                            field_info += f" | value='{input_value}'"
                    
                    if input_placeholder:
                        field_info += f" | placeholder='{input_placeholder}'"
                    
                    if input_required:
                        field_info += f" | {input_required}"
                    
                    if input_readonly:
                        field_info += f" | {input_readonly}"
                    
                    print(field_info)
                    
                    # Si es un botón, mostrar el texto
                    if input_elem.name == 'button' or input_type in ['submit', 'button']:
                        button_text = input_elem.get_text(strip=True) or input_value
                        if button_text:
                            print(f"         📝 Texto del botón: '{button_text}'")
        
        # Buscar mensajes importantes en la página
        print(f"\n💬 MENSAJES EN LA PÁGINA:")
        print("=" * 40)
        
        # Buscar elementos con clases que sugieran mensajes
        message_selectors = [
            '.alert', '.message', '.notification', '.error', '.success', '.warning',
            '[class*="alert"]', '[class*="message"]', '[class*="error"]', '[class*="success"]'
        ]
        
        messages_found = []
        for selector in message_selectors:
            try:
                elements = soup.select(selector)
                for elem in elements:
                    text = elem.get_text(strip=True)
                    if text and len(text) > 10 and text not in messages_found:
                        messages_found.append(text)
            except:
                continue
        
        if messages_found:
            for i, message in enumerate(messages_found[:5], 1):
                print(f"   {i}. {message[:100]}{'...' if len(message) > 100 else ''}")
        else:
            print("   No se encontraron mensajes específicos")
        
        # Buscar texto relacionado con email change
        print(f"\n📧 CONTENIDO RELACIONADO CON CAMBIO DE EMAIL:")
        print("=" * 50)
        
        email_change_text = soup.find_all(string=lambda text: text and any(
            phrase in text.lower() for phrase in [
                'email change', 'change email', 'approve', 'confirm', 'verification',
                'current email', 'new email', 'email address'
            ]
        ))
        
        if email_change_text:
            unique_texts = []
            for text in email_change_text:
                clean_text = text.strip()
                if clean_text and len(clean_text) > 5 and clean_text not in unique_texts:
                    unique_texts.append(clean_text)
            
            for i, text in enumerate(unique_texts[:10], 1):
                print(f"   {i}. {text}")
        else:
            print("   No se encontró contenido específico sobre cambio de email")
        
        return {
            'html': response.text,
            'soup': soup,
            'forms': forms,
            'url': response.url,
            'status': response.status_code
        }
        
    except Exception as e:
        print(f"❌ Error accediendo al formulario: {e}")
        return None
    
    finally:
        session.close()

def main():
    """Función principal"""
    print("🔍 MOSTRAR FORMULARIO ACTUAL DE CONFIRMACIÓN")
    print("=" * 60)
    
    # Usar link conocido o obtener uno fresco
    link_conocido = "https://club.pokemon.com/us/pokemon-trainer-club/email-change-approval/49d9fd87c3028b1267f9825fddf6e825"
    
    print("🔗 Opciones de link:")
    print(f"1. Usar link conocido: {link_conocido}")
    print("2. Obtener link fresco del buzón")
    
    # Por defecto usar el link conocido, pero también intentar obtener uno fresco
    print(f"\n1️⃣ Usando link conocido...")
    resultado = acceder_y_mostrar_formulario(link_conocido)
    
    if not resultado:
        print(f"\n2️⃣ Intentando obtener link fresco...")
        fresh_link = obtener_link_fresco()
        
        if fresh_link and fresh_link != link_conocido:
            print(f"🔄 Probando con link fresco...")
            resultado = acceder_y_mostrar_formulario(fresh_link)
    
    if resultado:
        print(f"\n✅ FORMULARIO ANALIZADO EXITOSAMENTE")
        print(f"🌐 URL: {resultado['url']}")
        print(f"📊 Status: {resultado['status']}")
        print(f"📋 Formularios: {len(resultado['forms'])}")
        
        # Identificar el formulario de confirmación principal
        confirmation_form = None
        for form in resultado['forms']:
            inputs = form.find_all('input')
            has_password = any(inp.get('type') == 'password' for inp in inputs)
            has_email = any('email' in inp.get('name', '').lower() for inp in inputs)
            
            if has_password and has_email:
                confirmation_form = form
                break
        
        if confirmation_form:
            print(f"\n🎯 FORMULARIO DE CONFIRMACIÓN IDENTIFICADO")
            print(f"✅ Este es el formulario que necesitas completar")
            print(f"🔑 Requiere password en el campo de tipo 'password'")
            print(f"📧 Password a usar: EMVaB#6G3")
        else:
            print(f"\n⚠️ No se identificó claramente el formulario de confirmación")
            print(f"💡 Revisar el archivo HTML guardado para más detalles")
    else:
        print(f"\n❌ No se pudo acceder al formulario")
        print(f"💡 El link puede haber expirado o hay problemas de conectividad")
    
    print(f"\n🏁 Análisis completado")

if __name__ == "__main__":
    main()
