#!/usr/bin/env python3
"""
Verificación definitiva del estado del cambio de email
Compara ambos buzones para confirmar el estado actual
"""

import sys
import os
import requests
from datetime import datetime

sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.dujaw_client import DujawClient
from src.utils import setup_logging

def verificar_buzones_completo():
    """Verificación completa de ambos buzones"""
    logger = setup_logging('INFO')
    
    print("🔍 VERIFICACIÓN DEFINITIVA DEL CAMBIO DE EMAIL")
    print("=" * 60)
    
    # Credenciales
    email_original = "<EMAIL>"
    email_nuevo = "<EMAIL>"
    password = "unlockgs2024"
    
    resultados = {
        'timestamp': datetime.now().isoformat(),
        'email_original': {
            'email': email_original,
            'accesible': False,
            'correos_count': 0,
            'correos': [],
            'error': None
        },
        'email_nuevo': {
            'email': email_nuevo,
            'accesible': False,
            'correos_count': 0,
            'correos': [],
            'error': None
        },
        'cambio_completado': False
    }
    
    print(f"📧 Email original: {email_original}")
    print(f"📧 Email nuevo: {email_nuevo}")
    
    # 1. Verificar buzón original
    print(f"\n1️⃣ Verificando buzón original...")
    try:
        with DujawClient(email_original, password) as client:
            emails = client.get_emails(force_refresh=True)
            
            resultados['email_original']['accesible'] = True
            resultados['email_original']['correos_count'] = len(emails) if emails else 0
            resultados['email_original']['correos'] = emails or []
            
            print(f"✅ Buzón original accesible")
            print(f"📊 Correos encontrados: {len(emails) if emails else 0}")
            
            if emails:
                print(f"📧 Correos en buzón original:")
                for i, email in enumerate(emails[:3], 1):
                    sender = email.get('sender_name', 'N/A')
                    subject = email.get('subject', 'N/A')
                    print(f"   {i}. De: {sender} | Asunto: {subject}")
                    
    except Exception as e:
        resultados['email_original']['error'] = str(e)
        print(f"❌ Error accediendo buzón original: {e}")
    
    # 2. Verificar buzón nuevo
    print(f"\n2️⃣ Verificando buzón nuevo...")
    try:
        with DujawClient(email_nuevo, password) as client:
            emails = client.get_emails(force_refresh=True)
            
            resultados['email_nuevo']['accesible'] = True
            resultados['email_nuevo']['correos_count'] = len(emails) if emails else 0
            resultados['email_nuevo']['correos'] = emails or []
            
            print(f"✅ Buzón nuevo accesible")
            print(f"📊 Correos encontrados: {len(emails) if emails else 0}")
            
            if emails:
                print(f"📧 Correos en buzón nuevo:")
                for i, email in enumerate(emails[:3], 1):
                    sender = email.get('sender_name', 'N/A')
                    subject = email.get('subject', 'N/A')
                    print(f"   {i}. De: {sender} | Asunto: {subject}")
                    
    except Exception as e:
        resultados['email_nuevo']['error'] = str(e)
        print(f"❌ Error accediendo buzón nuevo: {e}")
    
    # 3. Verificar estado del link de confirmación
    print(f"\n3️⃣ Verificando estado del link de confirmación...")
    pokemon_link = "https://club.pokemon.com/us/pokemon-trainer-club/email-change-approval/49d9fd87c3028b1267f9825fddf6e825"
    
    try:
        response = requests.get(pokemon_link, timeout=10)
        print(f"🔗 Link de confirmación: {response.status_code}")
        
        if response.status_code == 200:
            # Verificar si aún muestra el formulario o si ya se procesó
            html_lower = response.text.lower()
            
            if 'approve your email change' in html_lower:
                print(f"⏳ Link aún muestra formulario de confirmación")
            elif 'already processed' in html_lower or 'completed' in html_lower:
                print(f"✅ Link indica que ya se procesó")
            else:
                print(f"❓ Estado del link incierto")
                
        elif response.status_code == 404:
            print(f"❌ Link no encontrado (404) - posiblemente expirado")
        else:
            print(f"⚠️ Link responde con status: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error verificando link: {e}")
    
    # 4. Determinar estado final
    print(f"\n4️⃣ Determinando estado final...")
    
    # Lógica para determinar si el cambio se completó
    if resultados['email_nuevo']['accesible']:
        if resultados['email_original']['accesible']:
            # Ambos buzones accesibles - verificar contenido
            correos_original = resultados['email_original']['correos_count']
            correos_nuevo = resultados['email_nuevo']['correos_count']
            
            if correos_nuevo == 0 and correos_original > 0:
                print(f"✅ CAMBIO COMPLETADO: Buzón nuevo vacío, original con correos")
                resultados['cambio_completado'] = True
            elif correos_nuevo > 0:
                print(f"🎉 CAMBIO COMPLETADO: Buzón nuevo activo con correos")
                resultados['cambio_completado'] = True
            else:
                print(f"❓ Estado incierto: Ambos buzones accesibles")
        else:
            print(f"🎉 CAMBIO COMPLETADO: Solo buzón nuevo accesible")
            resultados['cambio_completado'] = True
    else:
        if resultados['email_original']['accesible']:
            print(f"❌ CAMBIO NO COMPLETADO: Solo buzón original accesible")
        else:
            print(f"❌ ERROR: Ningún buzón accesible")
    
    return resultados

def verificar_login_pokemon():
    """Verifica si se puede hacer login en Pokémon con el nuevo email"""
    print(f"\n5️⃣ Verificando login en Pokémon...")
    
    # Esta sería una verificación adicional si tuviéramos las credenciales de Pokémon
    print(f"💡 Para verificar completamente:")
    print(f"   1. Intentar login en pokemon.com con:")
    print(f"      Email: <EMAIL>")
    print(f"      Password: [tu password de Pokémon]")
    print(f"   2. Verificar que el email en el perfil sea el nuevo")

def mostrar_resumen_final(resultados):
    """Muestra el resumen final"""
    print(f"\n📊 RESUMEN FINAL")
    print("=" * 40)
    
    print(f"🕐 Timestamp: {resultados['timestamp']}")
    print(f"📧 Email original: {resultados['email_original']['email']}")
    print(f"   ✅ Accesible: {resultados['email_original']['accesible']}")
    print(f"   📊 Correos: {resultados['email_original']['correos_count']}")
    
    print(f"📧 Email nuevo: {resultados['email_nuevo']['email']}")
    print(f"   ✅ Accesible: {resultados['email_nuevo']['accesible']}")
    print(f"   📊 Correos: {resultados['email_nuevo']['correos_count']}")
    
    print(f"\n🎯 ESTADO DEL CAMBIO:")
    if resultados['cambio_completado']:
        print(f"   🎉 ¡CAMBIO COMPLETADO EXITOSAMENTE!")
        print(f"   ✅ El email de Pokémon cambió a: {resultados['email_nuevo']['email']}")
    else:
        print(f"   ❌ Cambio no completado o estado incierto")
        print(f"   💡 Puede requerir verificación manual")
    
    # Guardar resultados
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"verificacion_definitiva_{timestamp}.json"
    
    import json
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(resultados, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Resultados guardados: {filename}")

def main():
    """Función principal"""
    print("🚀 VERIFICACIÓN DEFINITIVA DEL CAMBIO DE EMAIL")
    print("Comparando estado de ambos buzones")
    print("=" * 70)
    
    try:
        resultados = verificar_buzones_completo()
        verificar_login_pokemon()
        mostrar_resumen_final(resultados)
        
        # Recomendaciones finales
        print(f"\n💡 RECOMENDACIONES:")
        if resultados['cambio_completado']:
            print(f"   ✅ El cambio se completó exitosamente")
            print(f"   🎯 Puedes usar el nuevo email: {resultados['email_nuevo']['email']}")
            print(f"   🔐 Verifica login en pokemon.com para confirmar")
        else:
            print(f"   ⚠️ El cambio puede no haberse completado")
            print(f"   🔄 Intenta acceder al link de confirmación manualmente")
            print(f"   📞 Contacta soporte de Pokémon si persiste el problema")
        
    except Exception as e:
        print(f"❌ Error en verificación: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n🏁 Verificación completada")

if __name__ == "__main__":
    main()
