#!/usr/bin/env python3
"""
Example usage of the Enhanced Dujaw API
Demonstrates advanced features like filtering, notifications, and monitoring
"""

import json
import time
from datetime import datetime
from dujaw_api_enhanced import EnhancedDujawAPI, MessageFilter, MessagePriority, NotificationConfig

def setup_notifications():
    """Setup notification configuration"""
    config = NotificationConfig(
        # Email notifications (configure with your SMTP settings)
        email_enabled=False,  # Set to True to enable
        email_smtp_server="smtp.gmail.com",
        email_smtp_port=587,
        email_username="<EMAIL>",
        email_password="your-app-password",
        email_recipients=["<EMAIL>"],
        
        # Webhook notifications
        webhook_enabled=True,
        webhook_url="http://localhost:5000/webhook",
        webhook_headers={"Authorization": "Bearer test-token"}
    )
    return config

def setup_filters():
    """Setup message filters"""
    filters = [
        # Filter for urgent messages
        MessageFilter(
            subject_patterns=["urgent", "important", "asap"],
            min_priority=MessagePriority.HIGH,
            max_age_hours=24
        ),
        
        # Filter for specific senders
        MessageFilter(
            sender_patterns=[".*@important-domain\\.com", "admin@.*"],
            min_priority=MessagePriority.NORMAL,
            exclude_read=True
        )
    ]
    return filters

def new_message_handler(message):
    """Handle new message events"""
    print(f"\n🔔 NEW MESSAGE ALERT!")
    print(f"   From: {message.sender}")
    print(f"   Subject: {message.subject}")
    print(f"   Priority: {message.priority.value}")
    print(f"   Time: {message.timestamp}")
    print(f"   Content: {message.content[:100]}...")

def error_handler(error_data):
    """Handle error events"""
    print(f"\n❌ ERROR: {error_data.get('type', 'unknown')}")
    if 'error' in error_data:
        print(f"   Details: {error_data['error']}")

def authentication_failed_handler(data):
    """Handle authentication failure events"""
    print(f"\n🔐 AUTHENTICATION FAILED: {data.get('reason', 'unknown')}")

def main():
    """Main demonstration function"""
    print("🚀 ENHANCED DUJAW API DEMONSTRATION")
    print("=" * 50)
    
    # Initialize API with configuration
    api = EnhancedDujawAPI(config_file="config.json")
    
    # Setup notifications
    api.notification_config = setup_notifications()
    
    # Setup filters
    api.filters = setup_filters()
    
    # Add event handlers
    api.add_event_handler('new_message', new_message_handler)
    api.add_event_handler('error', error_handler)
    api.add_event_handler('authentication_failed', authentication_failed_handler)
    
    try:
        # 1. Unlock mailbox
        print("\n1. 🔓 Unlocking mailbox...")
        if api.unlock_mailbox():
            print("✅ Mailbox unlocked successfully!")
        else:
            print("❌ Failed to unlock mailbox")
            return
        
        # 2. Get messages with filtering
        print("\n2. 📧 Getting filtered messages...")
        messages = api.get_messages(apply_filters=True)
        print(f"✅ Found {len(messages)} messages after filtering")
        
        # Display messages
        for i, message in enumerate(messages[:5]):  # Show first 5
            print(f"\n   Message {i+1}:")
            print(f"   📧 From: {message.sender}")
            print(f"   📝 Subject: {message.subject}")
            print(f"   ⚡ Priority: {message.priority.value}")
            print(f"   📅 Time: {message.timestamp}")
            print(f"   💬 Content: {message.content[:100]}...")
        
        # 3. Get statistics
        print("\n3. 📊 Getting mailbox statistics...")
        stats = api.get_statistics()
        print("✅ Statistics:")
        print(f"   📧 Total messages: {stats['total_messages']}")
        print(f"   📬 Unread messages: {stats['unread_messages']}")
        print(f"   🔥 Priority breakdown:")
        for priority, count in stats['priority_breakdown'].items():
            print(f"      {priority}: {count}")
        print(f"   🔍 Active filters: {stats['filters_active']}")
        print(f"   📡 Notifications enabled: {stats['notifications_enabled']}")
        
        # 4. Start monitoring
        print("\n4. 👁️ Starting background monitoring...")
        api.start_monitoring(interval=10)  # Check every 10 seconds for demo
        print("✅ Background monitoring started!")
        print("   Monitoring for new messages every 10 seconds...")
        
        # 5. Simulate monitoring for a while
        print("\n5. ⏱️ Monitoring for 60 seconds (demo)...")
        print("   (In production, this would run continuously)")
        
        for i in range(6):  # 6 x 10 seconds = 60 seconds
            print(f"   Monitoring... {(i+1)*10}s elapsed")
            time.sleep(10)
            
            # Check for new messages manually (for demo)
            new_messages = api.check_new_messages()
            if new_messages:
                print(f"   🔔 Found {len(new_messages)} new messages!")
        
        # 6. Stop monitoring
        print("\n6. 🛑 Stopping monitoring...")
        api.stop_monitoring()
        print("✅ Monitoring stopped!")
        
        # 7. Final statistics
        print("\n7. 📈 Final statistics...")
        final_stats = api.get_statistics()
        print(f"   📧 Total messages processed: {final_stats['total_messages']}")
        print(f"   💾 Messages in cache: {final_stats['cache_size']}")
        
    except KeyboardInterrupt:
        print("\n\n⚠️ Interrupted by user")
        api.stop_monitoring()
    except Exception as e:
        print(f"\n❌ Error during demonstration: {e}")
        api.stop_monitoring()
    
    print("\n🏁 Demonstration completed!")
    print("\n💡 Tips for production use:")
    print("   - Configure email/webhook notifications in config.json")
    print("   - Set up appropriate message filters")
    print("   - Run monitoring in a separate process/service")
    print("   - Monitor the log files for errors")
    print("   - Use the webhook server for testing notifications")

def test_webhook_notifications():
    """Test webhook notifications separately"""
    print("\n🔗 TESTING WEBHOOK NOTIFICATIONS")
    print("=" * 40)
    
    # Make sure webhook server is running
    print("📡 Make sure webhook server is running:")
    print("   python webhook_server.py")
    print("\n⏱️ Waiting 5 seconds for you to start the webhook server...")
    time.sleep(5)
    
    # Initialize API with webhook enabled
    api = EnhancedDujawAPI()
    api.notification_config.webhook_enabled = True
    api.notification_config.webhook_url = "http://localhost:5000/webhook"
    
    # Create a test message
    from dujaw_api_enhanced import Message, MessagePriority
    test_message = Message(
        id="test_msg_001",
        subject="Test Webhook Notification",
        sender="<EMAIL>",
        content="This is a test message to verify webhook notifications are working.",
        html_content="<p>Test message</p>",
        timestamp=datetime.now(),
        priority=MessagePriority.HIGH
    )
    
    # Send webhook notification
    print("📤 Sending test webhook notification...")
    api.send_webhook_notification(test_message)
    print("✅ Test webhook sent!")
    print("🌐 Check the webhook server at: http://localhost:5000")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "webhook-test":
        test_webhook_notifications()
    else:
        main()
