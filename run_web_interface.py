#!/usr/bin/env python3
"""
Startup script for Dujaw Web Interface
Handles installation of dependencies and starts the web server
"""

import subprocess
import sys
import os
from pathlib import Path

def install_requirements():
    """Install required packages"""
    print("🔧 Installing required packages...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    directories = ["templates", "static/css", "static/js", "logs"]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"📁 Created directory: {directory}")

def check_files():
    """Check if all required files exist"""
    required_files = [
        "app.py",
        "dujaw_api_final.py",
        "templates/dashboard.html",
        "static/css/dashboard.css",
        "static/js/dashboard.js"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ Missing required files:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    
    print("✅ All required files found!")
    return True

def start_server():
    """Start the FastAPI server"""
    print("\n🚀 Starting Dujaw Web Interface...")
    print("📱 Web interface will be available at: http://localhost:8000")
    print("🔗 API documentation at: http://localhost:8000/docs")
    print("\n⚠️  Press Ctrl+C to stop the server\n")
    
    try:
        # Import and run the app
        import uvicorn
        uvicorn.run(
            "app:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except ImportError:
        print("❌ FastAPI/Uvicorn not installed. Installing dependencies...")
        if install_requirements():
            start_server()
    except Exception as e:
        print(f"❌ Error starting server: {e}")

def main():
    """Main startup function"""
    print("🎯 DUJAW WEB INTERFACE STARTUP")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not Path("dujaw_api_final.py").exists():
        print("❌ dujaw_api_final.py not found!")
        print("   Make sure you're running this script from the project directory")
        sys.exit(1)
    
    # Create necessary directories
    create_directories()
    
    # Check for required files
    if not check_files():
        print("\n❌ Setup incomplete. Please ensure all files are present.")
        sys.exit(1)
    
    # Install dependencies if needed
    try:
        import fastapi
        import uvicorn
        print("✅ Dependencies already installed!")
    except ImportError:
        print("📦 Installing dependencies...")
        if not install_requirements():
            sys.exit(1)
    
    # Start the server
    start_server()

if __name__ == "__main__":
    main()
