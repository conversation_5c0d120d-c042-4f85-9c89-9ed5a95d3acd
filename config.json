{"notifications": {"email_enabled": false, "email_smtp_server": "smtp.gmail.com", "email_smtp_port": 587, "email_username": "", "email_password": "", "email_recipients": [], "webhook_enabled": false, "webhook_url": "", "webhook_headers": {"Authorization": "Bearer YOUR_TOKEN_HERE"}}, "filters": [{"sender_patterns": [".*@important-domain\\.com"], "subject_patterns": ["urgent", "important"], "content_patterns": [], "min_priority": "normal", "max_age_hours": 24, "exclude_read": false}], "monitoring": {"enabled": true, "interval_seconds": 30, "auto_start": true}, "logging": {"level": "INFO", "file": "dujaw_api.log", "max_size_mb": 10, "backup_count": 5}}