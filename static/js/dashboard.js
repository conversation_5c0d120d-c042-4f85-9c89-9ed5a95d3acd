// Dashboard JavaScript
class DujawDashboard {
    constructor() {
        this.autoRefreshInterval = null;
        this.isConnected = false;
        this.init();
    }

    init() {
        this.bindEvents();
        this.updateConnectionStatus('connecting');
        this.checkStatus();
        this.startAutoRefresh();
    }

    bindEvents() {
        // Button events
        document.getElementById('unlockBtn').addEventListener('click', () => this.unlockMailbox());
        document.getElementById('refreshBtn').addEventListener('click', () => this.refreshMailbox());
        document.getElementById('statusBtn').addEventListener('click', () => this.checkStatus());
        document.getElementById('logoutBtn').addEventListener('click', () => this.logout());
        document.getElementById('composeBtn').addEventListener('click', () => this.showComposeModal());
        document.getElementById('sendMessageBtn').addEventListener('click', () => this.sendMessage());

        // Auto-refresh toggle
        document.getElementById('autoRefreshToggle').addEventListener('change', (e) => {
            if (e.target.checked) {
                this.startAutoRefresh();
            } else {
                this.stopAutoRefresh();
            }
        });

        // Enter key for password input
        document.getElementById('passwordInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.unlockMailbox();
            }
        });
    }

    async makeRequest(url, options = {}) {
        try {
            const response = await fetch(url, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('Request failed:', error);
            this.showNotification('Request failed: ' + error.message, 'error');
            throw error;
        }
    }

    async unlockMailbox() {
        const btn = document.getElementById('unlockBtn');
        const password = document.getElementById('passwordInput').value;

        this.setButtonLoading(btn, true);

        try {
            const response = await this.makeRequest('/api/unlock', {
                method: 'POST',
                body: JSON.stringify({ password: password || null })
            });

            if (response.success) {
                this.showNotification('Mailbox unlocked successfully!', 'success');
                this.updateConnectionStatus('connected');
                this.updateStatus(response.status);
                this.refreshMailbox();
            } else {
                this.showNotification(response.message || 'Failed to unlock mailbox', 'error');
            }
        } catch (error) {
            this.updateConnectionStatus('disconnected');
        } finally {
            this.setButtonLoading(btn, false);
        }
    }

    async refreshMailbox() {
        const btn = document.getElementById('refreshBtn');
        this.setButtonLoading(btn, true);

        try {
            const response = await this.makeRequest('/api/refresh', {
                method: 'POST'
            });

            if (response.success) {
                this.showNotification('Mailbox refreshed!', 'success');
                this.updateMailboxContent(response.data);
                this.updateConnectionStatus('connected');
            } else {
                this.showNotification(response.message || 'Failed to refresh mailbox', 'error');
            }
        } catch (error) {
            this.updateConnectionStatus('disconnected');
        } finally {
            this.setButtonLoading(btn, false);
        }
    }

    async checkStatus() {
        const btn = document.getElementById('statusBtn');
        this.setButtonLoading(btn, true);

        try {
            const status = await this.makeRequest('/api/status');
            this.updateStatus(status);
            this.updateConnectionStatus('connected');
            this.showNotification('Status updated!', 'info');
        } catch (error) {
            this.updateConnectionStatus('disconnected');
        } finally {
            this.setButtonLoading(btn, false);
        }
    }

    async logout() {
        const btn = document.getElementById('logoutBtn');
        this.setButtonLoading(btn, true);

        try {
            const response = await this.makeRequest('/api/logout', {
                method: 'POST'
            });

            if (response.success) {
                this.showNotification('Logged out successfully!', 'success');
                this.updateConnectionStatus('disconnected');
                this.clearMailboxContent();
                this.updateStatus({
                    status: 'disconnected',
                    authenticated: false,
                    message_count: 0,
                    available_actions: 0
                });
            } else {
                this.showNotification(response.message || 'Failed to logout', 'error');
            }
        } catch (error) {
            // Even if request fails, clear local state
            this.updateConnectionStatus('disconnected');
            this.clearMailboxContent();
        } finally {
            this.setButtonLoading(btn, false);
        }
    }

    showComposeModal() {
        const modal = new bootstrap.Modal(document.getElementById('composeModal'));
        modal.show();
    }

    async sendMessage() {
        const btn = document.getElementById('sendMessageBtn');
        const to = document.getElementById('composeTo').value;
        const subject = document.getElementById('composeSubject').value;
        const body = document.getElementById('composeBody').value;

        if (!to || !subject || !body) {
            this.showNotification('Please fill in all fields', 'warning');
            return;
        }

        this.setButtonLoading(btn, true);

        try {
            const response = await this.makeRequest('/api/compose', {
                method: 'POST',
                body: JSON.stringify({ to, subject, body })
            });

            if (response.success) {
                this.showNotification('Message sent successfully!', 'success');
                bootstrap.Modal.getInstance(document.getElementById('composeModal')).hide();
                document.getElementById('composeForm').reset();
            } else {
                this.showNotification(response.message || 'Failed to send message', 'error');
            }
        } catch (error) {
            // Error already handled in makeRequest
        } finally {
            this.setButtonLoading(btn, false);
        }
    }

    updateConnectionStatus(status) {
        const indicator = document.getElementById('connectionStatus');
        const text = document.getElementById('connectionText');

        indicator.className = 'fas fa-circle status-indicator';
        
        switch (status) {
            case 'connected':
                indicator.classList.add('connected');
                text.textContent = 'Connected';
                this.isConnected = true;
                break;
            case 'disconnected':
                indicator.classList.add('disconnected');
                text.textContent = 'Disconnected';
                this.isConnected = false;
                break;
            case 'connecting':
                indicator.classList.add('connecting');
                text.textContent = 'Connecting...';
                this.isConnected = false;
                break;
        }
    }

    updateStatus(status) {
        document.getElementById('messageCount').textContent = status.message_count || 0;
        document.getElementById('actionCount').textContent = status.available_actions || 0;
        document.getElementById('lastUpdate').textContent = status.last_check ? 
            new Date(status.last_check).toLocaleString() : 'Never';

        const authBadge = document.getElementById('authStatus');
        if (status.authenticated) {
            authBadge.textContent = 'Authenticated';
            authBadge.className = 'badge bg-success';
        } else {
            authBadge.textContent = 'Not Authenticated';
            authBadge.className = 'badge bg-secondary';
        }
    }

    updateMailboxContent(data) {
        this.updateMessages(data.messages || []);
        this.updateActions(data.actions || []);
    }

    updateMessages(messages) {
        const container = document.getElementById('messagesContainer');
        
        if (!messages || messages.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-envelope-open-text fa-3x mb-3"></i>
                    <p>No messages found in mailbox.</p>
                </div>
            `;
            return;
        }

        container.innerHTML = messages.map((message, index) => `
            <div class="message-item fade-in">
                <div class="message-header">
                    <div class="message-subject">Message ${index + 1}</div>
                    <small class="text-muted">${new Date().toLocaleString()}</small>
                </div>
                <div class="message-content text-truncate-2">
                    ${this.escapeHtml(message.content || 'No content')}
                </div>
                <div class="message-actions">
                    <button class="btn btn-sm btn-outline-primary me-2" onclick="dashboard.viewMessage(${index})">
                        <i class="fas fa-eye me-1"></i>View
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="dashboard.deleteMessage(${index})">
                        <i class="fas fa-trash me-1"></i>Delete
                    </button>
                </div>
            </div>
        `).join('');
    }

    updateActions(actions) {
        const container = document.getElementById('actionsContainer');
        
        if (!actions || actions.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-cogs fa-3x mb-3"></i>
                    <p>No actions available.</p>
                </div>
            `;
            return;
        }

        container.innerHTML = actions.map(action => `
            <a href="${action.href || '#'}" class="action-item" 
               ${action.onclick ? `onclick="${this.escapeHtml(action.onclick)}"` : ''}>
                <i class="fas fa-link"></i>
                ${this.escapeHtml(action.text || 'Action')}
            </a>
        `).join('');
    }

    clearMailboxContent() {
        document.getElementById('messagesContainer').innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-envelope-open-text fa-3x mb-3"></i>
                <p>No messages loaded. Click "Unlock" to access your mailbox.</p>
            </div>
        `;
        
        document.getElementById('actionsContainer').innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-cogs fa-3x mb-3"></i>
                <p>No actions available. Access your mailbox to see available options.</p>
            </div>
        `;
    }

    setButtonLoading(button, loading) {
        if (loading) {
            button.classList.add('btn-loading');
            button.disabled = true;
        } else {
            button.classList.remove('btn-loading');
            button.disabled = false;
        }
    }

    showNotification(message, type = 'info') {
        const toast = document.getElementById('notificationToast');
        const toastMessage = document.getElementById('toastMessage');
        
        toastMessage.textContent = message;
        
        // Update toast styling based on type
        toast.className = `toast ${type === 'error' ? 'bg-danger text-white' : 
                                  type === 'success' ? 'bg-success text-white' : 
                                  type === 'warning' ? 'bg-warning' : 'bg-info text-white'}`;
        
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
    }

    startAutoRefresh() {
        this.stopAutoRefresh();
        this.autoRefreshInterval = setInterval(() => {
            if (this.isConnected) {
                this.checkStatus();
            }
        }, 30000); // 30 seconds
    }

    stopAutoRefresh() {
        if (this.autoRefreshInterval) {
            clearInterval(this.autoRefreshInterval);
            this.autoRefreshInterval = null;
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Message actions
    viewMessage(index) {
        this.showNotification(`Viewing message ${index + 1}`, 'info');
        // Implement message viewing logic
    }

    deleteMessage(index) {
        if (confirm('Are you sure you want to delete this message?')) {
            this.showNotification(`Message ${index + 1} deleted`, 'success');
            // Implement message deletion logic
        }
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new DujawDashboard();
});
