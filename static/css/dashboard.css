/* Dashboard Styles */
body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Status Indicator */
.status-indicator {
    font-size: 0.8rem;
    margin-right: 0.5rem;
}

.status-indicator.connected {
    color: #28a745;
}

.status-indicator.disconnected {
    color: #dc3545;
}

.status-indicator.connecting {
    color: #ffc107;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Cards */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* Statistics */
.stat-item {
    padding: 1rem 0;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #495057;
}

.stat-label {
    font-size: 0.875rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Messages */
.message-item {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 0.75rem;
    background-color: #fff;
    transition: all 0.2s ease;
}

.message-item:hover {
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.message-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.message-subject {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.25rem;
}

.message-from {
    font-size: 0.875rem;
    color: #6c757d;
}

.message-content {
    color: #495057;
    font-size: 0.9rem;
    line-height: 1.4;
    margin-top: 0.5rem;
}

.message-actions {
    margin-top: 0.75rem;
    padding-top: 0.75rem;
    border-top: 1px solid #f8f9fa;
}

/* Actions */
.action-item {
    display: inline-block;
    margin: 0.25rem;
    padding: 0.5rem 1rem;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    text-decoration: none;
    color: #495057;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.action-item:hover {
    background-color: #e9ecef;
    color: #495057;
    text-decoration: none;
    transform: translateY(-1px);
}

.action-item i {
    margin-right: 0.5rem;
}

/* Buttons */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-loading {
    position: relative;
    color: transparent !important;
}

.btn-loading::after {
    content: "";
    position: absolute;
    width: 1rem;
    height: 1rem;
    top: 50%;
    left: 50%;
    margin-left: -0.5rem;
    margin-top: -0.5rem;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Form Controls */
.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Badges */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
}

/* Toast Notifications */
.toast {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.toast-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

/* Loading States */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container-fluid {
        padding: 0.5rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
    
    .message-item {
        padding: 0.75rem;
    }
    
    .btn {
        font-size: 0.875rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #212529;
        color: #fff;
    }
    
    .card {
        background-color: #343a40;
        color: #fff;
    }
    
    .card-header {
        background-color: #343a40;
        border-bottom-color: #495057;
    }
    
    .message-item {
        background-color: #343a40;
        border-color: #495057;
    }
    
    .action-item {
        background-color: #495057;
        border-color: #6c757d;
        color: #fff;
    }
    
    .action-item:hover {
        background-color: #6c757d;
        color: #fff;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Utility Classes */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.border-start-primary {
    border-left: 4px solid #007bff !important;
}

.border-start-success {
    border-left: 4px solid #28a745 !important;
}

.border-start-warning {
    border-left: 4px solid #ffc107 !important;
}

.border-start-danger {
    border-left: 4px solid #dc3545 !important;
}
