#!/usr/bin/env python3
"""
Script de debug para analizar el contenido completo del mailbox
Captura todo el HTML y busca correos incluso si están marcados como leídos
"""

import sys
import os
import json
from datetime import datetime
from dujaw_interactive import DujawAPIParametrized

def save_html_content(html_content, filename):
    """Guarda el contenido HTML para análisis"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(html_content)
        print(f"💾 HTML guardado en: {filename}")
    except Exception as e:
        print(f"❌ Error guardando HTML: {e}")

def analyze_mailbox_html(api):
    """Analiza el HTML completo del mailbox"""
    try:
        print("🔍 ANÁLISIS DETALLADO DEL MAILBOX")
        print("=" * 50)
        
        # Acceder al mailbox y obtener respuesta cruda
        response = api.session.get(api.mailbox_url)
        
        if response.status_code == 200:
            html_content = response.text
            
            # Guardar HTML para análisis
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"mailbox_debug_{timestamp}.html"
            save_html_content(html_content, filename)
            
            print(f"📄 Contenido HTML capturado ({len(html_content)} caracteres)")
            
            # Buscar patrones de correos
            print("\n🔍 Buscando patrones de correos...")
            
            # Patrones comunes para correos
            email_patterns = [
                'email', 'mail', 'message', 'inbox', 'from', 'subject', 
                'sender', 'recipient', 'body', 'content', 'read', 'unread'
            ]
            
            found_patterns = {}
            for pattern in email_patterns:
                count = html_content.lower().count(pattern)
                if count > 0:
                    found_patterns[pattern] = count
            
            if found_patterns:
                print("✅ Patrones encontrados:")
                for pattern, count in found_patterns.items():
                    print(f"   {pattern}: {count} veces")
            else:
                print("❌ No se encontraron patrones de correo")
            
            # Buscar elementos específicos
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Buscar todos los divs, trs, y otros elementos que podrían contener correos
            print("\n🔍 Analizando elementos HTML...")
            
            # Buscar por clases que contengan palabras relacionadas con correos
            all_elements = soup.find_all(['div', 'tr', 'td', 'li', 'span', 'p'])
            
            potential_emails = []
            for element in all_elements:
                # Verificar clases
                classes = element.get('class', [])
                class_str = ' '.join(classes).lower() if classes else ''
                
                # Verificar IDs
                element_id = element.get('id', '').lower()
                
                # Verificar contenido de texto
                text_content = element.get_text(strip=True)
                
                # Buscar indicadores de correo
                email_indicators = ['@', 'from:', 'to:', 'subject:', 'message', 'mail']
                
                if any(indicator in text_content.lower() for indicator in email_indicators):
                    potential_emails.append({
                        'tag': element.name,
                        'classes': classes,
                        'id': element_id,
                        'text': text_content[:200],
                        'html': str(element)[:300]
                    })
                
                # También buscar por clases/IDs relacionados
                if any(keyword in class_str for keyword in ['mail', 'message', 'inbox', 'email']):
                    potential_emails.append({
                        'tag': element.name,
                        'classes': classes,
                        'id': element_id,
                        'text': text_content[:200],
                        'html': str(element)[:300]
                    })
                
                if any(keyword in element_id for keyword in ['mail', 'message', 'inbox', 'email']):
                    potential_emails.append({
                        'tag': element.name,
                        'classes': classes,
                        'id': element_id,
                        'text': text_content[:200],
                        'html': str(element)[:300]
                    })
            
            # Eliminar duplicados
            unique_emails = []
            seen_texts = set()
            for email in potential_emails:
                if email['text'] not in seen_texts and len(email['text']) > 10:
                    unique_emails.append(email)
                    seen_texts.add(email['text'])
            
            print(f"📧 Elementos potenciales encontrados: {len(unique_emails)}")
            
            if unique_emails:
                print("\n📬 CONTENIDO ENCONTRADO:")
                for i, email in enumerate(unique_emails[:10], 1):  # Mostrar primeros 10
                    print(f"\n{i}. Elemento: {email['tag']}")
                    if email['classes']:
                        print(f"   Clases: {email['classes']}")
                    if email['id']:
                        print(f"   ID: {email['id']}")
                    print(f"   Texto: {email['text']}")
                    print(f"   HTML: {email['html'][:150]}...")
            else:
                print("❌ No se encontraron elementos que parezcan correos")
            
            # Buscar formularios y botones
            print("\n🔍 Buscando formularios y acciones...")
            forms = soup.find_all('form')
            buttons = soup.find_all(['button', 'input'])
            links = soup.find_all('a')
            
            print(f"📝 Formularios encontrados: {len(forms)}")
            print(f"🔘 Botones encontrados: {len(buttons)}")
            print(f"🔗 Enlaces encontrados: {len(links)}")
            
            # Mostrar algunos botones/enlaces interesantes
            interesting_actions = []
            for button in buttons:
                text = button.get_text(strip=True) or button.get('value', '')
                if text and any(keyword in text.lower() for keyword in ['read', 'delete', 'reply', 'forward', 'mark']):
                    interesting_actions.append(f"Botón: {text}")
            
            for link in links:
                text = link.get_text(strip=True)
                href = link.get('href', '')
                if text and any(keyword in text.lower() for keyword in ['read', 'delete', 'reply', 'forward', 'mark']):
                    interesting_actions.append(f"Enlace: {text} -> {href}")
            
            if interesting_actions:
                print("\n🔧 Acciones interesantes encontradas:")
                for action in interesting_actions[:5]:
                    print(f"   {action}")
            
            return {
                'html_length': len(html_content),
                'patterns_found': found_patterns,
                'potential_emails': len(unique_emails),
                'forms': len(forms),
                'buttons': len(buttons),
                'links': len(links),
                'filename': filename
            }
            
        else:
            print(f"❌ Error accediendo al mailbox: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Error en análisis: {e}")
        return None

def main():
    """Función principal de debug"""
    print("🐛 DEBUG MAILBOX DUJAW")
    print("=" * 30)
    
    # Credenciales
    EMAIL = "<EMAIL>"
    PASSWORD = "EMVaB#6G3"
    
    print(f"📧 Email: {EMAIL}")
    print(f"🔐 Password: {PASSWORD}")
    
    try:
        # Crear API
        api = DujawAPIParametrized(EMAIL, PASSWORD)
        
        # Hacer unlock
        print("\n🔓 Realizando unlock...")
        if api.unlock_mailbox():
            print("✅ Unlock exitoso")
            
            # Análisis detallado
            result = analyze_mailbox_html(api)
            
            if result:
                print(f"\n📊 RESUMEN DEL ANÁLISIS:")
                print(f"   📄 Tamaño HTML: {result['html_length']} caracteres")
                print(f"   🔍 Patrones encontrados: {len(result['patterns_found'])}")
                print(f"   📧 Elementos potenciales: {result['potential_emails']}")
                print(f"   📝 Formularios: {result['forms']}")
                print(f"   🔘 Botones: {result['buttons']}")
                print(f"   🔗 Enlaces: {result['links']}")
                print(f"   💾 Archivo guardado: {result['filename']}")
                
                # Guardar resumen en JSON
                summary_file = f"mailbox_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                with open(summary_file, 'w') as f:
                    json.dump(result, f, indent=2)
                print(f"   📋 Resumen guardado: {summary_file}")
            
            # Cerrar sesión
            api.logout()
            
        else:
            print("❌ Error en unlock")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
