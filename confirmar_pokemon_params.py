#!/usr/bin/env python3
"""
Script para confirmar cambios de email de Pokémon con parámetros
Uso: python confirmar_pokemon_params.py EMAIL PASSWORD_POKEMON
"""

import sys
import requests
import json
import re
from datetime import datetime
from bs4 import BeautifulSoup
from dujaw_api_final import DujawAPI

# Password fijo para todos los buzones de Dujaw
DUJAW_PASSWORD = "unlockgs2024"

def mostrar_ayuda():
    """Muestra la ayuda de uso"""
    print("🚀 CONFIRMADOR AUTOMÁTICO DE POKÉMON")
    print("=" * 60)
    print("📋 USO:")
    print("   python confirmar_pokemon_params.py EMAIL PASSWORD_POKEMON")
    print()
    print("📝 PARÁMETROS:")
    print("   EMAIL           - Email temporal de Dujaw (ej: <EMAIL>)")
    print("   PASSWORD_POKEMON - Password de la cuenta de Pokémon para confirmar")
    print()
    print("💡 EJEMPLOS:")
    print('   python confirmar_pokemon_params.py "<EMAIL>" "MiPass#123"')
    print('   python confirmar_pokemon_params.py <EMAIL> EMVaB#6G3')
    print()
    print("📌 NOTA:")
    print("   - El password de Dujaw siempre es: unlockgs2024 (automático)")
    print("   - Solo necesitas especificar el email y password de Pokémon")

def confirmar_pokemon_automatico(email, password_pokemon):
    """
    Confirma automáticamente cambios de email de Pokémon
    
    Args:
        email: Email temporal de Dujaw
        password_pokemon: Password de la cuenta de Pokémon
    """
    print("🚀 CONFIRMADOR AUTOMÁTICO DE POKÉMON")
    print("=" * 60)
    print(f"📧 Email: {email}")
    print(f"🔑 Password Dujaw: {DUJAW_PASSWORD} (fijo)")
    print(f"🔐 Password Pokémon: {password_pokemon}")
    
    try:
        # 1. Acceder al buzón
        print(f"\n1️⃣ Accediendo al buzón...")
        api = DujawAPI(email, DUJAW_PASSWORD)
        mailbox_data = api.access_mailbox()
        
        if not mailbox_data:
            print(f"❌ No se pudo acceder al buzón")
            print(f"💡 Verifica que el email sea correcto: {email}")
            return False
        
        correos = mailbox_data.get('messages', [])
        print(f"✅ Buzón accedido - {len(correos)} correos encontrados")
        
        if not correos:
            print(f"📭 El buzón está vacío")
            print(f"💡 Espera a que lleguen correos o verifica el email")
            return False
        
        # Mostrar correos encontrados
        print(f"\n📊 CORREOS EN EL BUZÓN:")
        for i, correo in enumerate(correos, 1):
            sender = correo.get('sender_name', 'N/A')
            subject = correo.get('subject', 'N/A')
            print(f"   {i}. De: {sender}")
            print(f"      Asunto: {subject}")
        
        # 2. Buscar correos de Pokémon
        print(f"\n2️⃣ Buscando correos de Pokémon...")
        correos_pokemon = []
        links_confirmacion = []
        
        for correo in correos:
            sender = correo.get('sender_name', '').lower()
            subject = correo.get('subject', '').lower()
            content = str(correo.get('content', ''))
            
            # Verificar si es de Pokémon
            es_pokemon = (
                'pokemon' in sender or 
                'pokemon' in subject or
                'email change' in subject or
                'trainer club' in content.lower()
            )
            
            if es_pokemon:
                correos_pokemon.append(correo)
                print(f"✅ Correo de Pokémon encontrado:")
                print(f"   De: {correo.get('sender_name', 'N/A')}")
                print(f"   Asunto: {correo.get('subject', 'N/A')}")
                
                # Buscar links de confirmación
                patron = r'https://club\.pokemon\.com/[^\s<>"\']+email-change-approval/[a-f0-9]+'
                links = re.findall(patron, content)
                
                for link in links:
                    if link not in links_confirmacion:
                        links_confirmacion.append(link)
                        print(f"🔗 Link de confirmación: {link}")
        
        if not correos_pokemon:
            print(f"❌ No se encontraron correos de Pokémon")
            print(f"💡 Verifica que el correo de confirmación haya llegado")
            return False
        
        if not links_confirmacion:
            print(f"❌ No se encontraron links de confirmación")
            print(f"💡 El correo puede no contener links válidos")
            return False
        
        print(f"🎯 Encontrados {len(links_confirmacion)} links de confirmación")
        
        # 3. Confirmar automáticamente
        print(f"\n3️⃣ Confirmando cambios automáticamente...")
        confirmaciones_exitosas = 0
        
        for i, link in enumerate(links_confirmacion, 1):
            print(f"\n🔄 Procesando link {i}/{len(links_confirmacion)}...")
            print(f"🔗 {link}")
            
            # Crear sesión nueva para cada confirmación
            session = requests.Session()
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9'
            })
            
            try:
                # Acceder a la página de confirmación
                print(f"   📥 Accediendo a la página...")
                response = session.get(link)
                
                if response.status_code != 200:
                    print(f"   ❌ Error {response.status_code}")
                    continue
                
                # Buscar formulario
                soup = BeautifulSoup(response.text, 'html.parser')
                form = None
                
                for f in soup.find_all('form'):
                    if f.find('input', {'type': 'password'}):
                        form = f
                        break
                
                if not form:
                    print(f"   ❌ No se encontró formulario de confirmación")
                    continue
                
                print(f"   ✅ Formulario encontrado")
                
                # Preparar datos del formulario
                form_data = {}
                
                for input_elem in form.find_all('input'):
                    name = input_elem.get('name')
                    value = input_elem.get('value', '')
                    input_type = input_elem.get('type', 'text')
                    
                    if not name:
                        continue
                    
                    if input_type == 'password':
                        form_data[name] = password_pokemon
                        print(f"   🔑 Password configurado")
                    else:
                        form_data[name] = value
                
                # Enviar confirmación
                print(f"   🚀 Enviando confirmación...")
                confirm_response = session.post(link, data=form_data, allow_redirects=True)
                
                print(f"   📥 Status: {confirm_response.status_code}")
                print(f"   🌐 URL final: {confirm_response.url}")
                
                # Verificar resultado
                if confirm_response.url != link and confirm_response.status_code == 200:
                    print(f"   🎉 ¡CONFIRMACIÓN EXITOSA! (Redirección detectada)")
                    confirmaciones_exitosas += 1
                elif 'could not be completed' in confirm_response.text.lower():
                    print(f"   ⚠️ 'Could not be completed' - Puede ya estar procesado")
                elif confirm_response.status_code == 200:
                    print(f"   ✅ Confirmación enviada (verificar manualmente)")
                    confirmaciones_exitosas += 1
                else:
                    print(f"   ❌ Error en confirmación")
                
                # Guardar respuesta para revisión
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"confirmacion_{email.split('@')[0]}_{i}_{timestamp}.html"
                
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(f"<!-- Email: {email} -->\n")
                    f.write(f"<!-- Link: {link} -->\n")
                    f.write(f"<!-- Status: {confirm_response.status_code} -->\n")
                    f.write(confirm_response.text)
                
                print(f"   💾 Respuesta guardada: {filename}")
            
            except Exception as e:
                print(f"   ❌ Error procesando link: {e}")
            
            finally:
                session.close()
        
        # 4. Resultado final
        print(f"\n📊 RESULTADO FINAL:")
        print("=" * 40)
        print(f"📧 Email procesado: {email}")
        print(f"📊 Correos encontrados: {len(correos)}")
        print(f"🎯 Correos de Pokémon: {len(correos_pokemon)}")
        print(f"🔗 Links de confirmación: {len(links_confirmacion)}")
        print(f"✅ Confirmaciones procesadas: {confirmaciones_exitosas}")
        
        if confirmaciones_exitosas > 0:
            print(f"\n🎉 ¡PROCESO COMPLETADO EXITOSAMENTE!")
            print(f"✅ {confirmaciones_exitosas} confirmación(es) enviada(s)")
            print(f"💡 Verifica en pokemon.com que el cambio se aplicó")
            return True
        else:
            print(f"\n⚠️ No se completaron confirmaciones exitosas")
            print(f"💡 Revisa los archivos guardados para más detalles")
            return False
    
    except Exception as e:
        print(f"❌ Error general: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Función principal"""
    # Verificar argumentos
    if len(sys.argv) == 1 or (len(sys.argv) == 2 and sys.argv[1] in ['-h', '--help', 'help']):
        mostrar_ayuda()
        return
    
    if len(sys.argv) != 3:
        print("❌ ERROR: Número incorrecto de parámetros")
        print()
        mostrar_ayuda()
        sys.exit(1)
    
    # Obtener parámetros
    email = sys.argv[1]
    password_pokemon = sys.argv[2]
    
    # Validar email
    if '@' not in email or not email.endswith('.trade'):
        print(f"❌ ERROR: Email inválido: {email}")
        print(f"💡 Debe ser un email de Dujaw (ej: <EMAIL>)")
        sys.exit(1)
    
    # Ejecutar confirmación
    success = confirmar_pokemon_automatico(email, password_pokemon)
    
    if success:
        print(f"\n✅ Proceso completado exitosamente")
        sys.exit(0)
    else:
        print(f"\n❌ Proceso no completado")
        sys.exit(1)

if __name__ == "__main__":
    main()
