{"timestamp": "20250624_035024", "html_length": 6236, "livewire_components": {}, "scripts_count": 2, "relevant_scripts": 1, "email_elements": 1, "forms": 1, "buttons": 3, "links": 0, "interesting_containers": 0, "relevant_meta": 1, "sample_data": {"email_elements": [{"text": "[wire\\:loading], [wire\\:loading\\.delay], [wire\\:loading\\.inline-block], [wire\\:loading\\.inline], [wire\\:loading\\.block], [wire\\:loading\\.flex], [wire\\:loading\\.table], [wire\\:loading\\.grid], [wire\\:loading\\.inline-flex] {display: none;}[wire\\:loading\\.delay\\.shortest], [wire\\:loading\\.delay\\.shorter], [wire\\:loading\\.delay\\.short], [wire\\:loading\\.delay\\.long], [wire\\:loading\\.delay\\.longer], [wire\\:loading\\.delay\\.longest] {display:none;}[wire\\:offline] {display: none;}[wire\\:dirty]:not(textarea):not(input):not(select) {display: none;}input:-webkit-autofill, select:-webkit-autofill, textarea:-webkit-autofill {animation-duration: 50000s;animation-name: livewireautofill;}@keyframes livewireautofill { from {} }", "tag": "style", "classes": [], "id": ""}], "interesting_containers": [], "relevant_meta": [{"name": "csrf-token", "content": "WgpLQc2pATdiQbPgxjVmjiylwa89Oe9oEw1Hm0P2"}], "script_samples": {"script_1": "\n  let captcha_name = \"off\";\n  let site_key = \"\";\n  if(captcha_name && captcha_name !== \"off\") {\n    site_key = \"\";\n  }\n  let strings = {\"Get back to MailBox\":\"Get back to MailBox\",\"Enter Username\":\"Enter Username\",\"Select Domain\":\"Select Domain\",\"Create\":\"Create\",\"Random\":\"Random\",\"Custom\":\"Custom\",\"Menu\":\"Menu\",\"Cancel\":\"Cancel\",\"Copy\":\"Copy\",\"Refresh\":\"Refresh\",\"New\":\"New\",\"Delete\":\"Delete\",\"Download\":\"Download\",\"Fetching\":\"Fetching\",\"Empty Inbox\":\"Empty Inbox\",\"Error\":\"Error\",\"Success\":\"Succ"}}}