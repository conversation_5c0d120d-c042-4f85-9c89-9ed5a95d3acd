#!/usr/bin/env python3
"""
FastAPI Web Interface for Dujaw API
Modern web interface for managing and monitoring the Dujaw mailbox
"""

from fastapi import FastAP<PERSON>, HTTPException, BackgroundTasks
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jin<PERSON>2Templates
from fastapi.requests import Request
from fastapi.responses import HTMLResponse, JSONResponse
from pydantic import BaseModel
from typing import Dict, Any, Optional, List
import json
import asyncio
from datetime import datetime
import uvicorn
import os

# Import our Dujaw API
from dujaw_api_final import DujawAPI

# Create FastAPI app
app = FastAPI(
    title="Dujaw Mailbox Manager",
    description="Modern web interface for Dujaw.com mailbox automation",
    version="1.0.0"
)

# Setup templates and static files
templates = Jinja2Templates(directory="templates")
app.mount("/static", StaticFiles(directory="static"), name="static")

# Global API instance
dujaw_api = DujawAPI()

# Pydantic models for API requests
class UnlockRequest(BaseModel):
    password: Optional[str] = None

class MessageRequest(BaseModel):
    message_id: str

class ComposeRequest(BaseModel):
    to: str
    subject: str
    body: str

# In-memory storage for real-time updates
mailbox_status = {
    "last_update": None,
    "message_count": 0,
    "is_authenticated": False,
    "messages": [],
    "actions": []
}

@app.get("/", response_class=HTMLResponse)
async def dashboard(request: Request):
    """Main dashboard page"""
    return templates.TemplateResponse("dashboard.html", {
        "request": request,
        "title": "Dujaw Mailbox Manager"
    })

@app.get("/api/status")
async def get_status():
    """Get current mailbox status"""
    try:
        status = dujaw_api.get_mailbox_status()
        mailbox_status.update(status)
        mailbox_status["last_update"] = datetime.now().isoformat()
        return JSONResponse(content=status)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/unlock")
async def unlock_mailbox(request: UnlockRequest):
    """Unlock the mailbox"""
    try:
        # Use provided password or default
        if request.password:
            dujaw_api.password = request.password
            
        success = dujaw_api.unlock_mailbox()
        
        if success:
            # Update status after successful unlock
            status = dujaw_api.get_mailbox_status()
            mailbox_status.update(status)
            return JSONResponse(content={
                "success": True,
                "message": "Mailbox unlocked successfully",
                "status": status
            })
        else:
            return JSONResponse(
                status_code=400,
                content={"success": False, "message": "Failed to unlock mailbox"}
            )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/mailbox")
async def get_mailbox():
    """Get mailbox content"""
    try:
        mailbox_info = dujaw_api.access_mailbox()
        
        if mailbox_info:
            mailbox_status.update(mailbox_info)
            mailbox_status["last_update"] = datetime.now().isoformat()
            return JSONResponse(content=mailbox_info)
        else:
            return JSONResponse(
                status_code=400,
                content={"error": "Failed to access mailbox"}
            )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/refresh")
async def refresh_mailbox():
    """Refresh mailbox content"""
    try:
        mailbox_info = dujaw_api.refresh_mailbox()
        
        if mailbox_info:
            mailbox_status.update(mailbox_info)
            mailbox_status["last_update"] = datetime.now().isoformat()
            return JSONResponse(content={
                "success": True,
                "message": "Mailbox refreshed successfully",
                "data": mailbox_info
            })
        else:
            return JSONResponse(
                status_code=400,
                content={"success": False, "message": "Failed to refresh mailbox"}
            )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/message/{message_id}")
async def get_message_details(message_id: str):
    """Get details of a specific message"""
    try:
        message_details = dujaw_api.get_message_details(message_id)
        
        if message_details:
            return JSONResponse(content=message_details)
        else:
            return JSONResponse(
                status_code=404,
                content={"error": "Message not found"}
            )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/message/{message_id}")
async def delete_message(message_id: str):
    """Delete a specific message"""
    try:
        success = dujaw_api.delete_message(message_id)
        
        if success:
            return JSONResponse(content={
                "success": True,
                "message": f"Message {message_id} deleted successfully"
            })
        else:
            return JSONResponse(
                status_code=400,
                content={"success": False, "message": "Failed to delete message"}
            )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/compose")
async def compose_message(request: ComposeRequest):
    """Compose and send a new message"""
    try:
        success = dujaw_api.compose_message(
            to=request.to,
            subject=request.subject,
            body=request.body
        )
        
        if success:
            return JSONResponse(content={
                "success": True,
                "message": "Message sent successfully"
            })
        else:
            return JSONResponse(
                status_code=400,
                content={"success": False, "message": "Failed to send message"}
            )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/logout")
async def logout():
    """Logout from the mailbox"""
    try:
        success = dujaw_api.logout()
        
        if success:
            # Reset status
            mailbox_status.update({
                "last_update": datetime.now().isoformat(),
                "message_count": 0,
                "is_authenticated": False,
                "messages": [],
                "actions": []
            })
            
            return JSONResponse(content={
                "success": True,
                "message": "Logged out successfully"
            })
        else:
            return JSONResponse(
                status_code=400,
                content={"success": False, "message": "Failed to logout"}
            )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Background task for periodic monitoring
async def monitor_mailbox():
    """Background task to monitor mailbox for new messages"""
    while True:
        try:
            if dujaw_api.is_authenticated:
                mailbox_info = dujaw_api.refresh_mailbox()
                if mailbox_info:
                    mailbox_status.update(mailbox_info)
                    mailbox_status["last_update"] = datetime.now().isoformat()
        except Exception as e:
            print(f"Error in background monitoring: {e}")
        
        # Wait 30 seconds before next check
        await asyncio.sleep(30)

@app.on_event("startup")
async def startup_event():
    """Start background monitoring on app startup"""
    asyncio.create_task(monitor_mailbox())

if __name__ == "__main__":
    # Create directories if they don't exist
    os.makedirs("templates", exist_ok=True)
    os.makedirs("static/css", exist_ok=True)
    os.makedirs("static/js", exist_ok=True)
    
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
