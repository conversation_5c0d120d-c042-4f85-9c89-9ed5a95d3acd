#!/usr/bin/env python3
"""
Script simple para confirmar cambios de email de Pokémon
Solo cambia el EMAIL y PASSWORD_POKEMON abajo
"""

# =============================================================================
# 🔧 SOLO EDITA ESTAS DOS LÍNEAS
# =============================================================================

# El email temporal que quieres monitorear
EMAIL = "<EMAIL>"

# El password de la cuenta de Pokémon (para confirmar el cambio)
PASSWORD_POKEMON = "EMVaB#6G3"

# =============================================================================
# 🚀 NO TOQUES NADA ABAJO - EL SCRIPT HACE TODO AUTOMÁTICO
# =============================================================================

import requests
import json
import re
from datetime import datetime
from bs4 import BeautifulSoup
from dujaw_api_final import DujawAPI

# Password fijo para todos los buzones de Dujaw
DUJAW_PASSWORD = "unlockgs2024"

def main():
    """Función principal - Proceso completamente automático"""
    print("🚀 CONFIRMADOR AUTOMÁTICO DE POKÉMON")
    print("=" * 60)
    print(f"📧 Buzón a monitorear: {EMAIL}")
    print(f"🔑 Password Dujaw: {DUJAW_PASSWORD} (fijo)")
    print(f"🔐 Password Pokémon: {PASSWORD_POKEMON}")
    
    try:
        # 1. Acceder al buzón
        print(f"\n1️⃣ Accediendo al buzón...")
        api = DujawAPI(EMAIL, DUJAW_PASSWORD)
        mailbox_data = api.access_mailbox()
        
        if not mailbox_data:
            print(f"❌ No se pudo acceder al buzón")
            print(f"💡 Verifica que el email sea correcto: {EMAIL}")
            return
        
        correos = mailbox_data.get('messages', [])
        print(f"✅ Buzón accedido - {len(correos)} correos encontrados")
        
        if not correos:
            print(f"📭 El buzón está vacío")
            print(f"💡 Espera a que lleguen correos o verifica el email")
            return
        
        # Mostrar correos encontrados
        print(f"\n📊 CORREOS EN EL BUZÓN:")
        for i, correo in enumerate(correos, 1):
            sender = correo.get('sender_name', 'N/A')
            subject = correo.get('subject', 'N/A')
            print(f"   {i}. De: {sender}")
            print(f"      Asunto: {subject}")
        
        # 2. Buscar correos de Pokémon
        print(f"\n2️⃣ Buscando correos de Pokémon...")
        correos_pokemon = []
        links_confirmacion = []
        
        for correo in correos:
            sender = correo.get('sender_name', '').lower()
            subject = correo.get('subject', '').lower()
            content = str(correo.get('content', ''))
            
            # Verificar si es de Pokémon
            es_pokemon = (
                'pokemon' in sender or 
                'pokemon' in subject or
                'email change' in subject or
                'trainer club' in content.lower()
            )
            
            if es_pokemon:
                correos_pokemon.append(correo)
                print(f"✅ Correo de Pokémon encontrado:")
                print(f"   De: {correo.get('sender_name', 'N/A')}")
                print(f"   Asunto: {correo.get('subject', 'N/A')}")
                
                # Buscar links de confirmación
                patron = r'https://club\.pokemon\.com/[^\s<>"\']+email-change-approval/[a-f0-9]+'
                links = re.findall(patron, content)
                
                for link in links:
                    if link not in links_confirmacion:
                        links_confirmacion.append(link)
                        print(f"🔗 Link de confirmación: {link}")
        
        if not correos_pokemon:
            print(f"❌ No se encontraron correos de Pokémon")
            print(f"💡 Verifica que el correo de confirmación haya llegado")
            return
        
        if not links_confirmacion:
            print(f"❌ No se encontraron links de confirmación")
            print(f"💡 El correo puede no contener links válidos")
            return
        
        print(f"🎯 Encontrados {len(links_confirmacion)} links de confirmación")
        
        # 3. Confirmar automáticamente
        print(f"\n3️⃣ Confirmando cambios automáticamente...")
        confirmaciones_exitosas = 0
        
        for i, link in enumerate(links_confirmacion, 1):
            print(f"\n🔄 Procesando link {i}/{len(links_confirmacion)}...")
            print(f"🔗 {link}")
            
            # Crear sesión nueva para cada confirmación
            session = requests.Session()
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9'
            })
            
            try:
                # Acceder a la página de confirmación
                print(f"   📥 Accediendo a la página...")
                response = session.get(link)
                
                if response.status_code != 200:
                    print(f"   ❌ Error {response.status_code}")
                    continue
                
                # Buscar formulario
                soup = BeautifulSoup(response.text, 'html.parser')
                form = None
                
                for f in soup.find_all('form'):
                    if f.find('input', {'type': 'password'}):
                        form = f
                        break
                
                if not form:
                    print(f"   ❌ No se encontró formulario de confirmación")
                    continue
                
                print(f"   ✅ Formulario encontrado")
                
                # Preparar datos del formulario
                form_data = {}
                
                for input_elem in form.find_all('input'):
                    name = input_elem.get('name')
                    value = input_elem.get('value', '')
                    input_type = input_elem.get('type', 'text')
                    
                    if not name:
                        continue
                    
                    if input_type == 'password':
                        form_data[name] = PASSWORD_POKEMON
                        print(f"   🔑 Password configurado")
                    else:
                        form_data[name] = value
                
                # Enviar confirmación
                print(f"   🚀 Enviando confirmación...")
                confirm_response = session.post(link, data=form_data, allow_redirects=True)
                
                print(f"   📥 Status: {confirm_response.status_code}")
                print(f"   🌐 URL final: {confirm_response.url}")
                
                # Verificar resultado
                if confirm_response.url != link and confirm_response.status_code == 200:
                    print(f"   🎉 ¡CONFIRMACIÓN EXITOSA! (Redirección detectada)")
                    confirmaciones_exitosas += 1
                elif 'could not be completed' in confirm_response.text.lower():
                    print(f"   ⚠️ 'Could not be completed' - Puede ya estar procesado")
                elif confirm_response.status_code == 200:
                    print(f"   ✅ Confirmación enviada (verificar manualmente)")
                    confirmaciones_exitosas += 1
                else:
                    print(f"   ❌ Error en confirmación")
                
                # Guardar respuesta para revisión
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"confirmacion_{i}_{timestamp}.html"
                
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(f"<!-- Link: {link} -->\n")
                    f.write(f"<!-- Status: {confirm_response.status_code} -->\n")
                    f.write(confirm_response.text)
                
                print(f"   💾 Respuesta guardada: {filename}")
            
            except Exception as e:
                print(f"   ❌ Error procesando link: {e}")
            
            finally:
                session.close()
        
        # 4. Resultado final
        print(f"\n📊 RESULTADO FINAL:")
        print("=" * 40)
        print(f"📧 Email procesado: {EMAIL}")
        print(f"📊 Correos encontrados: {len(correos)}")
        print(f"🎯 Correos de Pokémon: {len(correos_pokemon)}")
        print(f"🔗 Links de confirmación: {len(links_confirmacion)}")
        print(f"✅ Confirmaciones procesadas: {confirmaciones_exitosas}")
        
        if confirmaciones_exitosas > 0:
            print(f"\n🎉 ¡PROCESO COMPLETADO EXITOSAMENTE!")
            print(f"✅ {confirmaciones_exitosas} confirmación(es) enviada(s)")
            print(f"💡 Verifica en pokemon.com que el cambio se aplicó")
        else:
            print(f"\n⚠️ No se completaron confirmaciones exitosas")
            print(f"💡 Revisa los archivos guardados para más detalles")
    
    except Exception as e:
        print(f"❌ Error general: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n🏁 Proceso completado")

if __name__ == "__main__":
    main()
